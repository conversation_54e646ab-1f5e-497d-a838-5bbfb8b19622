# 🚀 كيفية تشغيل VS Code Control Center

## ⚡ التشغيل السريع

### 🎯 **الطريقة الأسهل:**
```
انقر مرتين على: quick_start.bat
```

### 🚀 **الواجهة المتقدمة (Task Manager):**
```
انقر مرتين على: start_pro.bat
```

### 🎨 **الواجهة الموحدة:**
```
انقر مرتين على: start.bat
```

---

## 🔧 إذا لم تعمل

### 1️⃣ **تأكد من Python:**
```bash
python --version
```

### 2️⃣ **ثبت المكتبات:**
```bash
pip install psutil requests
```

### 3️⃣ **شغل مباشرة:**
```bash
# الواجهة المتقدمة
python vscode_control_center_pro.py

# الواجهة الموحدة  
python vscode_control_center.py
```

---

## 🎯 ما ستراه

### 📊 **الواجهة المتقدمة:**
- Task Manager حقيقي مع جميع العمليات
- تحكم كامل في العمليات (إيقاف/تشغيل/إنهاء)
- محادثة تفاعلية مع النظام
- بحث وفلترة للعمليات
- مراقبة الشبكة والأمان

### 🎨 **الواجهة الموحدة:**
- إحصائيات سريعة للنظام
- مراقبة VS Code
- وكلاء ذكيين للتحليل
- محادثة مع AI

---

## 💡 لحل مشكلة الذاكرة العالية (89%)

### 🚀 **استخدم الواجهة المتقدمة:**
1. شغل `start_pro.bat`
2. ابحث عن العمليات الثقيلة
3. انقر يمين → إنهاء العملية
4. اكتب في المحادثة: "حلل الذاكرة"
5. اتبع التوصيات الذكية

---

## ✅ التطبيق يعمل الآن!

**الوكلاء الذكيين تم تهيئتهم بنجاح ✅**
