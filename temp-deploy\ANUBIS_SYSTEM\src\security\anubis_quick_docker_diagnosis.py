#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 تشخيص سريع لمشكلة Docker - أنوبيس
Quick Docker Diagnosis - Anubis
"""

import subprocess
import json

def run_command(cmd, description):
    """تشغيل أمر وعرض النتيجة"""
    print(f"\n🔍 {description}:")
    print("-" * 40)
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print(result.stdout)
        else:
            print(f"❌ خطأ: {result.stderr}")
    except Exception as e:
        print(f"❌ خطأ في التشغيل: {e}")

def main():
    print("🏺 تشخيص سريع لمشكلة Docker")
    print("=" * 50)
    
    # فحص حالة الحاويات
    run_command(['docker', 'ps', '-a'], "حالة جميع الحاويات")
    
    # فحص سجلات API
    run_command(['docker', 'logs', 'anubis-api-isolated', '--tail', '20'], "آخر 20 سطر من سجلات API")
    
    # فحص الشبكة
    run_command(['docker', 'network', 'ls'], "الشبكات المتاحة")
    
    # فحص الأحجام
    run_command(['docker', 'volume', 'ls'], "الأحجام المتاحة")
    
    # فحص استخدام الموارد
    run_command(['docker', 'stats', '--no-stream'], "استخدام الموارد")

if __name__ == "__main__":
    main()
