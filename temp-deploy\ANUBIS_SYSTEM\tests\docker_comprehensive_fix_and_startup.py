#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🐳 نظام إصلاح وتشغيل شامل لحاويات Docker - Universal AI Assistants
Comprehensive Docker Fix and Startup System for Universal AI Assistants
"""

import subprocess
import json
import time
import sys
from datetime import datetime
from pathlib import Path

class DockerComprehensiveFixer:
    def __init__(self):
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.containers_status = {}
        self.errors_found = []
        self.fixes_applied = []
        self.startup_order = [
            # قواعد البيانات أولاً
            "anubis-mysql",
            "anubis-database-isolated", 
            "anubis-redis-isolated",
            # ثم الخدمات الأساسية
            "anubis-simple",
            "anubis-api-isolated",
            # ثم المعالجات والمراقبة
            "anubis-worker-isolated",
            "anubis-monitor-isolated"
        ]
        
    def run_command(self, command, timeout=30):
        """تشغيل أمر مع معالجة الأخطاء"""
        try:
            print(f"🔧 تشغيل: {command}")
            result = subprocess.run(
                command.split() if isinstance(command, str) else command,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            
            if result.returncode == 0:
                print(f"✅ نجح: {command}")
                return True, result.stdout.strip()
            else:
                print(f"❌ فشل: {command}")
                print(f"   خطأ: {result.stderr.strip()}")
                self.errors_found.append({
                    "command": command,
                    "error": result.stderr.strip(),
                    "timestamp": datetime.now().isoformat()
                })
                return False, result.stderr.strip()
                
        except subprocess.TimeoutExpired:
            error_msg = f"انتهت مهلة الأمر: {command}"
            print(f"⏰ {error_msg}")
            self.errors_found.append({
                "command": command,
                "error": error_msg,
                "timestamp": datetime.now().isoformat()
            })
            return False, error_msg
            
        except Exception as e:
            error_msg = f"خطأ في تشغيل الأمر: {str(e)}"
            print(f"💥 {error_msg}")
            self.errors_found.append({
                "command": command,
                "error": error_msg,
                "timestamp": datetime.now().isoformat()
            })
            return False, error_msg
    
    def check_docker_status(self):
        """فحص حالة Docker"""
        print("🐳 فحص حالة Docker...")
        print("="*80)
        
        # فحص إصدار Docker
        success, output = self.run_command("docker --version")
        if success:
            print(f"✅ Docker متاح: {output}")
        else:
            print("❌ Docker غير متاح!")
            return False
        
        # فحص Docker Daemon
        success, output = self.run_command("docker info")
        if success:
            print("✅ Docker Daemon يعمل بشكل طبيعي")
        else:
            print("❌ مشكلة في Docker Daemon!")
            return False
            
        return True
    
    def analyze_containers(self):
        """تحليل حالة جميع الحاويات"""
        print("\n🔍 تحليل حالة الحاويات...")
        print("="*80)
        
        # الحصول على قائمة الحاويات
        success, output = self.run_command("docker ps -a --format json")
        if not success:
            return False
        
        containers_found = 0
        anubis_containers = 0
        
        for line in output.split('\n'):
            if line.strip():
                try:
                    container = json.loads(line)
                    name = container.get('Names', '')
                    image = container.get('Image', '')
                    status = container.get('Status', '')
                    state = container.get('State', '')
                    
                    containers_found += 1
                    
                    if 'anubis' in name.lower() or 'universal-ai-assistants' in image.lower():
                        anubis_containers += 1
                        self.containers_status[name] = {
                            'image': image,
                            'status': status,
                            'state': state,
                            'is_running': state.lower() == 'running'
                        }
                        
                        status_icon = "✅" if state.lower() == 'running' else "⚠️"
                        print(f"{status_icon} {name}: {status}")
                        
                except json.JSONDecodeError:
                    continue
        
        print(f"\n📊 إجمالي الحاويات: {containers_found}")
        print(f"🏺 حاويات أنوبيس: {anubis_containers}")
        
        return True
    
    def fix_container_issues(self):
        """إصلاح مشاكل الحاويات"""
        print("\n🛠️ إصلاح مشاكل الحاويات...")
        print("="*80)
        
        fixes_count = 0
        
        # إصلاح 1: تنظيف الحاويات المتوقفة
        print("🧹 تنظيف الحاويات المتوقفة...")
        success, output = self.run_command("docker container prune -f")
        if success:
            fixes_count += 1
            self.fixes_applied.append("تنظيف الحاويات المتوقفة")
        
        # إصلاح 2: تنظيف الصور غير المستخدمة
        print("🧹 تنظيف الصور غير المستخدمة...")
        success, output = self.run_command("docker image prune -f")
        if success:
            fixes_count += 1
            self.fixes_applied.append("تنظيف الصور غير المستخدمة")
        
        # إصلاح 3: تنظيف الشبكات غير المستخدمة
        print("🧹 تنظيف الشبكات غير المستخدمة...")
        success, output = self.run_command("docker network prune -f")
        if success:
            fixes_count += 1
            self.fixes_applied.append("تنظيف الشبكات غير المستخدمة")
        
        # إصلاح 4: تنظيف الأحجام غير المستخدمة
        print("🧹 تنظيف الأحجام غير المستخدمة...")
        success, output = self.run_command("docker volume prune -f")
        if success:
            fixes_count += 1
            self.fixes_applied.append("تنظيف الأحجام غير المستخدمة")
        
        print(f"✅ تم تطبيق {fixes_count} إصلاح")
        return fixes_count > 0
    
    def start_containers_sequentially(self):
        """تشغيل الحاويات بالتتابع حسب الأولوية"""
        print("\n🚀 تشغيل الحاويات بالتتابع...")
        print("="*80)
        
        started_containers = 0
        failed_containers = 0
        
        for container_name in self.startup_order:
            print(f"\n🔄 محاولة تشغيل: {container_name}")
            
            # محاولة تشغيل الحاوية
            success, output = self.run_command(f"docker start {container_name}")
            
            if success:
                print(f"✅ تم تشغيل: {container_name}")
                started_containers += 1
                
                # انتظار قصير للتأكد من التشغيل
                time.sleep(3)
                
                # فحص حالة الحاوية
                success, status = self.run_command(f"docker ps --filter name={container_name} --format json")
                if success and status.strip():
                    print(f"✅ {container_name} يعمل بشكل طبيعي")
                else:
                    print(f"⚠️ {container_name} قد يواجه مشاكل")
                    
            else:
                print(f"❌ فشل تشغيل: {container_name}")
                failed_containers += 1
                
                # محاولة فحص السجلات للتشخيص
                success, logs = self.run_command(f"docker logs {container_name} --tail 5")
                if success and logs.strip():
                    print(f"📋 آخر سجلات {container_name}:")
                    for log_line in logs.split('\n')[-3:]:
                        if log_line.strip():
                            print(f"   {log_line}")
        
        print(f"\n📊 نتائج التشغيل:")
        print(f"✅ حاويات تم تشغيلها: {started_containers}")
        print(f"❌ حاويات فشل تشغيلها: {failed_containers}")
        
        return started_containers, failed_containers
    
    def verify_system_readiness(self):
        """التحقق من جاهزية النظام"""
        print("\n🎯 التحقق من جاهزية النظام...")
        print("="*80)
        
        readiness_score = 0
        total_checks = 6
        
        # فحص 1: حالة الحاويات
        success, output = self.run_command("docker ps --filter name=anubis --format json")
        running_containers = len([line for line in output.split('\n') if line.strip()])
        if running_containers >= 4:
            print("✅ عدد كافٍ من الحاويات يعمل")
            readiness_score += 1
        else:
            print(f"⚠️ عدد قليل من الحاويات يعمل: {running_containers}")
        
        # فحص 2: حالة قواعد البيانات
        db_containers = ["anubis-mysql", "anubis-database-isolated", "anubis-redis-isolated"]
        db_running = 0
        for db in db_containers:
            success, output = self.run_command(f"docker ps --filter name={db} --format json")
            if success and output.strip():
                db_running += 1
        
        if db_running >= 2:
            print(f"✅ قواعد البيانات تعمل: {db_running}/3")
            readiness_score += 1
        else:
            print(f"⚠️ قواعد بيانات قليلة تعمل: {db_running}/3")
        
        # فحص 3: حالة API
        success, output = self.run_command("docker ps --filter name=anubis-api --format json")
        if success and output.strip():
            print("✅ واجهة API تعمل")
            readiness_score += 1
        else:
            print("⚠️ واجهة API لا تعمل")
        
        # فحص 4: حالة النظام الأساسي
        success, output = self.run_command("docker ps --filter name=anubis-simple --format json")
        if success and output.strip():
            print("✅ النظام الأساسي يعمل")
            readiness_score += 1
        else:
            print("⚠️ النظام الأساسي لا يعمل")
        
        # فحص 5: حالة المراقبة
        success, output = self.run_command("docker ps --filter name=anubis-monitor --format json")
        if success and output.strip():
            print("✅ نظام المراقبة يعمل")
            readiness_score += 1
        else:
            print("⚠️ نظام المراقبة لا يعمل")
        
        # فحص 6: حالة N8N
        success, output = self.run_command("docker ps --filter name=anubis-n8n --format json")
        if success and output.strip():
            print("✅ نظام الأتمتة (N8N) يعمل")
            readiness_score += 1
        else:
            print("⚠️ نظام الأتمتة لا يعمل")
        
        # حساب النتيجة النهائية
        readiness_percentage = (readiness_score / total_checks) * 100
        
        print(f"\n📊 نتيجة الجاهزية: {readiness_score}/{total_checks} ({readiness_percentage:.1f}%)")
        
        if readiness_percentage >= 80:
            print("🏆 النظام جاهز للاستخدام الفوري!")
        elif readiness_percentage >= 60:
            print("⚠️ النظام جاهز جزئياً - يحتاج تحسينات")
        else:
            print("❌ النظام غير جاهز - يحتاج إصلاحات")
        
        return readiness_percentage
    
    def generate_final_report(self, readiness_score):
        """إنشاء التقرير النهائي"""
        print("\n📄 إنشاء التقرير النهائي...")
        
        report = {
            "timestamp": self.timestamp,
            "readiness_score": readiness_score,
            "containers_status": self.containers_status,
            "errors_found": self.errors_found,
            "fixes_applied": self.fixes_applied,
            "startup_order": self.startup_order
        }
        
        report_filename = f"docker_comprehensive_fix_report_{self.timestamp}.json"
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"📄 تم حفظ التقرير: {report_filename}")
        return report_filename
    
    def run_comprehensive_fix(self):
        """تشغيل الإصلاح الشامل"""
        print("🚀 بدء الإصلاح الشامل لحاويات Docker")
        print("="*80)
        
        # الخطوة 1: فحص Docker
        if not self.check_docker_status():
            print("❌ فشل في فحص Docker!")
            return False
        
        # الخطوة 2: تحليل الحاويات
        if not self.analyze_containers():
            print("❌ فشل في تحليل الحاويات!")
            return False
        
        # الخطوة 3: إصلاح المشاكل
        self.fix_container_issues()
        
        # الخطوة 4: تشغيل الحاويات
        started, failed = self.start_containers_sequentially()
        
        # الخطوة 5: التحقق من الجاهزية
        readiness_score = self.verify_system_readiness()
        
        # الخطوة 6: إنشاء التقرير
        report_file = self.generate_final_report(readiness_score)
        
        # النتيجة النهائية
        print("\n" + "="*80)
        print("🎉 اكتمل الإصلاح الشامل!")
        print("="*80)
        print(f"📊 نتيجة الجاهزية: {readiness_score:.1f}%")
        print(f"✅ حاويات تم تشغيلها: {started}")
        print(f"❌ حاويات فشل تشغيلها: {failed}")
        print(f"🔧 إصلاحات مطبقة: {len(self.fixes_applied)}")
        print(f"⚠️ أخطاء مكتشفة: {len(self.errors_found)}")
        print(f"📄 التقرير: {report_file}")
        
        return readiness_score >= 70

def main():
    """الدالة الرئيسية"""
    print("🐳 مرحباً بك في نظام الإصلاح الشامل لحاويات Docker")
    print("🔧 سيتم إصلاح وتشغيل جميع حاويات Universal AI Assistants...")
    
    fixer = DockerComprehensiveFixer()
    
    try:
        success = fixer.run_comprehensive_fix()
        
        if success:
            print("\n🎉 تم الإصلاح والتشغيل بنجاح!")
            print("🚀 النظام جاهز للاستخدام الفوري!")
        else:
            print("\n⚠️ تم الإصلاح جزئياً - قد تحتاج تدخل يدوي")
        
        return success
        
    except Exception as e:
        print(f"\n💥 خطأ في الإصلاح الشامل: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
