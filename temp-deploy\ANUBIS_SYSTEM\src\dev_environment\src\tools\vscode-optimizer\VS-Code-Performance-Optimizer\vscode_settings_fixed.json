{"editor.semanticHighlighting.enabled": false, "editor.bracketPairColorization.enabled": false, "editor.renderWhitespace": "none", "editor.minimap.enabled": false, "editor.codeLens": false, "editor.lightbulb.enabled": false, "editor.hover.enabled": false, "editor.parameterHints.enabled": false, "editor.quickSuggestions": false, "editor.suggestOnTriggerCharacters": false, "editor.wordBasedSuggestions": false, "editor.acceptSuggestionOnEnter": "off", "editor.acceptSuggestionOnCommitCharacter": false, "editor.formatOnSave": false, "editor.formatOnType": false, "editor.formatOnPaste": false, "editor.autoIndent": "none", "editor.trimAutoWhitespace": false, "editor.maxTokenizationLineLength": 1000, "editor.largeFileOptimizations": true, "files.watcherExclude": {"**/node_modules/**": true, "**/.git/**": true, "**/dist/**": true, "**/build/**": true, "**/out/**": true, "**/.vscode/**": true, "**/coverage/**": true, "**/.nyc_output/**": true, "**/tmp/**": true, "**/temp/**": true}, "search.exclude": {"**/node_modules": true, "**/bower_components": true, "**/.git": true, "**/dist": true, "**/build": true, "**/coverage": true, "**/.nyc_output": true}, "files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, "**/node_modules": true, "**/bower_components": true}, "typescript.disableAutomaticTypeAcquisition": true, "typescript.suggest.autoImports": false, "typescript.updateImportsOnFileMove.enabled": "never", "javascript.suggest.autoImports": false, "javascript.updateImportsOnFileMove.enabled": "never", "extensions.autoUpdate": false, "extensions.autoCheckUpdates": false, "extensions.ignoreRecommendations": true, "update.mode": "none", "telemetry.telemetryLevel": "off", "workbench.enableExperiments": false, "workbench.settings.enableNaturalLanguageSearch": false, "workbench.startupEditor": "none", "workbench.tips.enabled": false, "workbench.welcome.enabled": false, "git.enabled": true, "git.autorefresh": false, "git.autofetch": false, "git.decorations.enabled": false, "breadcrumbs.enabled": false, "outline.showVariables": false, "outline.showFunctions": false, "problems.decorations.enabled": false, "http.proxyStrictSSL": false, "security.workspace.trust.enabled": false, "python.analysis.autoImportCompletions": false, "python.analysis.autoSearchPaths": false, "python.analysis.diagnosticMode": "openFilesOnly", "python.linting.enabled": false, "omnisharp.enableEditorConfigSupport": false, "omnisharp.enableImportCompletion": false, "omnisharp.enableRoslynAnalyzers": false, "window.titleBarStyle": "custom", "disable-hardware-acceleration": true}