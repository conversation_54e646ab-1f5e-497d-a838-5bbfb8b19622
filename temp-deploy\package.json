{"name": "universal-ai-assistants", "version": "2.0.0", "description": "🏺 Universal AI Assistants - Advanced AI Platform with Genkit Integration", "main": "genkit-server.js", "scripts": {"start": "node genkit-server.js", "dev": "genkit start", "build": "genkit build", "deploy": "genkit deploy", "test": "jest", "genkit:init": "genkit init", "genkit:serve": "genkit start --port 4000"}, "dependencies": {"@genkit-ai/ai": "^0.5.0", "@genkit-ai/core": "^0.5.0", "@genkit-ai/flow": "^0.5.0", "@genkit-ai/googleai": "^0.5.0", "@genkit-ai/vertexai": "^0.5.0", "@genkit-ai/firebase": "^0.5.0", "express": "^4.19.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "dotenv": "^16.4.5", "winston": "^3.13.0", "prometheus-client": "^15.1.3"}, "devDependencies": {"@types/node": "^20.14.9", "jest": "^29.7.0", "nodemon": "^3.1.4", "typescript": "^5.5.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["ai", "genkit", "google-ai", "chatbot", "machine-learning", "artificial-intelligence", "anubis", "horus", "mcp"], "author": "Universal AI Assistants Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/amrashour1/universal-ai-assistants.git"}, "bugs": {"url": "https://github.com/amrashour1/universal-ai-assistants/issues"}, "homepage": "https://universal-ai-assistants-554716410816.us-central1.run.app"}