# -*- coding: utf-8 -*-
"""
🚀 VS Code Control Center Pro - Task Manager متقدم
=================================================

واجهة متقدمة مع:
✨ عرض العمليات مثل Task Manager
💬 محادثة تفاعلية مع النظام
🎛️ تحكم كامل في العمليات
🤖 وكلاء ذكيين متقدمين
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import time
import json
import psutil
import subprocess
import os
import signal
from datetime import datetime
import sys

# إضافة مسار الوكلاء
sys.path.append(os.path.join(os.path.dirname(__file__), 'agents'))

try:
    from agents.agent_coordinator import AgentCoordinator
    AGENTS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ تحذير: سيعمل التطبيق بوضع أساسي - {e}")
    AGENTS_AVAILABLE = False

class VSCodeControlCenterPro:
    """VS Code Control Center Pro - Task Manager متقدم"""

    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()

        # نظام الوكلاء
        self.agent_coordinator = None
        self.agents_running = False

        # متغيرات البيانات
        self.processes_data = []
        self.selected_process = None
        self.auto_refresh = True
        self.chat_history = []

        # إنشاء الواجهة
        self.create_widgets()

        # تهيئة نظام الوكلاء
        if AGENTS_AVAILABLE:
            self.initialize_agents()

        # بدء التحديث التلقائي
        self.start_auto_refresh()

    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("🚀 VS Code Control Center Pro - Task Manager متقدم")
        self.root.geometry("1600x1000")
        self.root.configure(bg='#0d1117')
        self.root.state('zoomed')  # ملء الشاشة

        # أيقونة النافذة
        try:
            self.root.iconbitmap('icon.ico')
        except:
            pass

    def create_widgets(self):
        """إنشاء عناصر الواجهة المتقدمة"""
        # إطار رئيسي
        main_frame = tk.Frame(self.root, bg='#0d1117')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # العنوان الرئيسي
        self.create_header(main_frame)

        # إطار المحتوى الرئيسي
        content_frame = tk.Frame(main_frame, bg='#0d1117')
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

        # الجانب الأيسر - العمليات والتحكم
        left_frame = tk.Frame(content_frame, bg='#0d1117')
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        # الجانب الأيمن - المحادثة والتحليل
        right_frame = tk.Frame(content_frame, bg='#0d1117')
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))

        # إنشاء الأقسام
        self.create_stats_bar(left_frame)
        self.create_processes_section(left_frame)
        self.create_control_panel(left_frame)

        self.create_chat_section(right_frame)
        self.create_analysis_section(right_frame)

    def create_header(self, parent):
        """إنشاء العنوان الرئيسي"""
        header_frame = tk.Frame(parent, bg='#0d1117')
        header_frame.pack(fill=tk.X, pady=(0, 15))

        # العنوان
        title_label = tk.Label(
            header_frame,
            text="🚀 VS Code Control Center Pro",
            font=('Segoe UI', 32, 'bold'),
            fg='#58a6ff',
            bg='#0d1117'
        )
        title_label.pack()

        # العنوان الفرعي
        subtitle_text = "Task Manager متقدم مع وكلاء ذكيين ومحادثة تفاعلية"

        subtitle_label = tk.Label(
            header_frame,
            text=subtitle_text,
            font=('Segoe UI', 14),
            fg='#8b949e',
            bg='#0d1117'
        )
        subtitle_label.pack()

        # شريط الحالة العامة
        status_frame = tk.Frame(header_frame, bg='#0d1117')
        status_frame.pack(pady=(10, 0))

        self.overall_status_label = tk.Label(
            status_frame,
            text="🔄 جاري تحليل النظام...",
            font=('Segoe UI', 16, 'bold'),
            fg='#ffa657',
            bg='#0d1117'
        )
        self.overall_status_label.pack()

    def create_stats_bar(self, parent):
        """إنشاء شريط الإحصائيات السريعة"""
        stats_frame = tk.Frame(parent, bg='#161b22', relief='raised', bd=2)
        stats_frame.pack(fill=tk.X, pady=(0, 10))

        # إطار الإحصائيات
        stats_inner = tk.Frame(stats_frame, bg='#161b22')
        stats_inner.pack(fill=tk.X, padx=15, pady=10)

        # الإحصائيات
        self.stats_labels = {}

        stats_data = [
            ("🖥️ العمليات", "processes", "0"),
            ("💾 الذاكرة", "memory", "0%"),
            ("⚡ المعالج", "cpu", "0%"),
            ("🧩 VS Code", "vscode", "0"),
            ("🌐 الشبكة", "network", "0 KB/s"),
            ("💿 القرص", "disk", "0%")
        ]

        for i, (title, key, default) in enumerate(stats_data):
            stat_frame = tk.Frame(stats_inner, bg='#21262d', relief='raised', bd=1)
            stat_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=2)

            tk.Label(
                stat_frame,
                text=title,
                font=('Segoe UI', 10, 'bold'),
                fg='#8b949e',
                bg='#21262d'
            ).pack(pady=(5, 0))

            value_label = tk.Label(
                stat_frame,
                text=default,
                font=('Segoe UI', 14, 'bold'),
                fg='#58a6ff',
                bg='#21262d'
            )
            value_label.pack(pady=(0, 5))

            self.stats_labels[key] = value_label

    def create_processes_section(self, parent):
        """إنشاء قسم العمليات مثل Task Manager"""
        processes_frame = tk.LabelFrame(
            parent,
            text="📊 العمليات النشطة - Task Manager",
            font=('Segoe UI', 14, 'bold'),
            fg='#58a6ff',
            bg='#161b22',
            bd=2,
            relief='groove'
        )
        processes_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # شريط البحث والفلترة
        search_frame = tk.Frame(processes_frame, bg='#161b22')
        search_frame.pack(fill=tk.X, padx=10, pady=(10, 5))

        tk.Label(
            search_frame,
            text="🔍 بحث:",
            font=('Segoe UI', 11, 'bold'),
            fg='#f0f6fc',
            bg='#161b22'
        ).pack(side=tk.LEFT)

        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.filter_processes)

        search_entry = tk.Entry(
            search_frame,
            textvariable=self.search_var,
            font=('Segoe UI', 11),
            bg='#21262d',
            fg='#f0f6fc',
            insertbackground='#58a6ff',
            relief='flat',
            bd=5,
            width=20
        )
        search_entry.pack(side=tk.LEFT, padx=(5, 15))

        # فلتر نوع العمليات
        tk.Label(
            search_frame,
            text="📂 فلتر:",
            font=('Segoe UI', 11, 'bold'),
            fg='#f0f6fc',
            bg='#161b22'
        ).pack(side=tk.LEFT)

        self.filter_var = tk.StringVar(value="الكل")
        filter_combo = ttk.Combobox(
            search_frame,
            textvariable=self.filter_var,
            values=["الكل", "VS Code", "عالي الاستهلاك", "مشبوه", "نظام"],
            state="readonly",
            width=15
        )
        filter_combo.pack(side=tk.LEFT, padx=(5, 15))
        filter_combo.bind('<<ComboboxSelected>>', self.filter_processes)

        # زر التحديث
        refresh_btn = tk.Button(
            search_frame,
            text="🔄 تحديث",
            command=self.refresh_processes,
            font=('Segoe UI', 10, 'bold'),
            fg='white',
            bg='#238636',
            activebackground='#2ea043',
            relief='flat',
            bd=0,
            padx=15,
            pady=5,
            cursor='hand2'
        )
        refresh_btn.pack(side=tk.RIGHT)

        # جدول العمليات
        table_frame = tk.Frame(processes_frame, bg='#161b22')
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

        # إنشاء Treeview للعمليات
        columns = ('PID', 'الاسم', 'المعالج %', 'الذاكرة %', 'الحالة', 'المستخدم')

        self.processes_tree = ttk.Treeview(
            table_frame,
            columns=columns,
            show='headings',
            height=15
        )

        # تكوين الأعمدة
        column_widths = {'PID': 80, 'الاسم': 200, 'المعالج %': 100, 'الذاكرة %': 100, 'الحالة': 100, 'المستخدم': 120}

        for col in columns:
            self.processes_tree.heading(col, text=col, command=lambda c=col: self.sort_processes(c))
            self.processes_tree.column(col, width=column_widths.get(col, 100), anchor='center')

        # شريط التمرير
        scrollbar_v = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.processes_tree.yview)
        scrollbar_h = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.processes_tree.xview)

        self.processes_tree.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)

        # تخطيط الجدول
        self.processes_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_v.pack(side=tk.RIGHT, fill=tk.Y)
        scrollbar_h.pack(side=tk.BOTTOM, fill=tk.X)

        # ربط الأحداث
        self.processes_tree.bind('<Button-3>', self.show_process_menu)  # قائمة السياق
        self.processes_tree.bind('<Double-1>', self.process_details)    # تفاصيل العملية
        self.processes_tree.bind('<<TreeviewSelect>>', self.on_process_select)

    def create_control_panel(self, parent):
        """إنشاء لوحة التحكم"""
        control_frame = tk.LabelFrame(
            parent,
            text="🎛️ لوحة التحكم المتقدمة",
            font=('Segoe UI', 14, 'bold'),
            fg='#58a6ff',
            bg='#161b22',
            bd=2,
            relief='groove'
        )
        control_frame.pack(fill=tk.X)

        # إطار الأزرار
        buttons_frame = tk.Frame(control_frame, bg='#161b22')
        buttons_frame.pack(fill=tk.X, padx=15, pady=15)

        # الصف الأول من الأزرار
        row1_frame = tk.Frame(buttons_frame, bg='#161b22')
        row1_frame.pack(fill=tk.X, pady=(0, 10))

        buttons_row1 = [
            ("🚫 إنهاء العملية", self.kill_selected_process, '#da3633'),
            ("⏸️ إيقاف مؤقت", self.suspend_process, '#fb8500'),
            ("▶️ استئناف", self.resume_process, '#238636'),
            ("📊 تفاصيل", self.process_details, '#1f6feb')
        ]

        for text, command, color in buttons_row1:
            self.create_control_button(row1_frame, text, command, color)

        # الصف الثاني من الأزرار
        row2_frame = tk.Frame(buttons_frame, bg='#161b22')
        row2_frame.pack(fill=tk.X, pady=(0, 10))

        buttons_row2 = [
            ("🧹 تنظيف الذاكرة", self.cleanup_memory, '#8957e5'),
            ("🔄 إعادة تشغيل VS Code", self.restart_vscode, '#fd7e14'),
            ("🛡️ فحص أمني", self.security_scan, '#6f42c1'),
            ("💾 حفظ التقرير", self.save_detailed_report, '#0969da')
        ]

        for text, command, color in buttons_row2:
            self.create_control_button(row2_frame, text, command, color)

        # مفاتيح التحكم
        switches_frame = tk.Frame(control_frame, bg='#161b22')
        switches_frame.pack(fill=tk.X, padx=15, pady=(0, 15))

        self.auto_refresh_var = tk.BooleanVar(value=True)
        auto_refresh_check = tk.Checkbutton(
            switches_frame,
            text="🔄 تحديث تلقائي (كل 2 ثانية)",
            variable=self.auto_refresh_var,
            command=self.toggle_auto_refresh,
            font=('Segoe UI', 11, 'bold'),
            fg='#f0f6fc',
            bg='#161b22',
            selectcolor='#21262d',
            activebackground='#161b22',
            activeforeground='#58a6ff'
        )
        auto_refresh_check.pack(side=tk.LEFT)

        if AGENTS_AVAILABLE:
            self.ai_analysis_var = tk.BooleanVar()
            ai_analysis_check = tk.Checkbutton(
                switches_frame,
                text="🤖 تحليل ذكي مستمر",
                variable=self.ai_analysis_var,
                command=self.toggle_ai_analysis,
                font=('Segoe UI', 11, 'bold'),
                fg='#f0f6fc',
                bg='#161b22',
                selectcolor='#21262d',
                activebackground='#161b22',
                activeforeground='#58a6ff'
            )
            ai_analysis_check.pack(side=tk.RIGHT)

    def create_control_button(self, parent, text, command, color):
        """إنشاء زر تحكم"""
        button = tk.Button(
            parent,
            text=text,
            command=command,
            font=('Segoe UI', 10, 'bold'),
            fg='white',
            bg=color,
            activebackground=color,
            activeforeground='white',
            relief='flat',
            bd=0,
            padx=15,
            pady=8,
            cursor='hand2'
        )
        button.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        # تأثير hover
        def on_enter(e):
            button.configure(bg=self.lighten_color(color))

        def on_leave(e):
            button.configure(bg=color)

        button.bind("<Enter>", on_enter)
        button.bind("<Leave>", on_leave)

    def create_chat_section(self, parent):
        """إنشاء قسم المحادثة التفاعلية"""
        chat_frame = tk.LabelFrame(
            parent,
            text="💬 محادثة تفاعلية مع النظام",
            font=('Segoe UI', 14, 'bold'),
            fg='#58a6ff',
            bg='#161b22',
            bd=2,
            relief='groove'
        )
        chat_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # منطقة المحادثة
        self.chat_text = scrolledtext.ScrolledText(
            chat_frame,
            height=12,
            font=('Segoe UI', 11),
            bg='#0d1117',
            fg='#f0f6fc',
            insertbackground='#58a6ff',
            selectbackground='#264f78',
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        self.chat_text.pack(fill=tk.BOTH, expand=True, padx=15, pady=(15, 10))

        # إطار الإدخال
        input_frame = tk.Frame(chat_frame, bg='#161b22')
        input_frame.pack(fill=tk.X, padx=15, pady=(0, 15))

        # حقل الإدخال
        self.chat_input = tk.Entry(
            input_frame,
            font=('Segoe UI', 12),
            bg='#21262d',
            fg='#f0f6fc',
            insertbackground='#58a6ff',
            relief='flat',
            bd=8
        )
        self.chat_input.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        self.chat_input.bind('<Return>', self.send_chat_message)

        # أزرار سريعة
        quick_buttons_frame = tk.Frame(input_frame, bg='#161b22')
        quick_buttons_frame.pack(side=tk.RIGHT)

        quick_commands = [
            ("📤 إرسال", self.send_chat_message, '#238636'),
            ("🔍 تحليل", self.analyze_system, '#1f6feb'),
            ("🧹 تنظيف", self.quick_cleanup, '#fb8500')
        ]

        for text, command, color in quick_commands:
            btn = tk.Button(
                quick_buttons_frame,
                text=text,
                command=command,
                font=('Segoe UI', 10, 'bold'),
                fg='white',
                bg=color,
                relief='flat',
                bd=0,
                padx=12,
                pady=6,
                cursor='hand2'
            )
            btn.pack(side=tk.LEFT, padx=2)

        # رسائل ترحيبية
        self.add_chat_message("🤖 النظام", "مرحباً! أنا مساعدك الذكي لإدارة VS Code والنظام")
        self.add_chat_message("💡 نصيحة", "اكتب أوامر مثل: 'حلل النظام' أو 'أغلق العمليات الثقيلة' أو 'ما حالة VS Code؟'")

    def create_analysis_section(self, parent):
        """إنشاء قسم التحليل المتقدم"""
        analysis_frame = tk.LabelFrame(
            parent,
            text="📊 تحليل متقدم ومراقبة الشبكة",
            font=('Segoe UI', 14, 'bold'),
            fg='#58a6ff',
            bg='#161b22',
            bd=2,
            relief='groove'
        )
        analysis_frame.pack(fill=tk.BOTH, expand=True)

        # تبويبات التحليل
        notebook = ttk.Notebook(analysis_frame)
        notebook.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # تبويب التحليل العام
        general_frame = tk.Frame(notebook, bg='#0d1117')
        notebook.add(general_frame, text="📊 تحليل عام")

        self.analysis_text = scrolledtext.ScrolledText(
            general_frame,
            height=8,
            font=('Consolas', 10),
            bg='#0d1117',
            fg='#f0f6fc',
            insertbackground='#58a6ff',
            selectbackground='#264f78',
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        self.analysis_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # تبويب مراقبة الشبكة
        network_frame = tk.Frame(notebook, bg='#0d1117')
        notebook.add(network_frame, text="🌐 مراقبة الشبكة")

        self.network_text = scrolledtext.ScrolledText(
            network_frame,
            height=8,
            font=('Consolas', 10),
            bg='#0d1117',
            fg='#f0f6fc',
            insertbackground='#58a6ff',
            selectbackground='#264f78',
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        self.network_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # تبويب الأمان
        security_frame = tk.Frame(notebook, bg='#0d1117')
        notebook.add(security_frame, text="🛡️ الأمان")

        self.security_text = scrolledtext.ScrolledText(
            security_frame,
            height=8,
            font=('Consolas', 10),
            bg='#0d1117',
            fg='#f0f6fc',
            insertbackground='#58a6ff',
            selectbackground='#264f78',
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        self.security_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    def initialize_agents(self):
        """تهيئة نظام الوكلاء"""
        try:
            self.agent_coordinator = AgentCoordinator()
            self.agent_coordinator.start_all_agents()
            self.agents_running = True

            self.add_chat_message("🤖 النظام", "✅ تم تهيئة 6 وكلاء ذكيين بنجاح!")
            self.log_analysis("✅ نظام الوكلاء الذكيين جاهز للعمل")

        except Exception as e:
            self.add_chat_message("🤖 النظام", f"❌ خطأ في تهيئة الوكلاء: {e}")
            self.log_analysis(f"❌ خطأ في تهيئة الوكلاء: {e}")

    def start_auto_refresh(self):
        """بدء التحديث التلقائي"""
        self.refresh_all_data()
        if self.auto_refresh:
            self.root.after(2000, self.start_auto_refresh)  # كل 2 ثانية

    def refresh_all_data(self):
        """تحديث جميع البيانات"""
        try:
            self.update_stats()
            self.refresh_processes()
            self.update_network_info()

        except Exception as e:
            self.log_analysis(f"❌ خطأ في تحديث البيانات: {e}")

    def update_stats(self):
        """تحديث الإحصائيات السريعة"""
        try:
            # عدد العمليات
            process_count = len(psutil.pids())
            self.stats_labels['processes'].configure(text=str(process_count))

            # الذاكرة
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            self.stats_labels['memory'].configure(
                text=f"{memory_percent:.1f}%",
                fg=self.get_status_color(memory_percent, 70, 85)
            )

            # المعالج
            cpu_percent = psutil.cpu_percent(interval=0.1)
            self.stats_labels['cpu'].configure(
                text=f"{cpu_percent:.1f}%",
                fg=self.get_status_color(cpu_percent, 60, 80)
            )

            # VS Code
            vscode_count = 0
            vscode_memory = 0
            for proc in psutil.process_iter(['name', 'memory_percent']):
                try:
                    if 'code' in proc.info['name'].lower():
                        vscode_count += 1
                        vscode_memory += proc.info['memory_percent']
                except (psutil.NoSuchProcess, psutil.AccessDenied,
                        psutil.ZombieProcess, KeyboardInterrupt):
                    continue
                except Exception:
                    continue

            self.stats_labels['vscode'].configure(
                text=str(vscode_count),
                fg=self.get_status_color(vscode_count, 5, 10)
            )

            # الشبكة
            try:
                net_io = psutil.net_io_counters()
                net_speed = (net_io.bytes_sent + net_io.bytes_recv) / 1024  # KB
                self.stats_labels['network'].configure(text=f"{net_speed:.0f} KB/s")
            except:
                self.stats_labels['network'].configure(text="N/A")

            # القرص
            try:
                disk = psutil.disk_usage('/')
                disk_percent = disk.percent
                self.stats_labels['disk'].configure(
                    text=f"{disk_percent:.1f}%",
                    fg=self.get_status_color(disk_percent, 80, 90)
                )
            except:
                self.stats_labels['disk'].configure(text="N/A")

            # تحديث الحالة العامة
            self.update_overall_status(memory_percent, cpu_percent, vscode_count)

        except Exception as e:
            self.log_analysis(f"❌ خطأ في تحديث الإحصائيات: {e}")

    def get_status_color(self, value, warning_threshold, critical_threshold):
        """الحصول على لون الحالة حسب القيمة"""
        if value >= critical_threshold:
            return '#f85149'  # أحمر
        elif value >= warning_threshold:
            return '#ffa657'  # برتقالي
        else:
            return '#3fb950'  # أخضر

    def refresh_processes(self):
        """تحديث قائمة العمليات"""
        try:
            # مسح البيانات القديمة
            for item in self.processes_tree.get_children():
                self.processes_tree.delete(item)

            self.processes_data = []

            # جمع بيانات العمليات
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent', 'status', 'username']):
                try:
                    pinfo = proc.info
                    pinfo['cpu_percent'] = proc.cpu_percent()
                    pinfo['memory_percent'] = proc.memory_percent()

                    # تطبيق الفلتر
                    if self.should_show_process(pinfo):
                        self.processes_data.append(pinfo)

                except (psutil.NoSuchProcess, psutil.AccessDenied,
                        psutil.ZombieProcess, KeyboardInterrupt):
                    continue
                except Exception:
                    continue

            # ترتيب العمليات حسب استهلاك المعالج
            self.processes_data.sort(key=lambda x: x.get('cpu_percent', 0), reverse=True)

            # إضافة العمليات إلى الجدول
            for pinfo in self.processes_data:
                self.insert_process_to_tree(pinfo)

        except Exception as e:
            self.log_analysis(f"❌ خطأ في تحديث العمليات: {e}")

    def should_show_process(self, pinfo):
        """تحديد ما إذا كان يجب عرض العملية حسب الفلتر"""
        filter_value = self.filter_var.get()
        search_term = self.search_var.get().lower()

        # فلتر البحث
        if search_term and search_term not in pinfo.get('name', '').lower():
            return False

        # فلتر النوع
        if filter_value == "VS Code":
            return 'code' in pinfo.get('name', '').lower()
        elif filter_value == "عالي الاستهلاك":
            return pinfo.get('cpu_percent', 0) > 10 or pinfo.get('memory_percent', 0) > 5
        elif filter_value == "مشبوه":
            return pinfo.get('cpu_percent', 0) > 50 or pinfo.get('status') == 'zombie'
        elif filter_value == "نظام":
            system_processes = ['system', 'kernel', 'svchost', 'dwm', 'explorer', 'winlogon']
            return any(sys_proc in pinfo.get('name', '').lower() for sys_proc in system_processes)

        return True  # الكل

    def insert_process_to_tree(self, pinfo):
        """إدراج عملية في الجدول"""
        try:
            pid = pinfo.get('pid', 'N/A')
            name = pinfo.get('name', 'Unknown')
            cpu = f"{pinfo.get('cpu_percent', 0):.1f}%"
            memory = f"{pinfo.get('memory_percent', 0):.1f}%"
            status = pinfo.get('status', 'unknown')
            username = pinfo.get('username', 'N/A')

            # تحديد لون الصف حسب الاستهلاك
            tags = []
            if pinfo.get('cpu_percent', 0) > 50:
                tags.append('high_cpu')
            elif pinfo.get('memory_percent', 0) > 20:
                tags.append('high_memory')
            elif 'code' in name.lower():
                tags.append('vscode')

            item = self.processes_tree.insert('', 'end', values=(pid, name, cpu, memory, status, username), tags=tags)

        except Exception as e:
            self.log_analysis(f"❌ خطأ في إدراج العملية: {e}")

    def filter_processes(self, *args):
        """فلترة العمليات"""
        self.refresh_processes()

    def sort_processes(self, column):
        """ترتيب العمليات حسب العمود"""
        try:
            # الحصول على البيانات الحالية
            data = [(self.processes_tree.set(item, column), item) for item in self.processes_tree.get_children('')]

            # ترتيب البيانات
            if column in ['PID', 'المعالج %', 'الذاكرة %']:
                # ترتيب رقمي
                data.sort(key=lambda x: float(x[0].replace('%', '')) if x[0] != 'N/A' else 0, reverse=True)
            else:
                # ترتيب أبجدي
                data.sort(key=lambda x: x[0])

            # إعادة ترتيب العناصر
            for index, (val, item) in enumerate(data):
                self.processes_tree.move(item, '', index)

        except Exception as e:
            self.log_analysis(f"❌ خطأ في ترتيب العمليات: {e}")

    def on_process_select(self, event):
        """عند تحديد عملية"""
        selection = self.processes_tree.selection()
        if selection:
            item = selection[0]
            values = self.processes_tree.item(item, 'values')
            if values:
                self.selected_process = {
                    'pid': values[0],
                    'name': values[1],
                    'cpu': values[2],
                    'memory': values[3],
                    'status': values[4],
                    'username': values[5]
                }

    def show_process_menu(self, event):
        """عرض قائمة السياق للعملية"""
        if not self.selected_process:
            return

        context_menu = tk.Menu(self.root, tearoff=0, bg='#21262d', fg='#f0f6fc')
        context_menu.add_command(label="📊 تفاصيل العملية", command=self.process_details)
        context_menu.add_command(label="🚫 إنهاء العملية", command=self.kill_selected_process)
        context_menu.add_command(label="⏸️ إيقاف مؤقت", command=self.suspend_process)
        context_menu.add_command(label="▶️ استئناف", command=self.resume_process)
        context_menu.add_separator()
        context_menu.add_command(label="🔍 البحث عن العملية", command=self.search_process_info)

        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def kill_selected_process(self):
        """إنهاء العملية المحددة"""
        if not self.selected_process:
            messagebox.showwarning("تحذير", "يرجى تحديد عملية أولاً")
            return

        pid = self.selected_process['pid']
        name = self.selected_process['name']

        result = messagebox.askyesno(
            "تأكيد إنهاء العملية",
            f"هل تريد إنهاء العملية؟\n\nالاسم: {name}\nPID: {pid}\n\n⚠️ تحذير: قد يؤدي هذا إلى فقدان البيانات غير المحفوظة"
        )

        if result:
            try:
                proc = psutil.Process(int(pid))
                proc.terminate()

                self.add_chat_message("🤖 النظام", f"✅ تم إنهاء العملية: {name} (PID: {pid})")
                self.log_analysis(f"✅ تم إنهاء العملية: {name} (PID: {pid})")

                # تحديث القائمة
                self.refresh_processes()

            except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                error_msg = f"❌ فشل في إنهاء العملية: {e}"
                self.add_chat_message("🤖 النظام", error_msg)
                self.log_analysis(error_msg)
                messagebox.showerror("خطأ", error_msg)

    def suspend_process(self):
        """إيقاف العملية مؤقتاً"""
        if not self.selected_process:
            messagebox.showwarning("تحذير", "يرجى تحديد عملية أولاً")
            return

        try:
            pid = int(self.selected_process['pid'])
            proc = psutil.Process(pid)
            proc.suspend()

            name = self.selected_process['name']
            self.add_chat_message("🤖 النظام", f"⏸️ تم إيقاف العملية مؤقتاً: {name}")
            self.log_analysis(f"⏸️ تم إيقاف العملية مؤقتاً: {name} (PID: {pid})")

        except Exception as e:
            error_msg = f"❌ فشل في إيقاف العملية: {e}"
            self.add_chat_message("🤖 النظام", error_msg)
            messagebox.showerror("خطأ", error_msg)

    def resume_process(self):
        """استئناف العملية"""
        if not self.selected_process:
            messagebox.showwarning("تحذير", "يرجى تحديد عملية أولاً")
            return

        try:
            pid = int(self.selected_process['pid'])
            proc = psutil.Process(pid)
            proc.resume()

            name = self.selected_process['name']
            self.add_chat_message("🤖 النظام", f"▶️ تم استئناف العملية: {name}")
            self.log_analysis(f"▶️ تم استئناف العملية: {name} (PID: {pid})")

        except Exception as e:
            error_msg = f"❌ فشل في استئناف العملية: {e}"
            self.add_chat_message("🤖 النظام", error_msg)
            messagebox.showerror("خطأ", error_msg)

    def process_details(self):
        """عرض تفاصيل العملية"""
        if not self.selected_process:
            messagebox.showwarning("تحذير", "يرجى تحديد عملية أولاً")
            return

        try:
            pid = int(self.selected_process['pid'])
            proc = psutil.Process(pid)

            # جمع تفاصيل العملية
            details = f"""
📊 تفاصيل العملية:

🆔 PID: {pid}
📝 الاسم: {proc.name()}
📂 المسار: {proc.exe() if proc.exe() else 'غير متاح'}
👤 المستخدم: {proc.username() if proc.username() else 'غير متاح'}
📅 وقت البدء: {datetime.fromtimestamp(proc.create_time()).strftime('%Y-%m-%d %H:%M:%S')}
🔄 الحالة: {proc.status()}

📊 استهلاك الموارد:
⚡ المعالج: {proc.cpu_percent():.1f}%
💾 الذاكرة: {proc.memory_percent():.1f}% ({proc.memory_info().rss / 1024 / 1024:.1f} MB)
📁 الملفات المفتوحة: {len(proc.open_files()) if proc.open_files() else 0}
🌐 الاتصالات الشبكية: {len(proc.connections()) if proc.connections() else 0}

🧵 الخيوط: {proc.num_threads()}
🔧 الأولوية: {proc.nice()}
"""

            # عرض النافذة
            details_window = tk.Toplevel(self.root)
            details_window.title(f"تفاصيل العملية - {proc.name()}")
            details_window.geometry("600x500")
            details_window.configure(bg='#0d1117')

            details_text = scrolledtext.ScrolledText(
                details_window,
                font=('Consolas', 11),
                bg='#0d1117',
                fg='#f0f6fc',
                wrap=tk.WORD
            )
            details_text.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
            details_text.insert(tk.END, details)
            details_text.configure(state=tk.DISABLED)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في الحصول على تفاصيل العملية: {e}")

    def search_process_info(self):
        """البحث عن معلومات العملية عبر الإنترنت"""
        if not self.selected_process:
            return

        process_name = self.selected_process['name']
        search_url = f"https://www.google.com/search?q={process_name}+process+what+is"

        try:
            import webbrowser
            webbrowser.open(search_url)
            self.add_chat_message("🤖 النظام", f"🔍 تم فتح البحث عن: {process_name}")
        except Exception as e:
            self.add_chat_message("🤖 النظام", f"❌ فشل في فتح البحث: {e}")

    def add_chat_message(self, sender, message):
        """إضافة رسالة إلى المحادثة"""
        self.chat_text.configure(state=tk.NORMAL)
        timestamp = datetime.now().strftime("%H:%M:%S")

        # تنسيق الرسالة
        formatted_message = f"[{timestamp}] {sender}: {message}\n\n"

        self.chat_text.insert(tk.END, formatted_message)
        self.chat_text.configure(state=tk.DISABLED)
        self.chat_text.see(tk.END)

        # حفظ في التاريخ
        self.chat_history.append({
            'timestamp': timestamp,
            'sender': sender,
            'message': message
        })

    def send_chat_message(self, event=None):
        """إرسال رسالة في المحادثة"""
        message = self.chat_input.get().strip()
        if not message:
            return

        # مسح حقل الإدخال
        self.chat_input.delete(0, tk.END)

        # إضافة رسالة المستخدم
        self.add_chat_message("👤 أنت", message)

        # معالجة الرسالة
        self.process_chat_command(message)

    def process_chat_command(self, message):
        """معالجة أوامر المحادثة"""
        message_lower = message.lower()

        if any(word in message_lower for word in ['حلل', 'تحليل', 'فحص']):
            self.analyze_system()
        elif any(word in message_lower for word in ['نظف', 'تنظيف', 'تنظيف']):
            self.quick_cleanup()
        elif any(word in message_lower for word in ['vs code', 'vscode', 'كود']):
            self.analyze_vscode()
        elif any(word in message_lower for word in ['ذاكرة', 'memory', 'ram']):
            self.analyze_memory()
        elif any(word in message_lower for word in ['معالج', 'cpu', 'processor']):
            self.analyze_cpu()
        elif any(word in message_lower for word in ['شبكة', 'network', 'internet']):
            self.analyze_network()
        elif any(word in message_lower for word in ['أمان', 'security', 'حماية']):
            self.security_scan()
        elif any(word in message_lower for word in ['مساعدة', 'help', 'أوامر']):
            self.show_help()
        else:
            # استخدام الوكلاء الذكيين إذا كانوا متاحين
            if AGENTS_AVAILABLE and self.agent_coordinator:
                self.ask_ai_agents(message)
            else:
                self.add_chat_message("🤖 النظام", "لم أفهم الأمر. اكتب 'مساعدة' لرؤية الأوامر المتاحة.")

    def analyze_system(self):
        """تحليل شامل للنظام"""
        self.add_chat_message("🤖 النظام", "🔍 بدء التحليل الشامل للنظام...")

        try:
            # إحصائيات أساسية
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')

            analysis = f"""
📊 تحليل النظام الشامل:

💻 المعالج: {cpu_percent:.1f}% - {'🔴 مرتفع' if cpu_percent > 80 else '🟡 متوسط' if cpu_percent > 60 else '🟢 طبيعي'}
💾 الذاكرة: {memory.percent:.1f}% - {'🔴 مرتفع' if memory.percent > 85 else '🟡 متوسط' if memory.percent > 70 else '🟢 طبيعي'}
💿 القرص: {disk.percent:.1f}% - {'🔴 ممتلئ' if disk.percent > 90 else '🟡 يحتاج تنظيف' if disk.percent > 80 else '🟢 مساحة جيدة'}

🖥️ العمليات: {len(psutil.pids())} عملية نشطة
"""

            # تحليل VS Code
            vscode_procs = [p for p in psutil.process_iter(['name']) if 'code' in p.info['name'].lower()]
            if vscode_procs:
                analysis += f"🧩 VS Code: {len(vscode_procs)} عملية نشطة\n"
            else:
                analysis += "🧩 VS Code: غير مفتوح\n"

            # توصيات
            recommendations = []
            if cpu_percent > 80:
                recommendations.append("⚡ إغلاق العمليات عالية استهلاك المعالج")
            if memory.percent > 85:
                recommendations.append("💾 تحرير الذاكرة وإغلاق التطبيقات غير الضرورية")
            if disk.percent > 90:
                recommendations.append("💿 تنظيف القرص الصلب")
            if len(vscode_procs) > 10:
                recommendations.append("🧩 إعادة تشغيل VS Code")

            if recommendations:
                analysis += "\n💡 التوصيات:\n" + "\n".join(f"• {rec}" for rec in recommendations)
            else:
                analysis += "\n✅ النظام يعمل بكفاءة جيدة!"

            self.add_chat_message("🤖 النظام", analysis)
            self.log_analysis(f"تم إجراء تحليل شامل - المعالج: {cpu_percent:.1f}%, الذاكرة: {memory.percent:.1f}%")

            # تشغيل الوكلاء الذكيين إذا كانوا متاحين
            if AGENTS_AVAILABLE and self.agent_coordinator:
                threading.Thread(target=self.run_ai_analysis, daemon=True).start()

        except Exception as e:
            self.add_chat_message("🤖 النظام", f"❌ خطأ في التحليل: {e}")

    def quick_cleanup(self):
        """تنظيف سريع للنظام"""
        self.add_chat_message("🤖 النظام", "🧹 بدء التنظيف السريع...")

        try:
            cleaned_items = []

            # تنظيف الذاكرة
            import gc
            gc.collect()
            cleaned_items.append("✅ تم تنظيف الذاكرة")

            # إغلاق العمليات المعلقة
            zombie_count = 0
            for proc in psutil.process_iter(['status']):
                try:
                    if proc.info['status'] == psutil.STATUS_ZOMBIE:
                        proc.kill()
                        zombie_count += 1
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            if zombie_count > 0:
                cleaned_items.append(f"✅ تم إغلاق {zombie_count} عملية معلقة")

            # تقرير التنظيف
            if cleaned_items:
                result = "🧹 تم التنظيف بنجاح:\n" + "\n".join(cleaned_items)
            else:
                result = "✅ النظام نظيف بالفعل!"

            self.add_chat_message("🤖 النظام", result)
            self.log_analysis("تم إجراء تنظيف سريع للنظام")

        except Exception as e:
            self.add_chat_message("🤖 النظام", f"❌ خطأ في التنظيف: {e}")

    def analyze_vscode(self):
        """تحليل VS Code بالتفصيل"""
        self.add_chat_message("🤖 النظام", "🧩 تحليل VS Code...")

        try:
            vscode_procs = []
            total_cpu = 0
            total_memory = 0

            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
                try:
                    if 'code' in proc.info['name'].lower():
                        cpu = proc.cpu_percent()
                        memory = proc.memory_percent()

                        vscode_procs.append({
                            'pid': proc.info['pid'],
                            'name': proc.info['name'],
                            'cpu': cpu,
                            'memory': memory
                        })

                        total_cpu += cpu
                        total_memory += memory

                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            if not vscode_procs:
                self.add_chat_message("🤖 النظام", "🧩 VS Code غير مفتوح حالياً")
                return

            analysis = f"""
🧩 تحليل VS Code:

📊 الإحصائيات:
• العمليات: {len(vscode_procs)}
• استهلاك المعالج: {total_cpu:.1f}%
• استهلاك الذاكرة: {total_memory:.1f}%

📋 العمليات النشطة:
"""

            for proc in vscode_procs[:5]:  # أول 5 عمليات
                analysis += f"• {proc['name']} (PID: {proc['pid']}) - CPU: {proc['cpu']:.1f}%, Memory: {proc['memory']:.1f}%\n"

            # تقييم الحالة
            if total_cpu > 50 or total_memory > 30:
                analysis += "\n🔴 تحذير: استهلاك عالي! يُنصح بإعادة تشغيل VS Code"
            elif total_cpu > 25 or total_memory > 15:
                analysis += "\n🟡 استهلاك متوسط - مراقبة مطلوبة"
            else:
                analysis += "\n🟢 VS Code يعمل بكفاءة جيدة"

            self.add_chat_message("🤖 النظام", analysis)

        except Exception as e:
            self.add_chat_message("🤖 النظام", f"❌ خطأ في تحليل VS Code: {e}")

    def analyze_memory(self):
        """تحليل الذاكرة"""
        memory = psutil.virtual_memory()

        analysis = f"""
💾 تحليل الذاكرة:

📊 الإحصائيات:
• المستخدم: {memory.percent:.1f}% ({memory.used / 1024**3:.1f} GB)
• المتاح: {memory.available / 1024**3:.1f} GB
• الإجمالي: {memory.total / 1024**3:.1f} GB

{'🔴 تحذير: استهلاك عالي جداً!' if memory.percent > 90 else '🟡 استهلاك مرتفع' if memory.percent > 75 else '🟢 استهلاك طبيعي'}
"""

        self.add_chat_message("🤖 النظام", analysis)

    def analyze_cpu(self):
        """تحليل المعالج"""
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()

        analysis = f"""
⚡ تحليل المعالج:

📊 الإحصائيات:
• الاستخدام: {cpu_percent:.1f}%
• عدد الأنوية: {cpu_count}
• التردد: {psutil.cpu_freq().current:.0f} MHz

{'🔴 تحذير: حمولة عالية جداً!' if cpu_percent > 90 else '🟡 حمولة مرتفعة' if cpu_percent > 70 else '🟢 أداء طبيعي'}
"""

        self.add_chat_message("🤖 النظام", analysis)

    def analyze_network(self):
        """تحليل الشبكة"""
        try:
            net_io = psutil.net_io_counters()
            connections = psutil.net_connections()

            analysis = f"""
🌐 تحليل الشبكة:

📊 الإحصائيات:
• البيانات المرسلة: {net_io.bytes_sent / 1024**2:.1f} MB
• البيانات المستقبلة: {net_io.bytes_recv / 1024**2:.1f} MB
• الاتصالات النشطة: {len([c for c in connections if c.status == 'ESTABLISHED'])}
• إجمالي الاتصالات: {len(connections)}
"""

            self.add_chat_message("🤖 النظام", analysis)
            self.update_network_info()

        except Exception as e:
            self.add_chat_message("🤖 النظام", f"❌ خطأ في تحليل الشبكة: {e}")

    def security_scan(self):
        """فحص أمني للنظام"""
        self.add_chat_message("🤖 النظام", "🛡️ بدء الفحص الأمني...")

        try:
            security_issues = []

            # فحص العمليات المشبوهة
            suspicious_count = 0
            for proc in psutil.process_iter(['name', 'cpu_percent']):
                try:
                    if proc.info['cpu_percent'] > 80:
                        suspicious_count += 1
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            if suspicious_count > 3:
                security_issues.append(f"🔴 {suspicious_count} عمليات عالية الاستهلاك")

            # فحص الاتصالات الشبكية
            try:
                connections = psutil.net_connections()
                external_connections = [c for c in connections if c.raddr and not c.raddr.ip.startswith('127.')]

                if len(external_connections) > 50:
                    security_issues.append(f"🟡 {len(external_connections)} اتصال خارجي")
            except:
                pass

            # تقرير الأمان
            if security_issues:
                report = "🛡️ تقرير الأمان:\n\n⚠️ مشاكل محتملة:\n" + "\n".join(f"• {issue}" for issue in security_issues)
            else:
                report = "🛡️ تقرير الأمان:\n\n✅ لم يتم اكتشاف مشاكل أمنية واضحة"

            self.add_chat_message("🤖 النظام", report)
            self.log_security(f"تم إجراء فحص أمني - مشاكل: {len(security_issues)}")

        except Exception as e:
            self.add_chat_message("🤖 النظام", f"❌ خطأ في الفحص الأمني: {e}")

    def show_help(self):
        """عرض المساعدة والأوامر المتاحة"""
        help_text = """
💡 الأوامر المتاحة:

🔍 أوامر التحليل:
• "حلل النظام" - تحليل شامل للنظام
• "حلل VS Code" - تحليل VS Code بالتفصيل
• "حلل الذاكرة" - تحليل استهلاك الذاكرة
• "حلل المعالج" - تحليل أداء المعالج
• "حلل الشبكة" - تحليل الاتصالات الشبكية

🧹 أوامر التنظيف:
• "نظف النظام" - تنظيف سريع
• "نظف الذاكرة" - تحرير الذاكرة

🛡️ أوامر الأمان:
• "فحص أمني" - فحص شامل للأمان

❓ أوامر أخرى:
• "مساعدة" - عرض هذه القائمة
• يمكنك أيضاً طرح أسئلة مفتوحة!
"""

        self.add_chat_message("🤖 النظام", help_text)

    def ask_ai_agents(self, question):
        """سؤال الوكلاء الذكيين"""
        if not AGENTS_AVAILABLE or not self.agent_coordinator:
            self.add_chat_message("🤖 النظام", "❌ الوكلاء الذكيين غير متاحين")
            return

        self.add_chat_message("🤖 النظام", "🔍 جاري البحث عن إجابة من الوكلاء الذكيين...")

        def ask_agents():
            try:
                responses = self.agent_coordinator.ask_ai_agents(question)

                for agent_name, response in responses.items():
                    self.root.after(0, lambda a=agent_name, r=response:
                                   self.add_chat_message(f"🤖 {a.upper()}", r))

            except Exception as e:
                self.root.after(0, lambda:
                               self.add_chat_message("🤖 النظام", f"❌ خطأ في الوكلاء: {e}"))

        threading.Thread(target=ask_agents, daemon=True).start()

    def run_ai_analysis(self):
        """تشغيل التحليل بالوكلاء الذكيين"""
        if not AGENTS_AVAILABLE or not self.agent_coordinator:
            return

        try:
            results = self.agent_coordinator.run_comprehensive_analysis()

            # عرض النتائج
            summary = results.get('summary', {})
            overall_status = summary.get('overall_status', 'غير محدد')
            overall_score = results.get('overall_score', 0)

            ai_analysis = f"""
🤖 تحليل الوكلاء الذكيين:

📊 النتيجة العامة: {overall_status}
🎯 النقاط: {overall_score}/100

💡 أهم التوصيات:
"""

            recommendations = results.get('combined_recommendations', [])
            for i, rec in enumerate(recommendations[:5], 1):
                ai_analysis += f"{i}. {rec}\n"

            self.add_chat_message("🤖 الوكلاء", ai_analysis)

        except Exception as e:
            self.add_chat_message("🤖 النظام", f"❌ خطأ في تحليل الوكلاء: {e}")

    def update_network_info(self):
        """تحديث معلومات الشبكة"""
        try:
            connections = psutil.net_connections()

            network_info = "🌐 الاتصالات الشبكية النشطة:\n\n"

            established_connections = [c for c in connections if c.status == 'ESTABLISHED']

            for conn in established_connections[:10]:  # أول 10 اتصالات
                local_addr = f"{conn.laddr.ip}:{conn.laddr.port}" if conn.laddr else "N/A"
                remote_addr = f"{conn.raddr.ip}:{conn.raddr.port}" if conn.raddr else "N/A"

                try:
                    proc = psutil.Process(conn.pid) if conn.pid else None
                    proc_name = proc.name() if proc else "Unknown"
                except:
                    proc_name = "Unknown"

                network_info += f"• {proc_name}: {local_addr} ↔ {remote_addr}\n"

            if len(established_connections) > 10:
                network_info += f"\n... و {len(established_connections) - 10} اتصالات أخرى"

            self.log_network(network_info)

        except Exception as e:
            self.log_network(f"❌ خطأ في تحديث معلومات الشبكة: {e}")

    def update_overall_status(self, memory_percent, cpu_percent, vscode_count):
        """تحديث الحالة العامة"""
        if memory_percent > 90 or cpu_percent > 90:
            status = "🚨 النظام يحتاج تدخل فوري!"
            color = '#f85149'
        elif memory_percent > 75 or cpu_percent > 75:
            status = "⚠️ النظام يحتاج مراقبة"
            color = '#ffa657'
        elif vscode_count > 15:
            status = "🔄 VS Code يحتاج إعادة تشغيل"
            color = '#ffa657'
        else:
            status = "✅ النظام يعمل بكفاءة ممتازة"
            color = '#3fb950'

        self.overall_status_label.configure(text=status, fg=color)

    def log_analysis(self, message):
        """تسجيل رسالة في قسم التحليل"""
        self.analysis_text.configure(state=tk.NORMAL)
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.analysis_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.analysis_text.configure(state=tk.DISABLED)
        self.analysis_text.see(tk.END)

    def log_network(self, message):
        """تسجيل رسالة في قسم الشبكة"""
        self.network_text.configure(state=tk.NORMAL)
        self.network_text.delete(1.0, tk.END)  # مسح المحتوى السابق
        self.network_text.insert(tk.END, message)
        self.network_text.configure(state=tk.DISABLED)

    def log_security(self, message):
        """تسجيل رسالة في قسم الأمان"""
        self.security_text.configure(state=tk.NORMAL)
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.security_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.security_text.configure(state=tk.DISABLED)
        self.security_text.see(tk.END)

    def lighten_color(self, color):
        """تفتيح لون للتأثير hover"""
        color_map = {
            '#238636': '#2ea043',
            '#1f6feb': '#388bfd',
            '#fb8500': '#fd7e14',
            '#da3633': '#f85149',
            '#8957e5': '#a475f9',
            '#0969da': '#1f6feb',
            '#6f42c1': '#8957e5',
            '#fd7e14': '#fb8500'
        }
        return color_map.get(color, color)

    def toggle_auto_refresh(self):
        """تبديل التحديث التلقائي"""
        self.auto_refresh = self.auto_refresh_var.get()

        if self.auto_refresh:
            self.add_chat_message("🤖 النظام", "🔄 تم تفعيل التحديث التلقائي")
            self.start_auto_refresh()
        else:
            self.add_chat_message("🤖 النظام", "⏹️ تم إيقاف التحديث التلقائي")

    def toggle_ai_analysis(self):
        """تبديل التحليل الذكي المستمر"""
        if not AGENTS_AVAILABLE:
            return

        ai_enabled = self.ai_analysis_var.get()

        if ai_enabled:
            self.add_chat_message("🤖 النظام", "🤖 تم تفعيل التحليل الذكي المستمر")
            self.start_continuous_ai_analysis()
        else:
            self.add_chat_message("🤖 النظام", "⏹️ تم إيقاف التحليل الذكي المستمر")

    def start_continuous_ai_analysis(self):
        """بدء التحليل الذكي المستمر"""
        if hasattr(self, 'ai_analysis_var') and self.ai_analysis_var.get():
            threading.Thread(target=self.run_ai_analysis, daemon=True).start()
            self.root.after(60000, self.start_continuous_ai_analysis)  # كل دقيقة

    def cleanup_memory(self):
        """تنظيف الذاكرة"""
        self.add_chat_message("🤖 النظام", "🧹 تنظيف الذاكرة...")

        try:
            import gc
            gc.collect()

            # محاولة تحرير ذاكرة إضافية
            before_memory = psutil.virtual_memory().percent

            # تنظيف العمليات المعلقة
            cleaned_processes = 0
            for proc in psutil.process_iter(['status']):
                try:
                    if proc.info['status'] == psutil.STATUS_ZOMBIE:
                        proc.kill()
                        cleaned_processes += 1
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            after_memory = psutil.virtual_memory().percent
            memory_freed = before_memory - after_memory

            result = f"✅ تم تنظيف الذاكرة\n"
            if memory_freed > 0:
                result += f"• تم تحرير {memory_freed:.1f}% من الذاكرة\n"
            if cleaned_processes > 0:
                result += f"• تم إغلاق {cleaned_processes} عملية معلقة"

            self.add_chat_message("🤖 النظام", result)

        except Exception as e:
            self.add_chat_message("🤖 النظام", f"❌ خطأ في تنظيف الذاكرة: {e}")

    def restart_vscode(self):
        """إعادة تشغيل VS Code"""
        result = messagebox.askyesno(
            "إعادة تشغيل VS Code",
            "هل تريد إغلاق جميع عمليات VS Code؟\n\n⚠️ تأكد من حفظ عملك أولاً!"
        )

        if result:
            try:
                killed_count = 0
                for proc in psutil.process_iter(['name']):
                    try:
                        if 'code' in proc.info['name'].lower():
                            proc.terminate()
                            killed_count += 1
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue

                self.add_chat_message("🤖 النظام", f"🔄 تم إغلاق {killed_count} عملية VS Code")
                self.log_analysis(f"تم إعادة تشغيل VS Code - أُغلقت {killed_count} عمليات")

            except Exception as e:
                self.add_chat_message("🤖 النظام", f"❌ خطأ في إعادة تشغيل VS Code: {e}")

    def save_detailed_report(self):
        """حفظ تقرير مفصل"""
        try:
            # جمع البيانات
            report_data = {
                'timestamp': datetime.now().isoformat(),
                'system_stats': {
                    'processes': len(psutil.pids()),
                    'memory_percent': psutil.virtual_memory().percent,
                    'cpu_percent': psutil.cpu_percent(),
                    'disk_percent': psutil.disk_usage('/').percent
                },
                'vscode_processes': len([p for p in psutil.process_iter(['name']) if 'code' in p.info['name'].lower()]),
                'chat_history': self.chat_history,
                'processes_data': self.processes_data[:50]  # أول 50 عملية
            }

            filename = f"vscode_control_center_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2)

            self.add_chat_message("🤖 النظام", f"💾 تم حفظ التقرير: {filename}")
            messagebox.showinfo("نجح", f"تم حفظ التقرير المفصل في:\n{filename}")

        except Exception as e:
            error_msg = f"❌ فشل في حفظ التقرير: {e}"
            self.add_chat_message("🤖 النظام", error_msg)
            messagebox.showerror("خطأ", error_msg)

    def run(self):
        """تشغيل التطبيق"""
        try:
            # رسالة ترحيبية
            self.add_chat_message("🤖 النظام", "🚀 مرحباً بك في VS Code Control Center Pro!")
            self.log_analysis("تم تشغيل VS Code Control Center Pro بنجاح")

            # بدء التطبيق
            self.root.mainloop()

        except KeyboardInterrupt:
            self.cleanup_and_exit()
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في التطبيق:\n{e}")

    def cleanup_and_exit(self):
        """تنظيف وإغلاق التطبيق"""
        if self.agent_coordinator:
            self.agent_coordinator.stop_all_agents()
        self.root.quit()

if __name__ == "__main__":
    app = VSCodeControlCenterPro()
    app.run()