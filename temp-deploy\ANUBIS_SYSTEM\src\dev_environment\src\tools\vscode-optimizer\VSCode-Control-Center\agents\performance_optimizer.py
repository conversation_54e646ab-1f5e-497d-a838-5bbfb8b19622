# -*- coding: utf-8 -*-
"""
⚡ وكيل تحسين الأداء - Performance Optimizer Agent
===============================================

وكيل ذكي متخصص في تحسين أداء VS Code والنظام
"""

import psutil
import time
import os
from typing import Dict, List, Any
from .base_agent import BaseAgent

class PerformanceOptimizerAgent(BaseAgent):
    """وكيل تحسين الأداء الذكي"""
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__("PerformanceOptimizer", config)
        self.optimization_history = []
        self.auto_optimize = config.get('auto_optimize', False) if config else False
        
    def analyze(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """تحليل الأداء وتحديد فرص التحسين"""
        try:
            analysis = {
                'timestamp': time.time(),
                'performance_metrics': self._collect_performance_metrics(),
                'optimization_opportunities': self._find_optimization_opportunities(),
                'resource_usage': self._analyze_resource_usage(),
                'vscode_performance': self._analyze_vscode_performance(),
                'system_bottlenecks': self._identify_bottlenecks(),
                'optimization_score': 0
            }
            
            # حساب نقاط التحسين
            analysis['optimization_score'] = self._calculate_optimization_score(analysis)
            
            # تطبيق التحسينات التلقائية إذا كانت مفعلة
            if self.auto_optimize:
                analysis['auto_optimizations'] = self._apply_auto_optimizations(analysis)
            
            self.save_analysis(analysis)
            return analysis
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في تحليل الأداء: {e}")
            return {'error': str(e)}
    
    def _collect_performance_metrics(self) -> Dict[str, Any]:
        """جمع مقاييس الأداء"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # عدد العمليات
            process_count = len(psutil.pids())
            
            # متوسط الحمولة (إذا كان متاحاً)
            try:
                load_avg = os.getloadavg()
            except (AttributeError, OSError):
                load_avg = [0, 0, 0]  # Windows doesn't have load average
            
            return {
                'cpu_usage': cpu_percent,
                'memory_usage': memory.percent,
                'memory_available_gb': round(memory.available / (1024**3), 2),
                'disk_usage': disk.percent,
                'disk_free_gb': round(disk.free / (1024**3), 2),
                'process_count': process_count,
                'load_average': load_avg,
                'boot_time': psutil.boot_time()
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في جمع مقاييس الأداء: {e}")
            return {}
    
    def _find_optimization_opportunities(self) -> List[Dict[str, Any]]:
        """العثور على فرص التحسين"""
        opportunities = []
        
        try:
            # تحليل العمليات عالية الاستهلاك
            high_cpu_procs = []
            high_memory_procs = []
            
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
                try:
                    pinfo = proc.info
                    cpu = proc.cpu_percent()
                    memory = proc.memory_percent()
                    
                    if cpu > 15:
                        high_cpu_procs.append({
                            'pid': pinfo['pid'],
                            'name': pinfo['name'],
                            'cpu': cpu,
                            'optimization': 'تقليل استهلاك المعالج'
                        })
                    
                    if memory > 10:
                        high_memory_procs.append({
                            'pid': pinfo['pid'],
                            'name': pinfo['name'],
                            'memory': memory,
                            'optimization': 'تحرير الذاكرة'
                        })
                        
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            if high_cpu_procs:
                opportunities.append({
                    'type': 'high_cpu_usage',
                    'description': 'عمليات عالية استهلاك المعالج',
                    'processes': high_cpu_procs[:5],
                    'impact': 'عالي',
                    'action': 'تحسين أو إغلاق العمليات'
                })
            
            if high_memory_procs:
                opportunities.append({
                    'type': 'high_memory_usage',
                    'description': 'عمليات عالية استهلاك الذاكرة',
                    'processes': high_memory_procs[:5],
                    'impact': 'عالي',
                    'action': 'تحرير الذاكرة'
                })
            
            # فرص تحسين VS Code
            vscode_opportunities = self._find_vscode_optimizations()
            opportunities.extend(vscode_opportunities)
            
        except Exception as e:
            self.logger.error(f"خطأ في العثور على فرص التحسين: {e}")
        
        return opportunities
    
    def _find_vscode_optimizations(self) -> List[Dict[str, Any]]:
        """العثور على فرص تحسين VS Code"""
        opportunities = []
        
        try:
            vscode_procs = []
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
                try:
                    if 'code' in proc.info['name'].lower():
                        vscode_procs.append({
                            'pid': proc.info['pid'],
                            'name': proc.info['name'],
                            'cpu': proc.cpu_percent(),
                            'memory': proc.memory_percent()
                        })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            if len(vscode_procs) > 5:
                opportunities.append({
                    'type': 'too_many_vscode_processes',
                    'description': f'عدد كبير من عمليات VS Code ({len(vscode_procs)})',
                    'processes': vscode_procs,
                    'impact': 'متوسط',
                    'action': 'إعادة تشغيل VS Code'
                })
            
            total_vscode_memory = sum(p['memory'] for p in vscode_procs)
            if total_vscode_memory > 25:
                opportunities.append({
                    'type': 'high_vscode_memory',
                    'description': f'استهلاك عالي للذاكرة من VS Code ({total_vscode_memory:.1f}%)',
                    'memory_usage': total_vscode_memory,
                    'impact': 'عالي',
                    'action': 'تعطيل الإضافات غير الضرورية'
                })
                
        except Exception as e:
            self.logger.error(f"خطأ في تحليل VS Code: {e}")
        
        return opportunities
    
    def _analyze_resource_usage(self) -> Dict[str, Any]:
        """تحليل استخدام الموارد"""
        try:
            cpu_count = psutil.cpu_count()
            memory_total = psutil.virtual_memory().total / (1024**3)
            
            return {
                'cpu_cores': cpu_count,
                'memory_total_gb': round(memory_total, 2),
                'cpu_utilization': psutil.cpu_percent(interval=1),
                'memory_utilization': psutil.virtual_memory().percent,
                'disk_io': self._get_disk_io_stats(),
                'network_io': self._get_network_io_stats()
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل الموارد: {e}")
            return {}
    
    def _get_disk_io_stats(self) -> Dict[str, Any]:
        """إحصائيات دخل/خرج القرص"""
        try:
            disk_io = psutil.disk_io_counters()
            if disk_io:
                return {
                    'read_bytes': disk_io.read_bytes,
                    'write_bytes': disk_io.write_bytes,
                    'read_count': disk_io.read_count,
                    'write_count': disk_io.write_count
                }
        except Exception:
            pass
        return {}
    
    def _get_network_io_stats(self) -> Dict[str, Any]:
        """إحصائيات دخل/خرج الشبكة"""
        try:
            net_io = psutil.net_io_counters()
            if net_io:
                return {
                    'bytes_sent': net_io.bytes_sent,
                    'bytes_recv': net_io.bytes_recv,
                    'packets_sent': net_io.packets_sent,
                    'packets_recv': net_io.packets_recv
                }
        except Exception:
            pass
        return {}
    
    def _analyze_vscode_performance(self) -> Dict[str, Any]:
        """تحليل أداء VS Code بالتفصيل"""
        vscode_analysis = {
            'process_count': 0,
            'total_cpu': 0,
            'total_memory': 0,
            'processes': [],
            'performance_status': 'غير محدد'
        }
        
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
                try:
                    if 'code' in proc.info['name'].lower() or 'electron' in proc.info['name'].lower():
                        cpu = proc.cpu_percent()
                        memory = proc.memory_percent()
                        
                        vscode_analysis['processes'].append({
                            'pid': proc.info['pid'],
                            'name': proc.info['name'],
                            'cpu': cpu,
                            'memory': memory
                        })
                        
                        vscode_analysis['total_cpu'] += cpu
                        vscode_analysis['total_memory'] += memory
                        vscode_analysis['process_count'] += 1
                        
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            # تحديد حالة الأداء
            if vscode_analysis['total_cpu'] > 50 or vscode_analysis['total_memory'] > 30:
                vscode_analysis['performance_status'] = '🔴 أداء ضعيف'
            elif vscode_analysis['total_cpu'] > 25 or vscode_analysis['total_memory'] > 15:
                vscode_analysis['performance_status'] = '🟡 أداء متوسط'
            else:
                vscode_analysis['performance_status'] = '🟢 أداء جيد'
                
        except Exception as e:
            self.logger.error(f"خطأ في تحليل VS Code: {e}")
        
        return vscode_analysis
    
    def _identify_bottlenecks(self) -> List[Dict[str, Any]]:
        """تحديد اختناقات الأداء"""
        bottlenecks = []
        
        try:
            # اختناق المعالج
            cpu_percent = psutil.cpu_percent(interval=1)
            if cpu_percent > 80:
                bottlenecks.append({
                    'type': 'cpu_bottleneck',
                    'description': f'اختناق في المعالج ({cpu_percent}%)',
                    'severity': 'عالي',
                    'recommendation': 'إغلاق العمليات عالية الاستهلاك'
                })
            
            # اختناق الذاكرة
            memory = psutil.virtual_memory()
            if memory.percent > 85:
                bottlenecks.append({
                    'type': 'memory_bottleneck',
                    'description': f'اختناق في الذاكرة ({memory.percent}%)',
                    'severity': 'عالي',
                    'recommendation': 'تحرير الذاكرة أو إضافة المزيد'
                })
            
            # اختناق القرص
            disk = psutil.disk_usage('/')
            if disk.percent > 90:
                bottlenecks.append({
                    'type': 'disk_bottleneck',
                    'description': f'اختناق في مساحة القرص ({disk.percent}%)',
                    'severity': 'متوسط',
                    'recommendation': 'تنظيف القرص أو إضافة مساحة'
                })
                
        except Exception as e:
            self.logger.error(f"خطأ في تحديد الاختناقات: {e}")
        
        return bottlenecks
    
    def _calculate_optimization_score(self, analysis: Dict[str, Any]) -> int:
        """حساب نقاط التحسين (من 100)"""
        score = 100
        
        try:
            # خصم نقاط حسب المشاكل
            opportunities = analysis.get('optimization_opportunities', [])
            for opp in opportunities:
                if opp.get('impact') == 'عالي':
                    score -= 20
                elif opp.get('impact') == 'متوسط':
                    score -= 10
                else:
                    score -= 5
            
            # خصم نقاط حسب الاختناقات
            bottlenecks = analysis.get('system_bottlenecks', [])
            for bottleneck in bottlenecks:
                if bottleneck.get('severity') == 'عالي':
                    score -= 25
                elif bottleneck.get('severity') == 'متوسط':
                    score -= 15
            
            # التأكد من أن النقاط لا تقل عن 0
            score = max(0, score)
            
        except Exception as e:
            self.logger.error(f"خطأ في حساب النقاط: {e}")
            score = 50  # نقاط افتراضية
        
        return score
    
    def get_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """الحصول على توصيات التحسين"""
        if 'error' in analysis:
            return ['❌ خطأ في الحصول على توصيات التحسين']
        
        recommendations = []
        
        # توصيات حسب النقاط
        score = analysis.get('optimization_score', 50)
        if score < 30:
            recommendations.append('🚨 النظام يحتاج تحسين فوري!')
        elif score < 60:
            recommendations.append('⚠️ النظام يحتاج بعض التحسينات')
        else:
            recommendations.append('✅ النظام يعمل بكفاءة جيدة')
        
        # توصيات من فرص التحسين
        opportunities = analysis.get('optimization_opportunities', [])
        for opp in opportunities:
            action = opp.get('action', 'تحسين عام')
            recommendations.append(f"💡 {action}")
        
        # توصيات من الاختناقات
        bottlenecks = analysis.get('system_bottlenecks', [])
        for bottleneck in bottlenecks:
            rec = bottleneck.get('recommendation', 'تحسين عام')
            recommendations.append(f"🔧 {rec}")
        
        return recommendations or ['لا توجد توصيات محددة']
