# -*- coding: utf-8 -*-
"""
🤖 الوكيل الأساسي - Base Agent
=============================

الفئة الأساسية لجميع الوكلاء الذكيين في النظام
"""

import time
import json
import logging
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Any, Optional

class BaseAgent(ABC):
    """الفئة الأساسية لجميع الوكلاء"""
    
    def __init__(self, name: str, config: Dict[str, Any] = None):
        self.name = name
        self.config = config or {}
        self.is_active = False
        self.last_analysis = None
        self.analysis_history = []
        self.logger = self._setup_logger()
        
    def _setup_logger(self):
        """إعداد نظام التسجيل"""
        logger = logging.getLogger(f"Agent_{self.name}")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    @abstractmethod
    def analyze(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """تحليل البيانات - يجب تنفيذها في كل وكيل"""
        pass
    
    @abstractmethod
    def get_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """الحصول على التوصيات - يجب تنفيذها في كل وكيل"""
        pass
    
    def start(self):
        """بدء تشغيل الوكيل"""
        self.is_active = True
        self.logger.info(f"🚀 تم تشغيل الوكيل: {self.name}")
    
    def stop(self):
        """إيقاف الوكيل"""
        self.is_active = False
        self.logger.info(f"⏹️ تم إيقاف الوكيل: {self.name}")
    
    def get_status(self) -> Dict[str, Any]:
        """الحصول على حالة الوكيل"""
        return {
            'name': self.name,
            'active': self.is_active,
            'last_analysis': self.last_analysis,
            'analysis_count': len(self.analysis_history),
            'uptime': self._get_uptime()
        }
    
    def _get_uptime(self) -> str:
        """حساب وقت التشغيل"""
        if not self.is_active:
            return "غير نشط"
        # يمكن تحسين هذا لاحقاً
        return "نشط"
    
    def save_analysis(self, analysis: Dict[str, Any]):
        """حفظ نتائج التحليل"""
        analysis['timestamp'] = datetime.now().isoformat()
        analysis['agent'] = self.name
        
        self.last_analysis = analysis
        self.analysis_history.append(analysis)
        
        # الاحتفاظ بآخر 100 تحليل فقط
        if len(self.analysis_history) > 100:
            self.analysis_history = self.analysis_history[-100:]
    
    def get_analysis_summary(self) -> Dict[str, Any]:
        """ملخص التحليلات"""
        if not self.analysis_history:
            return {'message': 'لا توجد تحليلات متاحة'}
        
        return {
            'total_analyses': len(self.analysis_history),
            'last_analysis_time': self.last_analysis.get('timestamp') if self.last_analysis else None,
            'agent_name': self.name,
            'status': 'نشط' if self.is_active else 'متوقف'
        }
    
    def export_data(self, format_type: str = 'json') -> str:
        """تصدير بيانات الوكيل"""
        data = {
            'agent_info': self.get_status(),
            'analysis_history': self.analysis_history,
            'config': self.config
        }
        
        if format_type.lower() == 'json':
            return json.dumps(data, ensure_ascii=False, indent=2)
        else:
            return str(data)
    
    def load_config(self, config_path: str):
        """تحميل إعدادات من ملف"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                self.config.update(json.load(f))
            self.logger.info(f"✅ تم تحميل الإعدادات من: {config_path}")
        except Exception as e:
            self.logger.error(f"❌ خطأ في تحميل الإعدادات: {e}")
    
    def __str__(self):
        return f"Agent({self.name}) - {'نشط' if self.is_active else 'متوقف'}"
    
    def __repr__(self):
        return f"<{self.__class__.__name__}(name='{self.name}', active={self.is_active})>"
