#!/bin/bash
# 🚀 سكريبت تشغيل سيرفر أنوبيس المتكامل
# Anubis Integrated Server Startup Script

set -e

# ألوان للإخراج
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# دوال المساعدة
print_header() {
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}🏺 نظام أنوبيس المتكامل${NC}"
    echo -e "${PURPLE}Anubis Integrated System${NC}"
    echo -e "${PURPLE}================================${NC}"
}

print_step() {
    echo -e "${BLUE}📋 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# فحص المتطلبات
check_requirements() {
    print_step "فحص المتطلبات الأساسية..."

    # فحص Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker غير مثبت. يرجى تثبيت Docker أولاً."
        exit 1
    fi

    # فحص Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose غير مثبت. يرجى تثبيت Docker Compose أولاً."
        exit 1
    fi

    # فحص وجود الملفات المطلوبة
    if [ ! -f "docker-compose-full-server.yml" ]; then
        print_error "ملف docker-compose-full-server.yml غير موجود."
        exit 1
    fi

    print_success "جميع المتطلبات متوفرة"
}

# إنشاء المجلدات المطلوبة
create_directories() {
    print_step "إنشاء المجلدات المطلوبة..."

    mkdir -p database
    mkdir -p nginx/ssl
    mkdir -p monitoring/grafana/dashboards
    mkdir -p monitoring/grafana/datasources
    mkdir -p logs
    mkdir -p data

    print_success "تم إنشاء المجلدات"
}

# إنشاء ملفات الإعداد المفقودة
create_missing_configs() {
    print_step "إنشاء ملفات الإعداد المفقودة..."

    # إنشاء ملف .env إذا لم يكن موجود
    if [ ! -f ".env" ]; then
        cat > .env << EOF
# متغيرات البيئة لنظام أنوبيس
POSTGRES_DB=anubis_db
POSTGRES_USER=anubis
POSTGRES_PASSWORD=anubis123
REDIS_PASSWORD=anubis_redis_pass
GRAFANA_ADMIN_PASSWORD=anubis123
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=anubis123
ANUBIS_SECRET_KEY=anubis_super_secret_key_2024
MCP_SECRET_KEY=mcp_secret_key_2024
HORUS_SECRET_KEY=horus_secret_key_2024
EOF
        print_success "تم إنشاء ملف .env"
    fi

    # إنشاء ملف Grafana datasource
    mkdir -p monitoring/grafana/datasources
    cat > monitoring/grafana/datasources/prometheus.yml << EOF
apiVersion: 1
datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
EOF

    print_success "تم إنشاء ملفات الإعداد"
}

# بناء الصور
build_images() {
    print_step "بناء صور Docker..."

    docker-compose -f docker-compose-full-server.yml build --no-cache

    print_success "تم بناء الصور بنجاح"
}

# تشغيل الخدمات
start_services() {
    print_step "تشغيل الخدمات..."

    # تشغيل البنية التحتية أولاً
    print_step "تشغيل البنية التحتية (قواعد البيانات، Redis)..."
    docker-compose -f docker-compose-full-server.yml up -d anubis-db anubis-redis

    # انتظار حتى تصبح قواعد البيانات جاهزة
    print_step "انتظار جاهزية قواعد البيانات..."
    sleep 30

    # تشغيل الخدمات الأساسية
    print_step "تشغيل الخدمات الأساسية..."
    docker-compose -f docker-compose-full-server.yml up -d anubis-core anubis-mcp horus-team

    # انتظار حتى تصبح الخدمات جاهزة
    print_step "انتظار جاهزية الخدمات الأساسية..."
    sleep 20

    # تشغيل الواجهة والمراقبة
    print_step "تشغيل الواجهة والمراقبة..."
    docker-compose -f docker-compose-full-server.yml up -d anubis-web-client nginx prometheus grafana

    # تشغيل الخدمات الإضافية
    print_step "تشغيل الخدمات الإضافية..."
    docker-compose -f docker-compose-full-server.yml up -d anubis-n8n elasticsearch kibana

    print_success "تم تشغيل جميع الخدمات"
}

# فحص حالة الخدمات
check_services() {
    print_step "فحص حالة الخدمات..."

    sleep 10

    # قائمة الخدمات للفحص
    services=(
        "http://localhost:8000/health:نظام أنوبيس"
        "http://localhost:3000/health:نظام MCP"
        "http://localhost:7000/health:فريق حورس"
        "http://localhost:5000:الواجهة الموحدة"
        "http://localhost:9090:Prometheus"
        "http://localhost:3001:Grafana"
        "http://localhost:5678:N8N"
    )

    echo -e "${CYAN}📊 حالة الخدمات:${NC}"

    for service in "${services[@]}"; do
        url=$(echo $service | cut -d: -f1-2)
        name=$(echo $service | cut -d: -f3)

        if curl -s -f "$url" > /dev/null 2>&1; then
            echo -e "${GREEN}✅ $name: متاح${NC}"
        else
            echo -e "${RED}❌ $name: غير متاح${NC}"
        fi
    done
}

# عرض معلومات الوصول
show_access_info() {
    print_step "معلومات الوصول للخدمات:"

    echo -e "${CYAN}🌐 الواجهات المتاحة:${NC}"
    echo -e "${GREEN}🏺 الواجهة الرئيسية: http://localhost${NC}"
    echo -e "${GREEN}🏺 نظام أنوبيس: http://localhost/anubis${NC}"
    echo -e "${GREEN}🔗 نظام MCP: http://localhost/mcp${NC}"
    echo -e "${GREEN}𓅃 فريق حورس: http://localhost/horus${NC}"
    echo -e "${GREEN}📊 Grafana: http://localhost:3001 (admin/anubis123)${NC}"
    echo -e "${GREEN}🔄 N8N: http://localhost:5678 (admin/anubis123)${NC}"
    echo -e "${GREEN}📈 Prometheus: http://localhost:9090${NC}"
    echo -e "${GREEN}📝 Kibana: http://localhost:5601${NC}"

    echo -e "${CYAN}🔧 أوامر مفيدة:${NC}"
    echo -e "${YELLOW}عرض السجلات: docker-compose -f docker-compose-full-server.yml logs -f${NC}"
    echo -e "${YELLOW}إيقاف الخدمات: docker-compose -f docker-compose-full-server.yml down${NC}"
    echo -e "${YELLOW}إعادة التشغيل: docker-compose -f docker-compose-full-server.yml restart${NC}"
}

# الدالة الرئيسية
main() {
    print_header

    check_requirements
    create_directories
    create_missing_configs

    # سؤال المستخدم عن البناء
    read -p "هل تريد بناء الصور من جديد؟ (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        build_images
    fi

    start_services
    check_services
    show_access_info

    print_success "تم تشغيل نظام أنوبيس المتكامل بنجاح!"
    print_warning "يرجى الانتظار بضع دقائق حتى تصبح جميع الخدمات جاهزة تماماً."
}

# تشغيل الدالة الرئيسية
main "$@"