#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 نظام الاختبار الشامل لنظام أنوبيس
𓅃 اختبار كامل لجميع مكونات ووظائف النظام
"""

import os
import sys
import json
import time
import subprocess
import importlib.util
from datetime import datetime
from pathlib import Path
import traceback

class AnubisComprehensiveTestingSystem:
    def __init__(self):
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.anubis_path = "ANUBIS_SYSTEM"
        self.test_results = {
            "timestamp": self.timestamp,
            "total_tests": 0,
            "passed_tests": 0,
            "failed_tests": 0,
            "test_details": {},
            "performance_metrics": {},
            "recommendations": []
        }
        
    def run_comprehensive_test(self):
        """تشغيل الاختبار الشامل"""
        print("🏺 بدء الاختبار الشامل لنظام أنوبيس")
        print("𓅃 سيتم اختبار جميع المكونات والوظائف...")
        print("="*70)
        
        if not os.path.exists(self.anubis_path):
            print(f"❌ نظام أنوبيس غير موجود: {self.anubis_path}")
            return None
        
        # قائمة الاختبارات الشاملة
        test_suite = [
            ("🔍 فحص البنية الأساسية", self.test_basic_structure),
            ("📄 اختبار الملفات الرئيسية", self.test_main_files),
            ("🐍 اختبار الكود المصدري", self.test_source_code),
            ("⚙️ اختبار ملفات التكوين", self.test_configuration),
            ("🐳 اختبار إعداد Docker", self.test_docker_setup),
            ("📦 اختبار المتطلبات", self.test_requirements),
            ("🗄️ اختبار قاعدة البيانات", self.test_database),
            ("📜 اختبار السكريبتات", self.test_scripts),
            ("🧪 تشغيل الاختبارات المدمجة", self.test_built_in_tests),
            ("🚀 اختبار التشغيل السريع", self.test_quick_start),
            ("🤖 اختبار خدمات الذكاء الاصطناعي", self.test_ai_services),
            ("🔐 اختبار الأمان", self.test_security),
            ("📊 اختبار المراقبة", self.test_monitoring),
            ("🏢 اختبار بيئة العمل", self.test_workspace),
            ("⚡ اختبار الأداء", self.test_performance)
        ]
        
        # تشغيل جميع الاختبارات
        for test_name, test_function in test_suite:
            self.run_single_test(test_name, test_function)
        
        # إنشاء التقرير النهائي
        self.generate_final_report()
        
        return self.test_results
    
    def run_single_test(self, test_name, test_function):
        """تشغيل اختبار واحد"""
        print(f"\n{test_name}")
        print("-" * 50)
        
        start_time = time.time()
        
        try:
            result = test_function()
            end_time = time.time()
            duration = end_time - start_time
            
            self.test_results["total_tests"] += 1
            
            if result.get("success", False):
                self.test_results["passed_tests"] += 1
                status = "✅ نجح"
            else:
                self.test_results["failed_tests"] += 1
                status = "❌ فشل"
            
            print(f"النتيجة: {status}")
            print(f"الوقت: {duration:.2f} ثانية")
            
            # حفظ تفاصيل الاختبار
            self.test_results["test_details"][test_name] = {
                "success": result.get("success", False),
                "duration": duration,
                "details": result.get("details", ""),
                "issues": result.get("issues", []),
                "recommendations": result.get("recommendations", [])
            }
            
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            
            self.test_results["total_tests"] += 1
            self.test_results["failed_tests"] += 1
            
            print(f"النتيجة: ❌ خطأ - {str(e)}")
            print(f"الوقت: {duration:.2f} ثانية")
            
            self.test_results["test_details"][test_name] = {
                "success": False,
                "duration": duration,
                "error": str(e),
                "traceback": traceback.format_exc()
            }
    
    def test_basic_structure(self):
        """اختبار البنية الأساسية"""
        required_dirs = ["src", "config", "scripts", "tests"]
        required_files = ["main.py", "requirements.txt", "README.md"]
        
        issues = []
        details = []
        
        # فحص المجلدات المطلوبة
        for dir_name in required_dirs:
            dir_path = os.path.join(self.anubis_path, dir_name)
            if os.path.exists(dir_path):
                details.append(f"✅ مجلد {dir_name} موجود")
            else:
                issues.append(f"❌ مجلد {dir_name} مفقود")
        
        # فحص الملفات المطلوبة
        for file_name in required_files:
            file_path = os.path.join(self.anubis_path, file_name)
            if os.path.exists(file_path):
                details.append(f"✅ ملف {file_name} موجود")
            else:
                issues.append(f"❌ ملف {file_name} مفقود")
        
        success = len(issues) == 0
        
        return {
            "success": success,
            "details": "\n".join(details),
            "issues": issues
        }
    
    def test_main_files(self):
        """اختبار الملفات الرئيسية"""
        main_file = os.path.join(self.anubis_path, "main.py")
        quick_start = os.path.join(self.anubis_path, "quick_start_anubis.py")
        
        details = []
        issues = []
        
        # فحص main.py
        if os.path.exists(main_file):
            try:
                with open(main_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if "def main(" in content or "if __name__" in content:
                        details.append("✅ main.py يحتوي على نقطة دخول")
                    else:
                        issues.append("⚠️ main.py لا يحتوي على نقطة دخول واضحة")
                    
                    details.append(f"📊 حجم main.py: {len(content)} حرف")
                    details.append(f"📊 عدد الأسطر: {len(content.split())}")
            except Exception as e:
                issues.append(f"❌ خطأ في قراءة main.py: {str(e)}")
        else:
            issues.append("❌ main.py غير موجود")
        
        # فحص quick_start_anubis.py
        if os.path.exists(quick_start):
            details.append("✅ quick_start_anubis.py موجود")
        else:
            issues.append("⚠️ quick_start_anubis.py غير موجود")
        
        return {
            "success": len([i for i in issues if i.startswith("❌")]) == 0,
            "details": "\n".join(details),
            "issues": issues
        }
    
    def test_source_code(self):
        """اختبار الكود المصدري"""
        src_path = os.path.join(self.anubis_path, "src")
        
        if not os.path.exists(src_path):
            return {
                "success": False,
                "details": "مجلد src غير موجود",
                "issues": ["❌ مجلد src مفقود"]
            }
        
        details = []
        issues = []
        python_files = 0
        
        # فحص الوحدات الفرعية
        expected_modules = ["core", "ai_services", "automation", "security", "monitoring"]
        
        for module in expected_modules:
            module_path = os.path.join(src_path, module)
            if os.path.exists(module_path):
                details.append(f"✅ وحدة {module} موجودة")
                
                # عد ملفات Python في الوحدة
                py_files = [f for f in os.listdir(module_path) if f.endswith('.py')]
                python_files += len(py_files)
                details.append(f"📊 {module}: {len(py_files)} ملف Python")
            else:
                issues.append(f"⚠️ وحدة {module} غير موجودة")
        
        details.append(f"📊 إجمالي ملفات Python: {python_files}")
        
        return {
            "success": python_files > 0,
            "details": "\n".join(details),
            "issues": issues
        }
    
    def test_configuration(self):
        """اختبار ملفات التكوين"""
        config_path = os.path.join(self.anubis_path, "config")
        
        if not os.path.exists(config_path):
            return {
                "success": False,
                "details": "مجلد config غير موجود",
                "issues": ["❌ مجلد config مفقود"]
            }
        
        details = []
        issues = []
        
        # فحص ملفات التكوين المهمة
        important_configs = [
            "default_config.json",
            "ai_config.json", 
            "database_config.json"
        ]
        
        valid_configs = 0
        
        for config_file in important_configs:
            config_file_path = os.path.join(config_path, config_file)
            if os.path.exists(config_file_path):
                try:
                    with open(config_file_path, 'r', encoding='utf-8') as f:
                        json.load(f)
                    details.append(f"✅ {config_file} صالح")
                    valid_configs += 1
                except json.JSONDecodeError:
                    issues.append(f"❌ {config_file} يحتوي على JSON غير صالح")
                except Exception as e:
                    issues.append(f"❌ خطأ في قراءة {config_file}: {str(e)}")
            else:
                issues.append(f"⚠️ {config_file} غير موجود")
        
        # فحص مجلد security
        security_path = os.path.join(config_path, "security")
        if os.path.exists(security_path):
            details.append("✅ مجلد security موجود")
        else:
            issues.append("⚠️ مجلد security غير موجود")
        
        return {
            "success": valid_configs > 0,
            "details": "\n".join(details),
            "issues": issues
        }
    
    def test_docker_setup(self):
        """اختبار إعداد Docker"""
        details = []
        issues = []
        
        # فحص Dockerfile الرئيسي
        dockerfile = os.path.join(self.anubis_path, "Dockerfile")
        if os.path.exists(dockerfile):
            details.append("✅ Dockerfile الرئيسي موجود")
        else:
            issues.append("⚠️ Dockerfile الرئيسي غير موجود")
        
        # فحص docker-compose.yml
        compose_file = os.path.join(self.anubis_path, "docker-compose.yml")
        if os.path.exists(compose_file):
            details.append("✅ docker-compose.yml موجود")
        else:
            issues.append("⚠️ docker-compose.yml غير موجود")
        
        # فحص مجلد docker
        docker_path = os.path.join(self.anubis_path, "docker")
        if os.path.exists(docker_path):
            docker_files = os.listdir(docker_path)
            compose_files = [f for f in docker_files if f.startswith("docker-compose")]
            dockerfiles = [f for f in docker_files if f.startswith("Dockerfile")]
            
            details.append(f"✅ مجلد docker موجود")
            details.append(f"📊 ملفات docker-compose: {len(compose_files)}")
            details.append(f"📊 ملفات Dockerfile: {len(dockerfiles)}")
        else:
            issues.append("⚠️ مجلد docker غير موجود")
        
        return {
            "success": len([i for i in issues if i.startswith("❌")]) == 0,
            "details": "\n".join(details),
            "issues": issues
        }
    
    def test_requirements(self):
        """اختبار ملفات المتطلبات"""
        details = []
        issues = []
        
        # البحث عن جميع ملفات requirements
        req_files = [f for f in os.listdir(self.anubis_path) 
                    if f.startswith("requirements") and f.endswith(".txt")]
        
        if not req_files:
            return {
                "success": False,
                "details": "لا توجد ملفات requirements",
                "issues": ["❌ لا توجد ملفات requirements"]
            }
        
        total_packages = 0
        
        for req_file in req_files:
            req_path = os.path.join(self.anubis_path, req_file)
            try:
                with open(req_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    packages = [l.strip() for l in lines if l.strip() and not l.startswith('#')]
                    total_packages += len(packages)
                    
                details.append(f"✅ {req_file}: {len(packages)} حزمة")
            except Exception as e:
                issues.append(f"❌ خطأ في قراءة {req_file}: {str(e)}")
        
        details.append(f"📊 إجمالي الحزم: {total_packages}")
        
        return {
            "success": total_packages > 0,
            "details": "\n".join(details),
            "issues": issues
        }
    
    def test_database(self):
        """اختبار قاعدة البيانات"""
        details = []
        issues = []
        
        # فحص مجلد database
        db_path = os.path.join(self.anubis_path, "database")
        if os.path.exists(db_path):
            details.append("✅ مجلد database موجود")
            
            # فحص ملف قاعدة البيانات
            db_file = os.path.join(db_path, "anubis.db")
            if os.path.exists(db_file):
                file_size = os.path.getsize(db_file)
                details.append(f"✅ anubis.db موجود ({file_size} بايت)")
            else:
                issues.append("⚠️ anubis.db غير موجود")
        else:
            issues.append("⚠️ مجلد database غير موجود")
        
        # فحص مجلد data
        data_path = os.path.join(self.anubis_path, "data")
        if os.path.exists(data_path):
            details.append("✅ مجلد data موجود")
            
            # فحص مجلدات فرعية
            subdirs = [d for d in os.listdir(data_path) 
                      if os.path.isdir(os.path.join(data_path, d))]
            details.append(f"📊 مجلدات فرعية في data: {len(subdirs)}")
        else:
            issues.append("⚠️ مجلد data غير موجود")
        
        return {
            "success": len([i for i in issues if i.startswith("❌")]) == 0,
            "details": "\n".join(details),
            "issues": issues
        }
    
    def test_scripts(self):
        """اختبار السكريبتات"""
        scripts_path = os.path.join(self.anubis_path, "scripts")
        
        if not os.path.exists(scripts_path):
            return {
                "success": False,
                "details": "مجلد scripts غير موجود",
                "issues": ["❌ مجلد scripts مفقود"]
            }
        
        details = []
        issues = []
        
        # عد السكريبتات
        script_files = [f for f in os.listdir(scripts_path) 
                       if f.endswith(('.py', '.sh', '.bat'))]
        
        details.append(f"📊 عدد السكريبتات: {len(script_files)}")
        
        # فحص المجلدات الفرعية
        subdirs = [d for d in os.listdir(scripts_path) 
                  if os.path.isdir(os.path.join(scripts_path, d))]
        
        for subdir in subdirs:
            details.append(f"✅ مجلد فرعي: {subdir}")
        
        # فحص سكريبتات مهمة
        important_scripts = ["setup_environment.py", "quick_setup.py"]
        for script in important_scripts:
            script_path = os.path.join(scripts_path, script)
            if os.path.exists(script_path):
                details.append(f"✅ {script} موجود")
            else:
                issues.append(f"⚠️ {script} غير موجود")
        
        return {
            "success": len(script_files) > 0,
            "details": "\n".join(details),
            "issues": issues
        }
    
    def test_built_in_tests(self):
        """تشغيل الاختبارات المدمجة"""
        tests_path = os.path.join(self.anubis_path, "tests")
        
        if not os.path.exists(tests_path):
            return {
                "success": False,
                "details": "مجلد tests غير موجود",
                "issues": ["❌ مجلد tests مفقود"]
            }
        
        details = []
        issues = []
        
        # البحث عن ملفات الاختبار
        test_files = [f for f in os.listdir(tests_path) if f.startswith("test_") and f.endswith(".py")]
        
        details.append(f"📊 عدد ملفات الاختبار: {len(test_files)}")
        
        # محاولة تشغيل الاختبارات
        for test_file in test_files:
            test_path = os.path.join(tests_path, test_file)
            details.append(f"🧪 ملف اختبار: {test_file}")
            
            # يمكن إضافة تشغيل فعلي للاختبارات هنا
            # لكن نتجنب ذلك لتجنب المشاكل المحتملة
        
        return {
            "success": len(test_files) > 0,
            "details": "\n".join(details),
            "issues": issues
        }
    
    def test_quick_start(self):
        """اختبار التشغيل السريع"""
        quick_start = os.path.join(self.anubis_path, "quick_start_anubis.py")
        
        if not os.path.exists(quick_start):
            return {
                "success": False,
                "details": "ملف التشغيل السريع غير موجود",
                "issues": ["❌ quick_start_anubis.py غير موجود"]
            }
        
        details = []
        issues = []
        
        try:
            with open(quick_start, 'r', encoding='utf-8') as f:
                content = f.read()
                
            details.append("✅ ملف التشغيل السريع موجود")
            details.append(f"📊 حجم الملف: {len(content)} حرف")
            
            # فحص محتوى الملف
            if "def" in content:
                details.append("✅ يحتوي على دوال")
            if "import" in content:
                details.append("✅ يحتوي على استيرادات")
            
        except Exception as e:
            issues.append(f"❌ خطأ في قراءة الملف: {str(e)}")
        
        return {
            "success": len(issues) == 0,
            "details": "\n".join(details),
            "issues": issues
        }
    
    def test_ai_services(self):
        """اختبار خدمات الذكاء الاصطناعي"""
        ai_path = os.path.join(self.anubis_path, "src", "ai_services")
        
        if not os.path.exists(ai_path):
            return {
                "success": False,
                "details": "مجلد ai_services غير موجود",
                "issues": ["⚠️ مجلد ai_services غير موجود"]
            }
        
        details = []
        issues = []
        
        # فحص محتويات مجلد AI
        ai_files = os.listdir(ai_path)
        py_files = [f for f in ai_files if f.endswith('.py')]
        
        details.append(f"✅ مجلد ai_services موجود")
        details.append(f"📊 ملفات Python: {len(py_files)}")
        
        # البحث عن خدمات AI محددة
        expected_services = ["openai", "gemini", "claude", "ollama"]
        for service in expected_services:
            service_files = [f for f in py_files if service.lower() in f.lower()]
            if service_files:
                details.append(f"✅ خدمة {service}: {len(service_files)} ملف")
            else:
                details.append(f"⚠️ خدمة {service}: غير موجودة")
        
        return {
            "success": len(py_files) > 0,
            "details": "\n".join(details),
            "issues": issues
        }
    
    def test_security(self):
        """اختبار الأمان"""
        details = []
        issues = []
        
        # فحص مجلد security في src
        security_src = os.path.join(self.anubis_path, "src", "security")
        if os.path.exists(security_src):
            details.append("✅ مجلد security في src موجود")
        else:
            issues.append("⚠️ مجلد security في src غير موجود")
        
        # فحص مجلد security في config
        security_config = os.path.join(self.anubis_path, "config", "security")
        if os.path.exists(security_config):
            details.append("✅ مجلد security في config موجود")
        else:
            issues.append("⚠️ مجلد security في config غير موجود")
        
        # فحص مجلد ssl
        ssl_path = os.path.join(self.anubis_path, "ssl")
        if os.path.exists(ssl_path):
            details.append("✅ مجلد ssl موجود")
        else:
            issues.append("⚠️ مجلد ssl غير موجود")
        
        return {
            "success": len([i for i in issues if i.startswith("❌")]) == 0,
            "details": "\n".join(details),
            "issues": issues
        }
    
    def test_monitoring(self):
        """اختبار المراقبة"""
        monitoring_path = os.path.join(self.anubis_path, "src", "monitoring")
        
        details = []
        issues = []
        
        if os.path.exists(monitoring_path):
            details.append("✅ مجلد monitoring موجود")
            
            # فحص ملفات المراقبة
            monitoring_files = [f for f in os.listdir(monitoring_path) if f.endswith('.py')]
            details.append(f"📊 ملفات المراقبة: {len(monitoring_files)}")
        else:
            issues.append("⚠️ مجلد monitoring غير موجود")
        
        # فحص مجلد logs
        logs_path = os.path.join(self.anubis_path, "logs")
        if os.path.exists(logs_path):
            details.append("✅ مجلد logs موجود")
            
            # فحص المجلدات الفرعية للسجلات
            log_subdirs = [d for d in os.listdir(logs_path) 
                          if os.path.isdir(os.path.join(logs_path, d))]
            details.append(f"📊 أنواع السجلات: {len(log_subdirs)}")
        else:
            issues.append("⚠️ مجلد logs غير موجود")
        
        return {
            "success": len([i for i in issues if i.startswith("❌")]) == 0,
            "details": "\n".join(details),
            "issues": issues
        }
    
    def test_workspace(self):
        """اختبار بيئة العمل"""
        workspace_path = os.path.join(self.anubis_path, "workspace")
        
        if not os.path.exists(workspace_path):
            return {
                "success": False,
                "details": "مجلد workspace غير موجود",
                "issues": ["⚠️ مجلد workspace غير موجود"]
            }
        
        details = []
        issues = []
        
        details.append("✅ مجلد workspace موجود")
        
        # فحص ملفات بيئة العمل
        workspace_files = os.listdir(workspace_path)
        script_files = [f for f in workspace_files if f.endswith(('.sh', '.py'))]
        
        details.append(f"📊 ملفات بيئة العمل: {len(workspace_files)}")
        details.append(f"📊 سكريبتات: {len(script_files)}")
        
        # فحص سكريبتات مهمة
        important_scripts = ["start_isolated_workspace.sh", "stop_isolated_workspace.sh"]
        for script in important_scripts:
            if script in workspace_files:
                details.append(f"✅ {script} موجود")
            else:
                issues.append(f"⚠️ {script} غير موجود")
        
        return {
            "success": len([i for i in issues if i.startswith("❌")]) == 0,
            "details": "\n".join(details),
            "issues": issues
        }
    
    def test_performance(self):
        """اختبار الأداء"""
        details = []
        issues = []
        
        start_time = time.time()
        
        # حساب حجم المشروع
        total_size = 0
        file_count = 0
        
        for root, dirs, files in os.walk(self.anubis_path):
            for file in files:
                file_path = os.path.join(root, file)
                try:
                    total_size += os.path.getsize(file_path)
                    file_count += 1
                except:
                    pass
        
        end_time = time.time()
        scan_duration = end_time - start_time
        
        # تحويل الحجم إلى وحدة مناسبة
        if total_size > 1024*1024:
            size_str = f"{total_size/(1024*1024):.2f} MB"
        elif total_size > 1024:
            size_str = f"{total_size/1024:.2f} KB"
        else:
            size_str = f"{total_size} bytes"
        
        details.append(f"📊 حجم المشروع: {size_str}")
        details.append(f"📊 عدد الملفات: {file_count}")
        details.append(f"⏱️ وقت المسح: {scan_duration:.2f} ثانية")
        
        # تقييم الأداء
        if scan_duration < 1.0:
            details.append("⚡ أداء ممتاز")
        elif scan_duration < 3.0:
            details.append("✅ أداء جيد")
        else:
            issues.append("⚠️ أداء بطيء")
        
        # حفظ مقاييس الأداء
        self.test_results["performance_metrics"] = {
            "total_size_bytes": total_size,
            "total_files": file_count,
            "scan_duration": scan_duration,
            "size_formatted": size_str
        }
        
        return {
            "success": True,
            "details": "\n".join(details),
            "issues": issues
        }
    
    def generate_final_report(self):
        """إنشاء التقرير النهائي"""
        success_rate = (self.test_results["passed_tests"] / self.test_results["total_tests"]) * 100
        
        print("\n" + "="*70)
        print("🏺 التقرير النهائي للاختبار الشامل لنظام أنوبيس")
        print("="*70)
        
        print(f"📊 إجمالي الاختبارات: {self.test_results['total_tests']}")
        print(f"✅ الاختبارات الناجحة: {self.test_results['passed_tests']}")
        print(f"❌ الاختبارات الفاشلة: {self.test_results['failed_tests']}")
        print(f"📈 معدل النجاح: {success_rate:.1f}%")
        
        # تقييم الحالة العامة
        if success_rate >= 90:
            status = "🏆 ممتاز - جاهز للإنتاج"
        elif success_rate >= 75:
            status = "✅ جيد - يحتاج تحسينات بسيطة"
        elif success_rate >= 50:
            status = "⚠️ متوسط - يحتاج تحسينات"
        else:
            status = "❌ ضعيف - يحتاج إصلاحات كبيرة"
        
        print(f"🎯 التقييم العام: {status}")
        
        # عرض مقاييس الأداء
        if "performance_metrics" in self.test_results:
            metrics = self.test_results["performance_metrics"]
            print(f"📊 حجم المشروع: {metrics['size_formatted']}")
            print(f"📁 عدد الملفات: {metrics['total_files']}")
        
        print("="*70)
        
        # حفظ التقرير
        report_filename = f"anubis_comprehensive_test_report_{self.timestamp}.json"
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        print(f"📄 تم حفظ التقرير المفصل: {report_filename}")

def main():
    """الدالة الرئيسية"""
    print("🏺 مرحباً بك في نظام الاختبار الشامل لأنوبيس")
    print("𓅃 سيتم اختبار جميع مكونات ووظائف النظام...")
    
    tester = AnubisComprehensiveTestingSystem()
    
    try:
        results = tester.run_comprehensive_test()
        
        if results:
            print("\n✅ تم إكمال الاختبار الشامل بنجاح!")
            return results
        else:
            print("\n❌ فشل في تشغيل الاختبار الشامل")
            return None
        
    except Exception as e:
        print(f"\n❌ خطأ في النظام: {str(e)}")
        return None

if __name__ == "__main__":
    main()
