version: '3.8'

services:
  anubis-advanced:
    build: .
    container_name: anubis-advanced-isolation
    restart: unless-stopped
    
    # إعدادات الأمان المتقدمة جداً
    security_opt:
      - no-new-privileges:true
      - apparmor:anubis-advanced-profile
      - seccomp:./security/seccomp-profile.json
    
    # قيود الموارد المتقدمة
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    
    # الشبكات المتعددة المعزولة
    networks:
      - anubis-advanced-net
      - anubis-monitoring-net
      - anubis-data-net
    
    # الأحجام المشفرة والمعزولة
    volumes:
      - anubis-advanced-data:/app/data
      - anubis-advanced-logs:/app/logs:rw
      - anubis-advanced-secrets:/app/secrets:ro
      - anubis-advanced-configs:/app/configs:ro
    
    # متغيرات البيئة المشفرة
    environment:
      - ANUBIS_MODE=advanced_isolation
      - SECURITY_LEVEL=maximum
      - ENCRYPTION_ENABLED=true
      - AUDIT_LOGGING=true
      - NETWORK_ISOLATION=strict
    
    # إعدادات متقدمة
    pid: "container"
    ipc: "private"
    
    # حماية متقدمة للنظام
    read_only: true
    tmpfs:
      - /tmp:rw,noexec,nosuid,nodev,size=100m
      - /var/tmp:rw,noexec,nosuid,nodev,size=50m
      - /run:rw,noexec,nosuid,nodev,size=50m
    
    # منع جميع الامتيازات
    cap_drop:
      - ALL
    
    # قيود النواة المتقدمة
    sysctls:
      - net.ipv4.ip_unprivileged_port_start=0
      - net.ipv4.ping_group_range=1 0
    
    # المنافذ المحمية
    ports:
      - "127.0.0.1:8002:8000"
    
    # تسميات مراقبة متقدمة
    labels:
      - "anubis.isolation.level=advanced"
      - "anubis.security.profile=maximum"
      - "anubis.monitoring.enabled=true"
      - "anubis.encryption.enabled=true"
      - "anubis.audit.enabled=true"
    
    # التبعيات
    depends_on:
      - anubis-monitor
      - anubis-vault
  
  anubis-monitor:
    image: prom/prometheus:latest
    container_name: anubis-security-monitor
    restart: unless-stopped
    networks:
      - anubis-monitoring-net
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - anubis-monitor-data:/prometheus
    ports:
      - "127.0.0.1:9090:9090"
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp:rw,noexec,nosuid,size=50m
  
  anubis-vault:
    image: vault:latest
    container_name: anubis-secrets-vault
    restart: unless-stopped
    networks:
      - anubis-data-net
    volumes:
      - anubis-vault-data:/vault/data
      - ./security/vault-config.hcl:/vault/config/vault.hcl:ro
    ports:
      - "127.0.0.1:8200:8200"
    cap_add:
      - IPC_LOCK
    security_opt:
      - no-new-privileges:true
    environment:
      - VAULT_DEV_ROOT_TOKEN_ID=anubis-dev-token
      - VAULT_DEV_LISTEN_ADDRESS=0.0.0.0:8200
  
  anubis-scanner:
    image: aquasec/trivy:latest
    container_name: anubis-security-scanner
    restart: "no"
    networks:
      - anubis-monitoring-net
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - anubis-scanner-cache:/root/.cache
    command: ["image", "--exit-code", "1", "anubis-advanced-isolation"]
    depends_on:
      - anubis-advanced

# الشبكات المعزولة المتقدمة
networks:
  anubis-advanced-net:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
          gateway: **********
    driver_opts:
      com.docker.network.bridge.enable_icc: "false"
      com.docker.network.enable_ipv6: "false"
      com.docker.network.bridge.host_binding_ipv4: "127.0.0.1"
  
  anubis-monitoring-net:
    driver: bridge
    internal: true
    ipam:
      config:
        - subnet: **********/16
  
  anubis-data-net:
    driver: bridge
    internal: true
    ipam:
      config:
        - subnet: **********/16

# الأحجام المشفرة
volumes:
  anubis-advanced-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data
  anubis-advanced-logs:
    driver: local
  anubis-advanced-secrets:
    driver: local
  anubis-advanced-configs:
    driver: local
  anubis-monitor-data:
    driver: local
  anubis-vault-data:
    driver: local
  anubis-scanner-cache:
    driver: local
