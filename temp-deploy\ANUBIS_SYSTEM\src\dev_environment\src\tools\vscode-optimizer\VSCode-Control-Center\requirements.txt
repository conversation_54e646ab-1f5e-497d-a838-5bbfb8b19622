# VS Code Control Center - AI Enhanced
# المكتبات المطلوبة

# المكتبات الأساسية
psutil>=5.9.0          # مراقبة النظام والعمليات
requests>=2.28.0       # طلبات HTTP للوكلاء الذكيين

# مكتبات اختيارية للميزات المتقدمة
# (يمكن تثبيتها حسب الحاجة)

# للتحليل المتقدم
# numpy>=1.21.0        # العمليات الرياضية
# pandas>=1.3.0        # تحليل البيانات

# للرسوم البيانية (مستقبلاً)
# matplotlib>=3.5.0    # الرسوم البيانية
# plotly>=5.0.0        # رسوم تفاعلية

# للذكاء الاصطناعي المحلي
# transformers>=4.20.0 # نماذج Hugging Face
# torch>=1.12.0        # PyTorch

# ملاحظات:
# - tkinter مدمجة مع Python (لا تحتاج تثبيت)
# - Gemini CLI يثبت منفصلاً عبر npm
# - Ollama يثبت منفصلاً من موقعه الرسمي
