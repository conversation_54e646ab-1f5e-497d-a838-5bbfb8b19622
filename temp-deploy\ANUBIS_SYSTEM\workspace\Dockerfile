# حاوية بيئة العمل المعزولة لنظام أنوبيس
FROM python:3.11-slim

# إعداد متغيرات البيئة لبيئة العمل
ENV PYTHONUNBUFFERED=1
ENV WORKSPACE_MODE=isolated
ENV USER_ID=1004
ENV GROUP_ID=1004
ENV WORKSPACE_DATA_PATH=/app/workspace_data
ENV WORKSPACE_LOGS_PATH=/app/workspace_logs

# إنشاء مستخدم مخصص لبيئة العمل
RUN groupadd -g $GROUP_ID anubis_workspace && \
    useradd -u $USER_ID -g $GROUP_ID -m -s /bin/bash anubis_workspace

# تثبيت أدوات بيئة التطوير
RUN apt-get update && apt-get install -y --no-install-recommends \
    git \
    curl \
    wget \
    vim \
    nano \
    htop \
    tree \
    jq \
    sqlite3 \
    && pip install --no-cache-dir \
    jupyter \
    jupyterlab \
    pandas \
    numpy \
    matplotlib \
    seaborn \
    plotly \
    streamlit \
    fastapi \
    uvicorn \
    pytest \
    black \
    flake8 \
    mypy \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# إعداد مجلد العمل
WORKDIR /app/workspace

# إنشاء هيكل مجلدات بيئة العمل
RUN mkdir -p /app/workspace/logs \
             /app/workspace/reports \
             /app/workspace/projects \
             /app/workspace/configs \
             /app/workspace/temp \
             /app/workspace/notebooks \
             /app/workspace/scripts \
             /app/workspace/data \
    && chown -R anubis_workspace:anubis_workspace /app/workspace

# نسخ بيانات بيئة العمل
COPY --chown=anubis_workspace:anubis_workspace . .

# التبديل للمستخدم غير المميز
USER anubis_workspace

# إعداد Jupyter Lab
RUN jupyter lab --generate-config && \
    echo "c.ServerApp.ip = '0.0.0.0'" >> ~/.jupyter/jupyter_lab_config.py && \
    echo "c.ServerApp.allow_root = False" >> ~/.jupyter/jupyter_lab_config.py && \
    echo "c.ServerApp.open_browser = False" >> ~/.jupyter/jupyter_lab_config.py

# فحص صحة بيئة العمل
HEALTHCHECK --interval=30s --timeout=15s --start-period=45s --retries=3 \
    CMD python -c "import sys; print('Workspace OK')" || exit 1

# المنافذ المكشوفة
EXPOSE 8888 8501 8000

# نقطة الدخول لبيئة العمل
ENTRYPOINT ["jupyter", "lab", "--port=8888", "--no-browser", "--allow-root"]
