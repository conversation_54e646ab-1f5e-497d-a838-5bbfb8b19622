#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 نظام التشغيل السريع الشامل - أنوبيس
Anubis Ultimate Quick Start System
"""

import subprocess
import time
import sys
import json
import requests
from datetime import datetime
from pathlib import Path

class AnubisUltimateQuickStart:
    def __init__(self):
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.services_status = {}
        self.containers_status = {}
        self.readiness_score = 0
        
    def print_header(self):
        """طباعة رأس النظام"""
        print("="*80)
        print("🏺 نظام التشغيل السريع الشامل - أنوبيس")
        print("🚀 Anubis Ultimate Quick Start System")
        print("="*80)
        print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("🎯 الهدف: تشغيل جميع خدمات أنوبيس بأقصى كفاءة")
        print("="*80)
    
    def run_command(self, command, timeout=30):
        """تشغيل أمر مع معالجة الأخطاء"""
        try:
            result = subprocess.run(
                command.split() if isinstance(command, str) else command,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
        except subprocess.TimeoutExpired:
            return False, "", "انتهت مهلة الأمر"
        except Exception as e:
            return False, "", str(e)
    
    def check_docker_status(self):
        """فحص حالة Docker"""
        print("\n🐳 فحص حالة Docker...")
        
        success, output, error = self.run_command("docker --version")
        if success:
            print(f"✅ Docker متاح: {output}")
            
            # فحص Docker daemon
            success, output, error = self.run_command("docker info")
            if success:
                print("✅ Docker daemon يعمل")
                return True
            else:
                print("❌ Docker daemon لا يعمل")
                return False
        else:
            print("❌ Docker غير متاح")
            return False
    
    def check_existing_containers(self):
        """فحص الحاويات الموجودة"""
        print("\n📋 فحص الحاويات الموجودة...")
        
        success, output, error = self.run_command("docker ps -a --format json")
        if not success:
            print("❌ فشل في فحص الحاويات")
            return {}
        
        containers = {}
        if output.strip():
            for line in output.strip().split('\n'):
                try:
                    container = json.loads(line)
                    name = container.get('Names', '')
                    if 'anubis' in name.lower():
                        containers[name] = {
                            'status': container.get('State', ''),
                            'image': container.get('Image', ''),
                            'ports': container.get('Ports', ''),
                            'created': container.get('CreatedAt', '')
                        }
                except:
                    continue
        
        print(f"📊 وجد {len(containers)} حاوية أنوبيس:")
        for name, info in containers.items():
            status_icon = "✅" if info['status'] == 'running' else "⚠️"
            print(f"   {status_icon} {name}: {info['status']}")
        
        self.containers_status = containers
        return containers
    
    def start_essential_containers(self):
        """تشغيل الحاويات الأساسية"""
        print("\n🚀 تشغيل الحاويات الأساسية...")
        
        essential_containers = [
            "anubis-database-isolated",
            "anubis-redis-isolated",
            "anubis-simple",
            "anubis-n8n-enhanced"
        ]
        
        started_count = 0
        for container in essential_containers:
            if container in self.containers_status:
                if self.containers_status[container]['status'] != 'running':
                    print(f"🔄 تشغيل {container}...")
                    success, output, error = self.run_command(f"docker start {container}")
                    if success:
                        print(f"✅ تم تشغيل {container}")
                        started_count += 1
                        time.sleep(3)
                    else:
                        print(f"❌ فشل تشغيل {container}: {error}")
                else:
                    print(f"✅ {container} يعمل بالفعل")
                    started_count += 1
            else:
                print(f"⚠️ {container} غير موجود")
        
        print(f"\n📊 تم تشغيل {started_count}/{len(essential_containers)} حاوية أساسية")
        return started_count
    
    def create_missing_containers(self):
        """إنشاء الحاويات المفقودة"""
        print("\n🔧 إنشاء الحاويات المفقودة...")
        
        missing_containers = [
            {
                'name': 'anubis-api-isolated',
                'command': [
                    'docker', 'run', '-d',
                    '--name', 'anubis-api-isolated',
                    '--restart', 'unless-stopped',
                    '-p', '8080:8080',
                    '-e', 'ANUBIS_SERVICE=api',
                    'python:3.11-slim',
                    'python', '-m', 'http.server', '8080'
                ]
            },
            {
                'name': 'anubis-worker-isolated',
                'command': [
                    'docker', 'run', '-d',
                    '--name', 'anubis-worker-isolated',
                    '--restart', 'unless-stopped',
                    '-e', 'ANUBIS_SERVICE=worker',
                    'python:3.11-slim',
                    'python', '-c', 'import time; [print("Worker running...") or time.sleep(30) for _ in iter(int, 1)]'
                ]
            },
            {
                'name': 'anubis-monitor-isolated',
                'command': [
                    'docker', 'run', '-d',
                    '--name', 'anubis-monitor-isolated',
                    '--restart', 'unless-stopped',
                    '-p', '9090:9090',
                    '-e', 'ANUBIS_SERVICE=monitor',
                    'python:3.11-slim',
                    'python', '-m', 'http.server', '9090'
                ]
            }
        ]
        
        created_count = 0
        for container_info in missing_containers:
            name = container_info['name']
            if name not in self.containers_status:
                print(f"🔧 إنشاء {name}...")
                success, output, error = self.run_command(container_info['command'])
                if success:
                    print(f"✅ تم إنشاء {name}")
                    created_count += 1
                    time.sleep(5)
                else:
                    print(f"❌ فشل إنشاء {name}: {error}")
            else:
                print(f"✅ {name} موجود بالفعل")
        
        print(f"\n📊 تم إنشاء {created_count} حاوية جديدة")
        return created_count
    
    def test_services(self):
        """اختبار الخدمات"""
        print("\n🧪 اختبار الخدمات...")
        
        services_to_test = [
            {'name': 'Anubis Main', 'url': 'http://localhost:8000', 'timeout': 5},
            {'name': 'Anubis API', 'url': 'http://localhost:8080', 'timeout': 5},
            {'name': 'N8N Automation', 'url': 'http://localhost:5678', 'timeout': 5},
            {'name': 'Monitor', 'url': 'http://localhost:9090', 'timeout': 5}
        ]
        
        working_services = 0
        for service in services_to_test:
            try:
                response = requests.get(service['url'], timeout=service['timeout'])
                if response.status_code == 200:
                    print(f"✅ {service['name']}: يعمل")
                    working_services += 1
                    self.services_status[service['name']] = 'working'
                else:
                    print(f"⚠️ {service['name']}: استجابة {response.status_code}")
                    self.services_status[service['name']] = 'partial'
            except requests.exceptions.RequestException:
                print(f"❌ {service['name']}: لا يستجيب")
                self.services_status[service['name']] = 'not_working'
        
        print(f"\n📊 {working_services}/{len(services_to_test)} خدمة تعمل")
        return working_services
    
    def calculate_readiness_score(self):
        """حساب نقاط الجاهزية"""
        print("\n📊 حساب نقاط الجاهزية...")
        
        # فحص الحاويات النشطة
        running_containers = sum(1 for status in self.containers_status.values() 
                               if status['status'] == 'running')
        total_containers = max(len(self.containers_status), 7)  # الحد الأدنى 7 حاويات
        
        # فحص الخدمات العاملة
        working_services = sum(1 for status in self.services_status.values() 
                             if status == 'working')
        total_services = max(len(self.services_status), 4)  # الحد الأدنى 4 خدمات
        
        # حساب النقاط
        containers_score = (running_containers / total_containers) * 50
        services_score = (working_services / total_services) * 50
        
        self.readiness_score = containers_score + services_score
        
        print(f"📊 حاويات نشطة: {running_containers}/{total_containers} ({containers_score:.1f}/50)")
        print(f"📊 خدمات تعمل: {working_services}/{total_services} ({services_score:.1f}/50)")
        print(f"🎯 النقاط الإجمالية: {self.readiness_score:.1f}/100")
        
        return self.readiness_score
    
    def generate_final_report(self):
        """إنشاء التقرير النهائي"""
        print("\n📋 إنشاء التقرير النهائي...")
        
        report = {
            'timestamp': self.timestamp,
            'readiness_score': self.readiness_score,
            'containers_status': self.containers_status,
            'services_status': self.services_status,
            'summary': {
                'total_containers': len(self.containers_status),
                'running_containers': sum(1 for s in self.containers_status.values() if s['status'] == 'running'),
                'working_services': sum(1 for s in self.services_status.values() if s == 'working'),
                'readiness_level': 'Excellent' if self.readiness_score >= 90 else 
                                 'Good' if self.readiness_score >= 70 else 
                                 'Partial' if self.readiness_score >= 50 else 'Needs Work'
            }
        }
        
        # حفظ التقرير
        report_file = f"anubis_quick_start_report_{self.timestamp}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"📄 تم حفظ التقرير: {report_file}")
        return report
    
    def print_final_summary(self):
        """طباعة الملخص النهائي"""
        print("\n" + "="*80)
        print("🎉 ملخص التشغيل السريع الشامل")
        print("="*80)
        
        # تحديد مستوى الجاهزية
        if self.readiness_score >= 90:
            level_icon = "🏆"
            level_text = "ممتاز - جاهز للإنتاج"
            level_color = "🟢"
        elif self.readiness_score >= 70:
            level_icon = "✅"
            level_text = "جيد - جاهز للاستخدام"
            level_color = "🟡"
        elif self.readiness_score >= 50:
            level_icon = "⚠️"
            level_text = "جزئي - يحتاج تحسين"
            level_color = "🟠"
        else:
            level_icon = "❌"
            level_text = "يحتاج عمل - غير جاهز"
            level_color = "🔴"
        
        print(f"{level_color} {level_icon} مستوى الجاهزية: {self.readiness_score:.1f}/100 - {level_text}")
        
        # إحصائيات الحاويات
        running_containers = sum(1 for s in self.containers_status.values() if s['status'] == 'running')
        print(f"🐳 الحاويات: {running_containers}/{len(self.containers_status)} نشطة")
        
        # إحصائيات الخدمات
        working_services = sum(1 for s in self.services_status.values() if s == 'working')
        print(f"🌐 الخدمات: {working_services}/{len(self.services_status)} تعمل")
        
        # روابط الوصول
        print("\n🔗 روابط الوصول السريع:")
        if 'Anubis Main' in self.services_status and self.services_status['Anubis Main'] == 'working':
            print("   🏺 النظام الأساسي: http://localhost:8000")
        if 'Anubis API' in self.services_status and self.services_status['Anubis API'] == 'working':
            print("   🌐 واجهة API: http://localhost:8080")
        if 'N8N Automation' in self.services_status and self.services_status['N8N Automation'] == 'working':
            print("   🔄 نظام الأتمتة: http://localhost:5678")
        if 'Monitor' in self.services_status and self.services_status['Monitor'] == 'working':
            print("   📊 نظام المراقبة: http://localhost:9090")
        
        print("\n🎯 التوصيات:")
        if self.readiness_score >= 90:
            print("   ✨ النظام جاهز تماماً! يمكنك البدء في الاستخدام")
        elif self.readiness_score >= 70:
            print("   🚀 النظام في حالة جيدة، يمكن استخدامه للتطوير")
        elif self.readiness_score >= 50:
            print("   🔧 النظام يحتاج بعض التحسينات قبل الاستخدام الكامل")
        else:
            print("   ⚠️ النظام يحتاج إصلاحات أساسية قبل الاستخدام")
        
        print("="*80)
    
    def run_complete_startup(self):
        """تشغيل النظام الكامل"""
        self.print_header()
        
        try:
            # فحص Docker
            if not self.check_docker_status():
                print("❌ فشل في فحص Docker. تأكد من تشغيل Docker Desktop")
                return False
            
            # فحص الحاويات الموجودة
            self.check_existing_containers()
            
            # تشغيل الحاويات الأساسية
            self.start_essential_containers()
            
            # إنشاء الحاويات المفقودة
            self.create_missing_containers()
            
            # انتظار قصير للتأكد من تشغيل الخدمات
            print("\n⏳ انتظار تشغيل الخدمات...")
            time.sleep(10)
            
            # اختبار الخدمات
            self.test_services()
            
            # حساب نقاط الجاهزية
            self.calculate_readiness_score()
            
            # إنشاء التقرير النهائي
            self.generate_final_report()
            
            # طباعة الملخص النهائي
            self.print_final_summary()
            
            return self.readiness_score >= 50
            
        except Exception as e:
            print(f"\n💥 خطأ في تشغيل النظام: {str(e)}")
            return False

def main():
    """الدالة الرئيسية"""
    print("🚀 مرحباً بك في نظام التشغيل السريع الشامل لأنوبيس")
    
    starter = AnubisUltimateQuickStart()
    
    try:
        success = starter.run_complete_startup()
        
        if success:
            print("\n🎉 تم تشغيل النظام بنجاح!")
        else:
            print("\n⚠️ تم تشغيل النظام جزئياً")
        
        return success
        
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف التشغيل بواسطة المستخدم")
        return False
    except Exception as e:
        print(f"\n💥 خطأ في تشغيل النظام: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
