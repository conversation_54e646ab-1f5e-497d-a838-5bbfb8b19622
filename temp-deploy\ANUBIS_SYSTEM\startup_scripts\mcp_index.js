
const express = require('express');
const app = express();
const port = 3000;

app.use(express.json());

app.get('/', (req, res) => {
    res.json({
        service: 'Anubis MCP System',
        status: 'running',
        message: 'نظام MCP يعمل بنجاح'
    });
});

app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        service: 'anubis-mcp',
        timestamp: new Date().toISOString()
    });
});

app.listen(port, '0.0.0.0', () => {
    console.log(`🔗 نظام MCP يعمل على المنفذ ${port}`);
});
