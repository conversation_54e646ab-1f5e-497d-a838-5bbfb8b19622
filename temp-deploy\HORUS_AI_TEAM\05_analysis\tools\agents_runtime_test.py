#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🤖 اختبار تشغيل الوكلاء والنماذج
𓅃 اختبار فعلي لتشغيل جميع وكلاء فريق حورس

تم إنشاؤه وفقاً لقواعد التطوير المعتمدة
الموقع: HORUS_AI_TEAM/05_analysis/tools/
"""

import os
import sys
import json
import time
import subprocess
import asyncio
from datetime import datetime
from typing import Dict, List, Optional
from pathlib import Path

class HorusAgentsRuntimeTester:
    """اختبار تشغيل وكلاء فريق حورس"""
    
    def __init__(self):
        """تهيئة نظام اختبار التشغيل"""
        self.project_root = Path(__file__).parent.parent.parent
        self.test_results = {
            "timestamp": datetime.now().isoformat(),
            "runtime_tests": {},
            "models_status": {},
            "agents_performance": {},
            "summary": {}
        }
        
        print("🤖 اختبار تشغيل وكلاء فريق حورس")
        print("=" * 60)
        print(f"📍 مجلد المشروع: {self.project_root}")
        print(f"⏰ وقت البدء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()

    def test_ollama_models(self) -> Dict:
        """اختبار النماذج المحلية (Ollama)"""
        print("🏠 اختبار النماذج المحلية (Ollama)...")
        
        local_models = [
            "phi3:mini",
            "mistral:7b", 
            "llama3:8b",
            "strikegpt-r1-zero-8b",
            "qwen2.5-vl:7b"
        ]
        
        results = {
            "status": "success",
            "models_available": 0,
            "models_working": 0,
            "models": {}
        }
        
        # التحقق من وجود Ollama
        try:
            result = subprocess.run(["ollama", "list"], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                available_models = result.stdout
                print("  ✅ Ollama متاح")
                
                for model in local_models:
                    if model in available_models:
                        results["models_available"] += 1
                        # اختبار تشغيل النموذج
                        model_test = self._test_ollama_model(model)
                        results["models"][model] = model_test
                        if model_test["status"] == "success":
                            results["models_working"] += 1
                            print(f"    ✅ {model} - يعمل")
                        else:
                            print(f"    ❌ {model} - خطأ")
                    else:
                        results["models"][model] = {"status": "not_installed"}
                        print(f"    ⚠️ {model} - غير مثبت")
                        
            else:
                results["status"] = "error"
                results["error"] = "Ollama غير متاح"
                print("  ❌ Ollama غير متاح")
                
        except Exception as e:
            results["status"] = "error"
            results["error"] = str(e)
            print(f"  ❌ خطأ في Ollama: {e}")
        
        print(f"📊 النتيجة: {results['models_working']}/{len(local_models)} نماذج تعمل")
        print()
        return results

    def _test_ollama_model(self, model_name: str) -> Dict:
        """اختبار نموذج Ollama محدد"""
        try:
            # اختبار بسيط للنموذج
            test_prompt = "مرحبا"
            result = subprocess.run(
                ["ollama", "run", model_name, test_prompt],
                capture_output=True, text=True, timeout=30
            )
            
            if result.returncode == 0 and result.stdout.strip():
                return {
                    "status": "success",
                    "response_length": len(result.stdout),
                    "response_time": "< 30s"
                }
            else:
                return {
                    "status": "error",
                    "error": result.stderr or "لا توجد استجابة"
                }
                
        except subprocess.TimeoutExpired:
            return {"status": "timeout", "error": "انتهت مهلة الاختبار"}
        except Exception as e:
            return {"status": "error", "error": str(e)}

    def test_external_apis(self) -> Dict:
        """اختبار APIs الخارجية"""
        print("🌐 اختبار APIs الخارجية...")
        
        results = {
            "status": "success",
            "apis_tested": 0,
            "apis_working": 0,
            "apis": {}
        }
        
        # اختبار Gemini API
        gemini_result = self._test_gemini_api()
        results["apis"]["gemini"] = gemini_result
        results["apis_tested"] += 1
        if gemini_result["status"] == "success":
            results["apis_working"] += 1
            print("  ✅ Gemini API - يعمل")
        else:
            print("  ❌ Gemini API - خطأ")
        
        # يمكن إضافة APIs أخرى هنا
        
        print(f"📊 النتيجة: {results['apis_working']}/{results['apis_tested']} APIs تعمل")
        print()
        return results

    def _test_gemini_api(self) -> Dict:
        """اختبار Gemini API"""
        try:
            # محاولة استيراد وتشغيل Gemini
            import google.generativeai as genai
            
            # البحث عن مفتاح API في متغيرات البيئة أو ملفات التكوين
            api_key = os.getenv("GEMINI_API_KEY")
            if not api_key:
                # البحث في ملفات التكوين
                config_files = [
                    self.project_root / "07_configuration" / "api_keys.json",
                    self.project_root.parent / "ANUBIS_HORUS_MCP" / "api_keys_vault" / "api_keys_collection.json"
                ]
                
                for config_file in config_files:
                    if config_file.exists():
                        try:
                            with open(config_file, 'r') as f:
                                config = json.load(f)
                                if "google_gemini" in config:
                                    api_key = config["google_gemini"].get("api_key")
                                    break
                        except:
                            continue
            
            if not api_key:
                return {"status": "error", "error": "مفتاح API غير موجود"}
            
            # اختبار الاتصال
            genai.configure(api_key=api_key)
            model = genai.GenerativeModel('gemini-pro')
            response = model.generate_content("مرحبا")
            
            if response and response.text:
                return {
                    "status": "success",
                    "response_length": len(response.text),
                    "model": "gemini-pro"
                }
            else:
                return {"status": "error", "error": "لا توجد استجابة"}
                
        except ImportError:
            return {"status": "error", "error": "مكتبة google-generativeai غير مثبتة"}
        except Exception as e:
            return {"status": "error", "error": str(e)}

    def test_agents_functionality(self) -> Dict:
        """اختبار وظائف الوكلاء"""
        print("🤖 اختبار وظائف الوكلاء...")
        
        results = {
            "status": "success",
            "agents_tested": 0,
            "agents_functional": 0,
            "agents": {}
        }
        
        # اختبار الوكلاء في 02_team_members
        team_members_path = self.project_root / "02_team_members"
        if team_members_path.exists():
            agent_files = list(team_members_path.glob("*_agent.py"))
            
            for agent_file in agent_files:
                agent_name = agent_file.stem
                print(f"  🤖 اختبار وظائف {agent_name}...")
                
                agent_result = self._test_agent_functionality(agent_file)
                results["agents"][agent_name] = agent_result
                results["agents_tested"] += 1
                
                if agent_result["status"] == "success":
                    results["agents_functional"] += 1
                    print(f"    ✅ وظائف تعمل بشكل صحيح")
                else:
                    print(f"    ❌ مشكلة في الوظائف")
        
        # اختبار وكيل البحث على الويب
        web_agent_path = self.project_root / "04_specialized_agents" / "web_research_agent"
        if web_agent_path.exists():
            print(f"  🌐 اختبار وظائف web_research_agent...")
            web_result = self._test_web_agent_functionality(web_agent_path)
            results["agents"]["web_research_agent"] = web_result
            results["agents_tested"] += 1
            if web_result["status"] == "success":
                results["agents_functional"] += 1
                print(f"    ✅ وظائف تعمل بشكل صحيح")
            else:
                print(f"    ❌ مشكلة في الوظائف")
        
        print(f"📊 النتيجة: {results['agents_functional']}/{results['agents_tested']} وكلاء وظيفيين")
        print()
        return results

    def _test_agent_functionality(self, agent_file: Path) -> Dict:
        """اختبار وظائف وكيل محدد"""
        try:
            with open(agent_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # فحص الوظائف الأساسية
            checks = {
                "has_init": "__init__" in content,
                "has_methods": "def " in content,
                "has_docstrings": '"""' in content or "'''" in content,
                "has_error_handling": "try:" in content or "except" in content,
                "has_logging": "print(" in content or "logging" in content
            }
            
            functionality_score = sum(checks.values()) / len(checks) * 100
            
            return {
                "status": "success" if functionality_score >= 60 else "warning",
                "functionality_score": functionality_score,
                "checks": checks,
                "lines": len(content.split('\n'))
            }
            
        except Exception as e:
            return {"status": "error", "error": str(e)}

    def _test_web_agent_functionality(self, web_agent_path: Path) -> Dict:
        """اختبار وظائف وكيل البحث على الويب"""
        try:
            agent_file = web_agent_path / "agent" / "anubis_web_researcher.py"
            if not agent_file.exists():
                return {"status": "error", "error": "ملف الوكيل غير موجود"}
            
            with open(agent_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # فحص الوظائف المتخصصة
            checks = {
                "has_anubis_class": "class AnubisWebResearcher" in content,
                "has_search_methods": "search" in content.lower(),
                "has_gemini_integration": "genai" in content,
                "has_tools_import": "from tools import" in content,
                "has_error_handling": "try:" in content,
                "has_memory_system": "search_history" in content
            }
            
            functionality_score = sum(checks.values()) / len(checks) * 100
            
            # فحص ملفات إضافية
            additional_files = {
                "tools_file": (web_agent_path / "agent" / "tools.py").exists(),
                "readme_file": (web_agent_path / "README.md").exists(),
                "main_file": (web_agent_path / "main.py").exists()
            }
            
            return {
                "status": "success" if functionality_score >= 70 else "warning",
                "functionality_score": functionality_score,
                "checks": checks,
                "additional_files": additional_files,
                "lines": len(content.split('\n'))
            }
            
        except Exception as e:
            return {"status": "error", "error": str(e)}

    def test_system_integration(self) -> Dict:
        """اختبار تكامل النظام"""
        print("🔗 اختبار تكامل النظام...")
        
        results = {
            "status": "success",
            "integration_score": 0,
            "components": {}
        }
        
        # اختبار الواجهات الرئيسية
        interfaces_path = self.project_root / "01_core" / "interfaces"
        if interfaces_path.exists():
            main_interface = interfaces_path / "horus_interface.py"
            if main_interface.exists():
                interface_result = self._test_main_interface(main_interface)
                results["components"]["main_interface"] = interface_result
                print(f"  ✅ الواجهة الرئيسية - متاحة")
            else:
                print(f"  ❌ الواجهة الرئيسية - غير موجودة")
        
        # اختبار نظام الذاكرة
        memory_path = self.project_root / "03_memory_system"
        if memory_path.exists():
            memory_files = list(memory_path.glob("*.py"))
            if memory_files:
                results["components"]["memory_system"] = {"status": "available", "files": len(memory_files)}
                print(f"  ✅ نظام الذاكرة - متاح ({len(memory_files)} ملف)")
            else:
                print(f"  ❌ نظام الذاكرة - فارغ")
        
        # اختبار الأدوات المساعدة
        utilities_path = self.project_root / "08_utilities"
        if utilities_path.exists():
            utility_files = list(utilities_path.rglob("*.py"))
            if utility_files:
                results["components"]["utilities"] = {"status": "available", "files": len(utility_files)}
                print(f"  ✅ الأدوات المساعدة - متاحة ({len(utility_files)} ملف)")
        
        print()
        return results

    def _test_main_interface(self, interface_file: Path) -> Dict:
        """اختبار الواجهة الرئيسية"""
        try:
            with open(interface_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # فحص المكونات الأساسية للواجهة
            checks = {
                "has_horus_class": "class" in content and "horus" in content.lower(),
                "has_agent_methods": "agent" in content.lower(),
                "has_team_management": "team" in content.lower(),
                "has_error_handling": "try:" in content
            }
            
            return {
                "status": "success",
                "checks": checks,
                "lines": len(content.split('\n'))
            }
            
        except Exception as e:
            return {"status": "error", "error": str(e)}

    def run_runtime_test(self) -> Dict:
        """تشغيل اختبار التشغيل الشامل"""
        print("🚀 بدء اختبار التشغيل الشامل")
        print("=" * 60)
        
        # تشغيل جميع اختبارات التشغيل
        self.test_results["runtime_tests"]["ollama_models"] = self.test_ollama_models()
        self.test_results["runtime_tests"]["external_apis"] = self.test_external_apis()
        self.test_results["runtime_tests"]["agents_functionality"] = self.test_agents_functionality()
        self.test_results["runtime_tests"]["system_integration"] = self.test_system_integration()
        
        # حساب الملخص
        self._calculate_runtime_summary()
        
        # حفظ النتائج
        self._save_runtime_results()
        
        # عرض الملخص النهائي
        self._display_runtime_summary()
        
        return self.test_results

    def _calculate_runtime_summary(self):
        """حساب ملخص نتائج التشغيل"""
        tests = self.test_results["runtime_tests"]
        
        total_components = 0
        working_components = 0
        
        # حساب النماذج المحلية
        if "ollama_models" in tests:
            ollama = tests["ollama_models"]
            total_components += len(ollama.get("models", {}))
            working_components += ollama.get("models_working", 0)
        
        # حساب APIs الخارجية
        if "external_apis" in tests:
            apis = tests["external_apis"]
            total_components += apis.get("apis_tested", 0)
            working_components += apis.get("apis_working", 0)
        
        # حساب الوكلاء
        if "agents_functionality" in tests:
            agents = tests["agents_functionality"]
            total_components += agents.get("agents_tested", 0)
            working_components += agents.get("agents_functional", 0)
        
        self.test_results["summary"] = {
            "total_components": total_components,
            "working_components": working_components,
            "runtime_success_rate": (working_components / total_components * 100) if total_components > 0 else 0,
            "overall_runtime_status": "operational" if working_components >= total_components * 0.7 else "needs_attention"
        }

    def _save_runtime_results(self):
        """حفظ نتائج اختبار التشغيل"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = self.project_root / "05_analysis" / "reports" / f"runtime_test_results_{timestamp}.json"
        
        results_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        print(f"💾 تم حفظ نتائج التشغيل في: {results_file}")

    def _display_runtime_summary(self):
        """عرض ملخص نتائج التشغيل"""
        summary = self.test_results["summary"]
        
        print("\n" + "=" * 60)
        print("🚀 ملخص نتائج اختبار التشغيل")
        print("=" * 60)
        
        print(f"🔧 إجمالي المكونات: {summary['total_components']}")
        print(f"✅ المكونات العاملة: {summary['working_components']}")
        print(f"📈 معدل التشغيل: {summary['runtime_success_rate']:.1f}%")
        
        if summary["overall_runtime_status"] == "operational":
            print("🎉 حالة النظام: جاهز للتشغيل!")
        else:
            print("⚠️ حالة النظام: يحتاج إلى انتباه")
        
        print(f"\n⏰ وقت الانتهاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)

def main():
    """الدالة الرئيسية"""
    tester = HorusAgentsRuntimeTester()
    results = tester.run_runtime_test()
    return results

if __name__ == "__main__":
    main()
