
import sys
import os
from http.server import HTTPServer, BaseHTTPRequestHandler
import json

class HorusHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/health':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {
                "status": "healthy", 
                "service": "horus-team",
                "team_members": ["THOTH", "PTAH", "RA", "KHNUM", "SESHAT", "ANUBIS", "MAAT", "HAPI"]
            }
            self.wfile.write(json.dumps(response).encode())
        elif self.path == '/team':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            team = {
                "THOTH": "المحلل السريع",
                "PTAH": "المطور الخبير", 
                "RA": "المستشار الاستراتيجي",
                "KHNUM": "المبدع والمبتكر",
                "SESHAT": "المحللة البصرية",
                "ANUBIS": "حارس الأمان",
                "MAAT": "حارسة العدالة",
                "HAPI": "محلل البيانات"
            }
            self.wfile.write(json.dumps(team, ensure_ascii=False).encode('utf-8'))
        else:
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            html = '''
            <html><body style="font-family: Arial; text-align: center; padding: 50px;">
            <h1>𓅃 فريق حورس</h1>
            <p>Horus AI Team</p>
            <p>فريق الذكاء الاصطناعي التعاوني</p>
            <a href="/team">عرض الفريق</a> | <a href="/health">فحص الصحة</a>
            </body></html>
            '''
            self.wfile.write(html.encode('utf-8'))

server = HTTPServer(('0.0.0.0', 7000), HorusHandler)
print("𓅃 فريق حورس يعمل على المنفذ 7000")
server.serve_forever()
