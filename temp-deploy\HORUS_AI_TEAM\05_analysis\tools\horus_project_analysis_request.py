#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
طلب تحليل شامل لمشروع حورس
Comprehensive Horus Project Analysis Request
"""

import os
import sys
import json
import time
from datetime import datetime
from typing import Dict, List, Any

# إضافة المسارات المطلوبة
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(current_dir)
sys.path.append(project_root)

# إضافة مسار GEMINI_MODELS_TESTER
gemini_tester_path = os.path.join(project_root, 'GEMINI_MODELS_TESTER')
sys.path.append(gemini_tester_path)

try:
    from horus_stable_system import HorusStableSystem
except ImportError:
    print("❌ لا يمكن استيراد نظام حورس")
    sys.exit(1)

class HorusProjectAnalyzer:
    """محلل مشروع حورس الشامل"""
    
    def __init__(self):
        print("𓅃 محلل مشروع حورس الشامل")
        print("=" * 50)
        
        self.project_path = current_dir
        self.horus_system = None
        self.analysis_results = {}
        
        # تهيئة نظام حورس
        self.initialize_horus()
    
    def initialize_horus(self):
        """تهيئة نظام حورس للتحليل"""
        try:
            print("🔧 تهيئة نظام حورس للتحليل...")
            self.horus_system = HorusStableSystem()
            
            if self.horus_system.system_status['active_agents'] > 0:
                print("✅ نظام حورس جاهز للتحليل")
                return True
            else:
                print("⚠️ لا توجد وكلاء متاحون للتحليل")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في تهيئة حورس: {e}")
            return False
    
    def scan_project_structure(self) -> Dict[str, Any]:
        """فحص بنية المشروع"""
        print("\n📁 فحص بنية مشروع HORUS_AI_TEAM...")
        
        structure = {
            'total_files': 0,
            'total_directories': 0,
            'python_files': [],
            'markdown_files': [],
            'json_files': [],
            'directories': {},
            'file_sizes': {},
            'file_types': {}
        }
        
        for root, dirs, files in os.walk(self.project_path):
            # تجاهل مجلدات __pycache__
            dirs[:] = [d for d in dirs if d != '__pycache__']
            
            rel_path = os.path.relpath(root, self.project_path)
            if rel_path == '.':
                rel_path = 'root'
            
            structure['directories'][rel_path] = {
                'files': [],
                'subdirs': dirs.copy(),
                'file_count': len(files)
            }
            
            structure['total_directories'] += 1
            
            for file in files:
                if file.endswith('.pyc'):
                    continue
                    
                file_path = os.path.join(root, file)
                rel_file_path = os.path.relpath(file_path, self.project_path)
                
                structure['total_files'] += 1
                structure['directories'][rel_path]['files'].append(file)
                
                # تصنيف الملفات
                if file.endswith('.py'):
                    structure['python_files'].append(rel_file_path)
                elif file.endswith('.md'):
                    structure['markdown_files'].append(rel_file_path)
                elif file.endswith('.json'):
                    structure['json_files'].append(rel_file_path)
                
                # حجم الملف
                try:
                    size = os.path.getsize(file_path)
                    structure['file_sizes'][rel_file_path] = size
                except:
                    structure['file_sizes'][rel_file_path] = 0
                
                # نوع الملف
                ext = os.path.splitext(file)[1].lower()
                if ext in structure['file_types']:
                    structure['file_types'][ext] += 1
                else:
                    structure['file_types'][ext] = 1
        
        return structure
    
    def analyze_with_horus(self, analysis_type: str, data: str) -> str:
        """تحليل البيانات باستخدام نظام حورس"""
        if not self.horus_system:
            return "❌ نظام حورس غير متاح"
        
        try:
            print(f"🤖 تحليل {analysis_type} بواسطة حورس...")
            response = self.horus_system.ask(data)
            return response
        except Exception as e:
            return f"❌ خطأ في التحليل: {e}"
    
    def comprehensive_analysis(self):
        """التحليل الشامل للمشروع"""
        print("\n🔍 بدء التحليل الشامل لمشروع HORUS_AI_TEAM")
        print("=" * 60)
        
        # 1. فحص البنية
        print("\n📊 المرحلة 1: فحص بنية المشروع")
        structure = self.scan_project_structure()
        self.analysis_results['structure'] = structure
        
        print(f"📁 إجمالي المجلدات: {structure['total_directories']}")
        print(f"📄 إجمالي الملفات: {structure['total_files']}")
        print(f"🐍 ملفات Python: {len(structure['python_files'])}")
        print(f"📝 ملفات Markdown: {len(structure['markdown_files'])}")
        print(f"📋 ملفات JSON: {len(structure['json_files'])}")
        
        # 2. تحليل البنية مع حورس
        print("\n🤖 المرحلة 2: تحليل البنية مع حورس")
        structure_summary = f"""
        تحليل بنية مشروع HORUS_AI_TEAM:
        
        إحصائيات المشروع:
        - إجمالي المجلدات: {structure['total_directories']}
        - إجمالي الملفات: {structure['total_files']}
        - ملفات Python: {len(structure['python_files'])}
        - ملفات التوثيق: {len(structure['markdown_files'])}
        - ملفات التكوين: {len(structure['json_files'])}
        
        المجلدات الرئيسية:
        {', '.join([d for d in structure['directories'].keys() if '/' not in d and d != 'root'])}
        
        أكبر الملفات:
        {self.get_largest_files(structure['file_sizes'])}
        
        أنواع الملفات:
        {json.dumps(structure['file_types'], indent=2)}
        
        يرجى تحليل هذه البنية وتقديم تقييم شامل لتنظيم المشروع، نقاط القوة، والتحسينات المقترحة.
        """
        
        structure_analysis = self.analyze_with_horus("بنية المشروع", structure_summary)
        self.analysis_results['structure_analysis'] = structure_analysis
        
        # 3. تحليل الملفات الأساسية
        print("\n📋 المرحلة 3: تحليل الملفات الأساسية")
        core_files_analysis = self.analyze_core_files()
        self.analysis_results['core_files'] = core_files_analysis
        
        # 4. تحليل التوثيق
        print("\n📚 المرحلة 4: تحليل التوثيق")
        documentation_analysis = self.analyze_documentation()
        self.analysis_results['documentation'] = documentation_analysis
        
        # 5. تحليل التكوين والإعدادات
        print("\n⚙️ المرحلة 5: تحليل التكوين")
        config_analysis = self.analyze_configuration()
        self.analysis_results['configuration'] = config_analysis
        
        # 6. تقييم الجودة الشامل
        print("\n🏆 المرحلة 6: التقييم الشامل")
        overall_assessment = self.get_overall_assessment()
        self.analysis_results['overall_assessment'] = overall_assessment
        
        # 7. حفظ النتائج
        self.save_analysis_results()
        
        # 8. عرض التقرير النهائي
        self.display_final_report()
    
    def get_largest_files(self, file_sizes: Dict[str, int], top_n: int = 5) -> str:
        """الحصول على أكبر الملفات"""
        sorted_files = sorted(file_sizes.items(), key=lambda x: x[1], reverse=True)[:top_n]
        return '\n'.join([f"  - {file}: {size/1024:.1f} KB" for file, size in sorted_files])
    
    def analyze_core_files(self) -> str:
        """تحليل الملفات الأساسية"""
        core_files = [
            'advanced_horus_system.py',
            'horus_complete_system.py',
            'horus_stable_system.py',
            'task_management_system.py',
            'web_interface.py'
        ]
        
        analysis_text = "تحليل الملفات الأساسية لنظام حورس:\n\n"
        
        for file in core_files:
            file_path = os.path.join(self.project_path, file)
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        lines = len(content.split('\n'))
                        chars = len(content)
                        
                    analysis_text += f"📄 {file}:\n"
                    analysis_text += f"   - الأسطر: {lines}\n"
                    analysis_text += f"   - الأحرف: {chars}\n"
                    analysis_text += f"   - الحجم: {chars/1024:.1f} KB\n\n"
                    
                except Exception as e:
                    analysis_text += f"❌ خطأ في قراءة {file}: {e}\n\n"
            else:
                analysis_text += f"❌ {file} غير موجود\n\n"
        
        analysis_text += "\nيرجى تحليل هذه الملفات الأساسية وتقييم جودة الكود، التنظيم، والتكامل بينها."
        
        return self.analyze_with_horus("الملفات الأساسية", analysis_text)
    
    def analyze_documentation(self) -> str:
        """تحليل التوثيق"""
        doc_files = [
            'README_FINAL.md',
            'FINAL_COMPLETION_REPORT.md',
            'QUICK_START_GUIDE.md',
            'TERMINAL_ISSUES_SOLUTION_REPORT.md'
        ]
        
        analysis_text = "تحليل توثيق مشروع حورس:\n\n"
        
        total_docs = 0
        total_size = 0
        
        for file in doc_files:
            file_path = os.path.join(self.project_path, file)
            if os.path.exists(file_path):
                try:
                    size = os.path.getsize(file_path)
                    total_docs += 1
                    total_size += size
                    
                    analysis_text += f"📚 {file}: {size/1024:.1f} KB\n"
                    
                except Exception as e:
                    analysis_text += f"❌ خطأ في {file}: {e}\n"
        
        analysis_text += f"\nإحصائيات التوثيق:\n"
        analysis_text += f"- عدد الملفات: {total_docs}\n"
        analysis_text += f"- الحجم الإجمالي: {total_size/1024:.1f} KB\n\n"
        
        analysis_text += "يرجى تقييم جودة وشمولية التوثيق، ومدى وضوحه للمستخدمين."
        
        return self.analyze_with_horus("التوثيق", analysis_text)
    
    def analyze_configuration(self) -> str:
        """تحليل التكوين والإعدادات"""
        config_info = "تحليل تكوين وإعدادات مشروع حورس:\n\n"
        
        # فحص ملفات المتطلبات
        req_files = ['requirements_complete.txt']
        for file in req_files:
            file_path = os.path.join(self.project_path, file)
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        lines = content.split('\n')
                        packages = [line for line in lines if line.strip() and not line.startswith('#')]
                        
                    config_info += f"📦 {file}:\n"
                    config_info += f"   - إجمالي الأسطر: {len(lines)}\n"
                    config_info += f"   - المكتبات: {len(packages)}\n\n"
                    
                except Exception as e:
                    config_info += f"❌ خطأ في {file}: {e}\n\n"
        
        # فحص مجلد التكوين
        config_dir = os.path.join(self.project_path, '07_configuration')
        if os.path.exists(config_dir):
            config_files = []
            for root, dirs, files in os.walk(config_dir):
                config_files.extend(files)
            
            config_info += f"⚙️ مجلد التكوين (07_configuration):\n"
            config_info += f"   - عدد الملفات: {len(config_files)}\n"
            config_info += f"   - الملفات: {', '.join(config_files[:10])}\n\n"
        
        config_info += "يرجى تحليل تكوين المشروع وتقييم مدى تنظيم الإعدادات والمتطلبات."
        
        return self.analyze_with_horus("التكوين", config_info)
    
    def get_overall_assessment(self) -> str:
        """التقييم الشامل للمشروع"""
        assessment_prompt = f"""
        تقييم شامل لمشروع HORUS_AI_TEAM:
        
        بناءً على التحليل السابق، يرجى تقديم تقييم شامل يشمل:
        
        1. نقاط القوة في المشروع
        2. المجالات التي تحتاج تحسين
        3. جودة التنظيم والبنية
        4. مستوى التوثيق
        5. التكامل بين المكونات
        6. التوصيات للتطوير المستقبلي
        7. تقييم عام من 1-10
        
        معلومات المشروع:
        - إجمالي الملفات: {self.analysis_results['structure']['total_files']}
        - ملفات Python: {len(self.analysis_results['structure']['python_files'])}
        - ملفات التوثيق: {len(self.analysis_results['structure']['markdown_files'])}
        - المجلدات: {self.analysis_results['structure']['total_directories']}
        
        يرجى تقديم تحليل مفصل ومفيد.
        """
        
        return self.analyze_with_horus("التقييم الشامل", assessment_prompt)
    
    def save_analysis_results(self):
        """حفظ نتائج التحليل"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"horus_project_analysis_{timestamp}.json"
        filepath = os.path.join(self.project_path, filename)
        
        try:
            # إضافة معلومات التحليل
            self.analysis_results['metadata'] = {
                'timestamp': timestamp,
                'analyzer_version': '1.0',
                'project_path': self.project_path,
                'analysis_date': datetime.now().isoformat()
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.analysis_results, f, ensure_ascii=False, indent=2)
            
            print(f"💾 تم حفظ نتائج التحليل في: {filename}")
            
        except Exception as e:
            print(f"❌ خطأ في حفظ النتائج: {e}")
    
    def display_final_report(self):
        """عرض التقرير النهائي"""
        print("\n" + "="*80)
        print("📊 التقرير النهائي لتحليل مشروع HORUS_AI_TEAM")
        print("="*80)
        
        # إحصائيات المشروع
        structure = self.analysis_results['structure']
        print(f"\n📈 إحصائيات المشروع:")
        print(f"   📁 المجلدات: {structure['total_directories']}")
        print(f"   📄 الملفات: {structure['total_files']}")
        print(f"   🐍 ملفات Python: {len(structure['python_files'])}")
        print(f"   📝 ملفات التوثيق: {len(structure['markdown_files'])}")
        print(f"   📋 ملفات JSON: {len(structure['json_files'])}")
        
        # تحليل البنية
        print(f"\n🏗️ تحليل البنية:")
        print("-" * 50)
        print(self.analysis_results.get('structure_analysis', 'غير متاح'))
        
        # تحليل الملفات الأساسية
        print(f"\n📋 تحليل الملفات الأساسية:")
        print("-" * 50)
        print(self.analysis_results.get('core_files', 'غير متاح'))
        
        # تحليل التوثيق
        print(f"\n📚 تحليل التوثيق:")
        print("-" * 50)
        print(self.analysis_results.get('documentation', 'غير متاح'))
        
        # التقييم الشامل
        print(f"\n🏆 التقييم الشامل:")
        print("-" * 50)
        print(self.analysis_results.get('overall_assessment', 'غير متاح'))
        
        print("\n" + "="*80)
        print("✅ تم إكمال التحليل الشامل لمشروع HORUS_AI_TEAM")
        print("="*80)

def main():
    """الدالة الرئيسية"""
    print("🔍 محلل مشروع حورس الشامل")
    print("تحليل شامل لمجلد HORUS_AI_TEAM بمساعدة نظام حورس")
    print("="*70)
    
    try:
        analyzer = HorusProjectAnalyzer()
        
        if analyzer.horus_system:
            analyzer.comprehensive_analysis()
        else:
            print("❌ لا يمكن تشغيل التحليل بدون نظام حورس")
            
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف التحليل بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في التحليل: {e}")

if __name__ == "__main__":
    main()