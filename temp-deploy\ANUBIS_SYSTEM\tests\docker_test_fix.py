#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🐳 اختبار Docker المحسن لنظام أنوبيس
Enhanced Docker Testing for Anubis System
"""

import os
import json
import subprocess
import time
from datetime import datetime

class DockerTestFix:
    def __init__(self):
        self.anubis_path = "ANUBIS_SYSTEM"
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
    def test_docker_comprehensive(self):
        """اختبار Docker الشامل المحسن"""
        results = {
            "docker_tests": [],
            "docker_available": False,
            "docker_version": "",
            "compose_available": False,
            "compose_version": "",
            "dockerfiles_found": 0,
            "compose_files_found": 0,
            "containers_status": {},
            "images_available": [],
            "success": False
        }
        
        print("🐳 اختبار Docker الشامل المحسن...")
        print("-" * 50)
        
        # 1. اختبار توفر Docker
        try:
            docker_version_result = subprocess.run(
                ["docker", "--version"], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            
            if docker_version_result.returncode == 0:
                results["docker_available"] = True
                results["docker_version"] = docker_version_result.stdout.strip()
                results["docker_tests"].append(f"✅ Docker متاح: {results['docker_version']}")
                print(f"✅ Docker متاح: {results['docker_version']}")
            else:
                results["docker_tests"].append("❌ Docker غير متاح")
                print("❌ Docker غير متاح")
                return results
                
        except subprocess.TimeoutExpired:
            results["docker_tests"].append("❌ Docker: timeout")
            print("❌ Docker: timeout")
            return results
        except FileNotFoundError:
            results["docker_tests"].append("❌ Docker: أمر غير موجود")
            print("❌ Docker: أمر غير موجود")
            return results
        except Exception as e:
            results["docker_tests"].append(f"❌ Docker: خطأ - {str(e)}")
            print(f"❌ Docker: خطأ - {str(e)}")
            return results
        
        # 2. اختبار Docker Compose
        try:
            compose_version_result = subprocess.run(
                ["docker-compose", "--version"], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            
            if compose_version_result.returncode == 0:
                results["compose_available"] = True
                results["compose_version"] = compose_version_result.stdout.strip()
                results["docker_tests"].append(f"✅ Docker Compose متاح: {results['compose_version']}")
                print(f"✅ Docker Compose متاح: {results['compose_version']}")
            else:
                # محاولة docker compose (الإصدار الجديد)
                compose_v2_result = subprocess.run(
                    ["docker", "compose", "version"], 
                    capture_output=True, 
                    text=True, 
                    timeout=10
                )
                if compose_v2_result.returncode == 0:
                    results["compose_available"] = True
                    results["compose_version"] = compose_v2_result.stdout.strip()
                    results["docker_tests"].append(f"✅ Docker Compose V2 متاح: {results['compose_version']}")
                    print(f"✅ Docker Compose V2 متاح: {results['compose_version']}")
                else:
                    results["docker_tests"].append("⚠️ Docker Compose غير متاح")
                    print("⚠️ Docker Compose غير متاح")
                    
        except Exception as e:
            results["docker_tests"].append(f"⚠️ Docker Compose: {str(e)}")
            print(f"⚠️ Docker Compose: {str(e)}")
        
        # 3. فحص ملفات Docker في المشروع
        docker_files = []
        compose_files = []
        
        # البحث في مجلد أنوبيس
        for root, dirs, files in os.walk(self.anubis_path):
            for file in files:
                if file.startswith("Dockerfile"):
                    docker_files.append(os.path.join(root, file))
                elif file.startswith("docker-compose") and file.endswith(('.yml', '.yaml')):
                    compose_files.append(os.path.join(root, file))
        
        results["dockerfiles_found"] = len(docker_files)
        results["compose_files_found"] = len(compose_files)
        
        results["docker_tests"].append(f"📄 وجد {len(docker_files)} Dockerfile")
        results["docker_tests"].append(f"📄 وجد {len(compose_files)} docker-compose file")
        print(f"📄 وجد {len(docker_files)} Dockerfile")
        print(f"📄 وجد {len(compose_files)} docker-compose file")
        
        # عرض الملفات الموجودة
        for dockerfile in docker_files:
            rel_path = os.path.relpath(dockerfile)
            results["docker_tests"].append(f"   📄 {rel_path}")
            print(f"   📄 {rel_path}")
            
        for composefile in compose_files:
            rel_path = os.path.relpath(composefile)
            results["docker_tests"].append(f"   📄 {rel_path}")
            print(f"   📄 {rel_path}")
        
        # 4. اختبار حالة Docker daemon
        try:
            docker_info_result = subprocess.run(
                ["docker", "info"], 
                capture_output=True, 
                text=True, 
                timeout=15
            )
            
            if docker_info_result.returncode == 0:
                results["docker_tests"].append("✅ Docker daemon يعمل بنجاح")
                print("✅ Docker daemon يعمل بنجاح")
                
                # استخراج معلومات مفيدة
                info_lines = docker_info_result.stdout.split('\n')
                for line in info_lines:
                    if 'Containers:' in line:
                        results["docker_tests"].append(f"   📊 {line.strip()}")
                        print(f"   📊 {line.strip()}")
                    elif 'Images:' in line:
                        results["docker_tests"].append(f"   📊 {line.strip()}")
                        print(f"   📊 {line.strip()}")
                        
            else:
                results["docker_tests"].append("❌ Docker daemon لا يعمل")
                print("❌ Docker daemon لا يعمل")
                
        except Exception as e:
            results["docker_tests"].append(f"⚠️ لا يمكن الوصول لـ Docker daemon: {str(e)}")
            print(f"⚠️ لا يمكن الوصول لـ Docker daemon: {str(e)}")
        
        # 5. اختبار الصور المتاحة
        try:
            images_result = subprocess.run(
                ["docker", "images", "--format", "table {{.Repository}}:{{.Tag}}"], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            
            if images_result.returncode == 0:
                images_lines = images_result.stdout.strip().split('\n')[1:]  # تجاهل العنوان
                results["images_available"] = [img.strip() for img in images_lines if img.strip()]
                
                if results["images_available"]:
                    results["docker_tests"].append(f"🖼️ وجد {len(results['images_available'])} صورة Docker")
                    print(f"🖼️ وجد {len(results['images_available'])} صورة Docker")
                    
                    # عرض أول 5 صور
                    for img in results["images_available"][:5]:
                        results["docker_tests"].append(f"   🖼️ {img}")
                        print(f"   🖼️ {img}")
                        
                    if len(results["images_available"]) > 5:
                        results["docker_tests"].append(f"   ... و {len(results['images_available']) - 5} صورة أخرى")
                        print(f"   ... و {len(results['images_available']) - 5} صورة أخرى")
                else:
                    results["docker_tests"].append("📭 لا توجد صور Docker محلية")
                    print("📭 لا توجد صور Docker محلية")
                    
        except Exception as e:
            results["docker_tests"].append(f"⚠️ لا يمكن قراءة صور Docker: {str(e)}")
            print(f"⚠️ لا يمكن قراءة صور Docker: {str(e)}")
        
        # 6. اختبار الحاويات الحالية
        try:
            containers_result = subprocess.run(
                ["docker", "ps", "-a", "--format", "table {{.Names}}\t{{.Status}}"], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            
            if containers_result.returncode == 0:
                container_lines = containers_result.stdout.strip().split('\n')[1:]  # تجاهل العنوان
                
                if container_lines and container_lines[0].strip():
                    results["docker_tests"].append(f"📦 وجد {len(container_lines)} حاوية")
                    print(f"📦 وجد {len(container_lines)} حاوية")
                    
                    for container_line in container_lines[:5]:  # أول 5 حاويات
                        if container_line.strip():
                            parts = container_line.split('\t')
                            if len(parts) >= 2:
                                name, status = parts[0].strip(), parts[1].strip()
                                results["containers_status"][name] = status
                                results["docker_tests"].append(f"   📦 {name}: {status}")
                                print(f"   📦 {name}: {status}")
                else:
                    results["docker_tests"].append("📭 لا توجد حاويات Docker")
                    print("📭 لا توجد حاويات Docker")
                    
        except Exception as e:
            results["docker_tests"].append(f"⚠️ لا يمكن قراءة حاويات Docker: {str(e)}")
            print(f"⚠️ لا يمكن قراءة حاويات Docker: {str(e)}")
        
        # 7. تقييم النتائج النهائية
        success_factors = []
        
        if results["docker_available"]:
            success_factors.append("Docker متاح")
        
        if results["compose_available"]:
            success_factors.append("Docker Compose متاح")
        
        if results["dockerfiles_found"] > 0:
            success_factors.append(f"{results['dockerfiles_found']} Dockerfile")
        
        if results["compose_files_found"] > 0:
            success_factors.append(f"{results['compose_files_found']} compose files")
        
        # تحديد النجاح
        if len(success_factors) >= 3:
            results["success"] = True
            status = "✅ نجح"
        elif len(success_factors) >= 2:
            results["warning"] = True
            status = "⚠️ تحذير"
        else:
            results["success"] = False
            status = "❌ فشل"
        
        results["summary"] = f"Docker: {', '.join(success_factors)}"
        
        print(f"\n🎯 النتيجة النهائية: {status}")
        print(f"📋 الملخص: {results['summary']}")
        
        return results
    
    def save_results(self, results):
        """حفظ نتائج الاختبار"""
        filename = f"docker_test_results_{self.timestamp}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 تم حفظ نتائج اختبار Docker: {filename}")
        return filename

def main():
    """الدالة الرئيسية"""
    print("🐳 بدء اختبار Docker المحسن لنظام أنوبيس")
    print("=" * 60)
    
    tester = DockerTestFix()
    results = tester.test_docker_comprehensive()
    
    if results:
        filename = tester.save_results(results)
        
        print("\n" + "=" * 60)
        print("🎉 تم إكمال اختبار Docker بنجاح!")
        
        if results.get("success"):
            print("✅ Docker يعمل بشكل مثالي مع نظام أنوبيس")
        elif results.get("warning"):
            print("⚠️ Docker يعمل مع بعض التحذيرات")
        else:
            print("❌ Docker يحتاج إعداد إضافي")
            
        return results
    else:
        print("❌ فشل في اختبار Docker")
        return None

if __name__ == "__main__":
    main()
