# 🚀 سكريبت PowerShell لتشغيل سيرفر أنوبيس المتكامل
# Anubis Integrated Server PowerShell Startup Script

param(
    [switch]$Build,
    [switch]$NoBuild,
    [switch]$Help
)

# ألوان للإخراج
$Colors = @{
    Red = "Red"
    Green = "Green"
    Yellow = "Yellow"
    Blue = "Blue"
    Magenta = "Magenta"
    Cyan = "Cyan"
    White = "White"
}

function Write-Header {
    Write-Host "================================" -ForegroundColor Magenta
    Write-Host "🏺 نظام أنوبيس المتكامل" -ForegroundColor Magenta
    Write-Host "Anubis Integrated System" -ForegroundColor Magenta
    Write-Host "================================" -ForegroundColor Magenta
}

function Write-Step {
    param($Message)
    Write-Host "📋 $Message" -ForegroundColor Blue
}

function Write-Success {
    param($Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Warning {
    param($Message)
    Write-Host "⚠️ $Message" -ForegroundColor Yellow
}

function Write-Error {
    param($Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

function Show-Help {
    Write-Host @"
🏺 سكريبت تشغيل نظام أنوبيس المتكامل

الاستخدام:
    .\start_anubis_server.ps1 [المعاملات]

المعاملات:
    -Build      بناء الصور من جديد
    -NoBuild    تشغيل بدون بناء الصور
    -Help       عرض هذه المساعدة

أمثلة:
    .\start_anubis_server.ps1                # تشغيل عادي
    .\start_anubis_server.ps1 -Build         # بناء وتشغيل
    .\start_anubis_server.ps1 -NoBuild       # تشغيل فقط
"@ -ForegroundColor Cyan
}

function Test-Requirements {
    Write-Step "فحص المتطلبات الأساسية..."
    
    # فحص Docker
    try {
        $dockerVersion = docker --version
        Write-Success "Docker متوفر: $dockerVersion"
    }
    catch {
        Write-Error "Docker غير مثبت. يرجى تثبيت Docker Desktop أولاً."
        exit 1
    }
    
    # فحص Docker Compose
    try {
        $composeVersion = docker-compose --version
        Write-Success "Docker Compose متوفر: $composeVersion"
    }
    catch {
        Write-Error "Docker Compose غير مثبت."
        exit 1
    }
    
    # فحص وجود الملفات المطلوبة
    if (-not (Test-Path "docker-compose-full-server.yml")) {
        Write-Error "ملف docker-compose-full-server.yml غير موجود."
        exit 1
    }
    
    Write-Success "جميع المتطلبات متوفرة"
}

function New-Directories {
    Write-Step "إنشاء المجلدات المطلوبة..."
    
    $directories = @(
        "database",
        "nginx\ssl",
        "monitoring\grafana\dashboards",
        "monitoring\grafana\datasources",
        "logs",
        "data"
    )
    
    foreach ($dir in $directories) {
        if (-not (Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
        }
    }
    
    Write-Success "تم إنشاء المجلدات"
}

function New-MissingConfigs {
    Write-Step "إنشاء ملفات الإعداد المفقودة..."
    
    # إنشاء ملف .env إذا لم يكن موجود
    if (-not (Test-Path ".env")) {
        $envContent = @"
# متغيرات البيئة لنظام أنوبيس
POSTGRES_DB=anubis_db
POSTGRES_USER=anubis
POSTGRES_PASSWORD=anubis123
REDIS_PASSWORD=anubis_redis_pass
GRAFANA_ADMIN_PASSWORD=anubis123
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=anubis123
ANUBIS_SECRET_KEY=anubis_super_secret_key_2024
MCP_SECRET_KEY=mcp_secret_key_2024
HORUS_SECRET_KEY=horus_secret_key_2024
"@
        $envContent | Out-File -FilePath ".env" -Encoding UTF8
        Write-Success "تم إنشاء ملف .env"
    }
    
    # إنشاء ملف Grafana datasource
    $datasourceContent = @"
apiVersion: 1
datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
"@
    $datasourceContent | Out-File -FilePath "monitoring\grafana\datasources\prometheus.yml" -Encoding UTF8
    
    Write-Success "تم إنشاء ملفات الإعداد"
}

function Build-Images {
    Write-Step "بناء صور Docker..."
    
    try {
        docker-compose -f docker-compose-full-server.yml build --no-cache
        Write-Success "تم بناء الصور بنجاح"
    }
    catch {
        Write-Error "فشل في بناء الصور: $_"
        exit 1
    }
}

function Start-Services {
    Write-Step "تشغيل الخدمات..."
    
    try {
        # تشغيل البنية التحتية أولاً
        Write-Step "تشغيل البنية التحتية (قواعد البيانات، Redis)..."
        docker-compose -f docker-compose-full-server.yml up -d anubis-db anubis-redis
        
        # انتظار حتى تصبح قواعد البيانات جاهزة
        Write-Step "انتظار جاهزية قواعد البيانات..."
        Start-Sleep -Seconds 30
        
        # تشغيل الخدمات الأساسية
        Write-Step "تشغيل الخدمات الأساسية..."
        docker-compose -f docker-compose-full-server.yml up -d anubis-core anubis-mcp horus-team
        
        # انتظار حتى تصبح الخدمات جاهزة
        Write-Step "انتظار جاهزية الخدمات الأساسية..."
        Start-Sleep -Seconds 20
        
        # تشغيل الواجهة والمراقبة
        Write-Step "تشغيل الواجهة والمراقبة..."
        docker-compose -f docker-compose-full-server.yml up -d anubis-web-client nginx prometheus grafana
        
        # تشغيل الخدمات الإضافية
        Write-Step "تشغيل الخدمات الإضافية..."
        docker-compose -f docker-compose-full-server.yml up -d anubis-n8n elasticsearch kibana
        
        Write-Success "تم تشغيل جميع الخدمات"
    }
    catch {
        Write-Error "فشل في تشغيل الخدمات: $_"
        exit 1
    }
}

function Test-Services {
    Write-Step "فحص حالة الخدمات..."
    
    Start-Sleep -Seconds 10
    
    # قائمة الخدمات للفحص
    $services = @(
        @{Url="http://localhost:8000/health"; Name="نظام أنوبيس"},
        @{Url="http://localhost:3000/health"; Name="نظام MCP"},
        @{Url="http://localhost:7000/health"; Name="فريق حورس"},
        @{Url="http://localhost:5000"; Name="الواجهة الموحدة"},
        @{Url="http://localhost:9090"; Name="Prometheus"},
        @{Url="http://localhost:3001"; Name="Grafana"},
        @{Url="http://localhost:5678"; Name="N8N"}
    )
    
    Write-Host "📊 حالة الخدمات:" -ForegroundColor Cyan
    
    foreach ($service in $services) {
        try {
            $response = Invoke-WebRequest -Uri $service.Url -TimeoutSec 5 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Write-Host "✅ $($service.Name): متاح" -ForegroundColor Green
            } else {
                Write-Host "❌ $($service.Name): غير متاح" -ForegroundColor Red
            }
        }
        catch {
            Write-Host "❌ $($service.Name): غير متاح" -ForegroundColor Red
        }
    }
}

function Show-AccessInfo {
    Write-Step "معلومات الوصول للخدمات:"
    
    Write-Host "🌐 الواجهات المتاحة:" -ForegroundColor Cyan
    Write-Host "🏺 الواجهة الرئيسية: http://localhost" -ForegroundColor Green
    Write-Host "🏺 نظام أنوبيس: http://localhost/anubis" -ForegroundColor Green
    Write-Host "🔗 نظام MCP: http://localhost/mcp" -ForegroundColor Green
    Write-Host "𓅃 فريق حورس: http://localhost/horus" -ForegroundColor Green
    Write-Host "📊 Grafana: http://localhost:3001 (admin/anubis123)" -ForegroundColor Green
    Write-Host "🔄 N8N: http://localhost:5678 (admin/anubis123)" -ForegroundColor Green
    Write-Host "📈 Prometheus: http://localhost:9090" -ForegroundColor Green
    Write-Host "📝 Kibana: http://localhost:5601" -ForegroundColor Green
    
    Write-Host "🔧 أوامر مفيدة:" -ForegroundColor Cyan
    Write-Host "عرض السجلات: docker-compose -f docker-compose-full-server.yml logs -f" -ForegroundColor Yellow
    Write-Host "إيقاف الخدمات: docker-compose -f docker-compose-full-server.yml down" -ForegroundColor Yellow
    Write-Host "إعادة التشغيل: docker-compose -f docker-compose-full-server.yml restart" -ForegroundColor Yellow
}

function Main {
    if ($Help) {
        Show-Help
        return
    }
    
    Write-Header
    
    Test-Requirements
    New-Directories
    New-MissingConfigs
    
    # تحديد ما إذا كان سيتم البناء
    $shouldBuild = $false
    if ($Build) {
        $shouldBuild = $true
    } elseif (-not $NoBuild) {
        $response = Read-Host "هل تريد بناء الصور من جديد؟ (y/N)"
        if ($response -match "^[Yy]$") {
            $shouldBuild = $true
        }
    }
    
    if ($shouldBuild) {
        Build-Images
    }
    
    Start-Services
    Test-Services
    Show-AccessInfo
    
    Write-Success "تم تشغيل نظام أنوبيس المتكامل بنجاح!"
    Write-Warning "يرجى الانتظار بضع دقائق حتى تصبح جميع الخدمات جاهزة تماماً."
    
    # فتح المتصفح
    $openBrowser = Read-Host "هل تريد فتح الواجهة الرئيسية في المتصفح؟ (Y/n)"
    if ($openBrowser -notmatch "^[Nn]$") {
        Start-Process "http://localhost"
    }
}

# تشغيل الدالة الرئيسية
Main
