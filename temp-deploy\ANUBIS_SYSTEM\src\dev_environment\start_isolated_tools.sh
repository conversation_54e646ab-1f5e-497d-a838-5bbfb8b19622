#!/bin/bash
# سكريبت تشغيل أدوات أنوبيس المعزولة

echo "🛠️ بدء تشغيل أدوات أنوبيس المعزولة..."

# التحقق من المتطلبات
if ! command -v docker &> /dev/null; then
    echo "❌ Docker غير مثبت"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose غير مثبت"
    exit 1
fi

# الانتقال لمجلد الأدوات
cd tools_and_utilities

# إنشاء المجلدات المطلوبة
echo "📁 إنشاء مجلدات البيانات..."
mkdir -p data logs backups temp monitoring

# تعيين الصلاحيات الآمنة
echo "🔐 تعيين الصلاحيات..."
chmod 750 data logs backups
chmod 700 temp

# إنشاء ملف مراقبة Prometheus
mkdir -p monitoring
cat > monitoring/prometheus-tools.yml << EOF
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'anubis-tools'
    static_configs:
      - targets: ['anubis-tools:8080']
EOF

# بناء الحاويات
echo "🔨 بناء حاويات الأدوات..."
docker-compose build

# تشغيل النظام المعزول
echo "🚀 تشغيل الأدوات في بيئة معزولة..."
docker-compose up -d

# التحقق من الحالة
echo "🔍 فحص حالة الحاويات..."
sleep 15
docker-compose ps

echo ""
echo "✅ تم تشغيل أدوات أنوبيس في بيئة معزولة!"
echo "🌐 أدوات النظام: http://localhost:8080"
echo "📊 مراقبة الأدوات: http://localhost:9091"
echo ""
echo "📋 أوامر مفيدة:"
echo "   السجلات: docker-compose logs -f anubis-tools"
echo "   الحالة: docker-compose ps"
echo "   الدخول للحاوية: docker exec -it anubis-tools-isolated bash"
echo "   إيقاف النظام: docker-compose down"
