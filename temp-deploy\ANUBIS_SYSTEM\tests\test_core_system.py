#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 اختبارات النظام الأساسي لأنوبيس
Anubis Core System Tests

اختبارات شاملة للنظام الأساسي والوظائف الرئيسية
"""

import pytest
import sys
import os
from pathlib import Path
from fastapi.testclient import TestClient
import requests
import time

# إضافة مسار src إلى Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

class TestAnubisCore:
    """اختبارات النظام الأساسي"""
    
    @pytest.fixture(scope="class")
    def client(self):
        """إنشاء عميل اختبار FastAPI"""
        try:
            from core.main import app
            return TestClient(app)
        except ImportError:
            pytest.skip("لا يمكن استيراد النظام الأساسي")
    
    def test_main_import(self):
        """اختبار استيراد الملف الرئيسي"""
        try:
            from core.main import app
            assert app is not None
            assert hasattr(app, 'title')
            assert "أنوبيس" in app.title or "Anubis" in app.title
        except ImportError as e:
            pytest.fail(f"فشل في استيراد النظام الأساسي: {e}")
    
    def test_home_page(self, client):
        """اختبار الصفحة الرئيسية"""
        response = client.get("/")
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]
        
        # فحص وجود عناصر مهمة في الصفحة
        content = response.text
        assert "أنوبيس" in content or "Anubis" in content
        assert "html" in content.lower()
    
    def test_health_endpoint(self, client):
        """اختبار نقطة فحص الصحة"""
        response = client.get("/health")
        assert response.status_code == 200
        
        data = response.json()
        assert "status" in data
        assert data["status"] == "healthy"
        assert "timestamp" in data
    
    def test_api_info(self, client):
        """اختبار معلومات API"""
        response = client.get("/api/v1/info")
        assert response.status_code == 200
        
        data = response.json()
        assert "name" in data
        assert "version" in data
        assert "status" in data
    
    def test_generate_endpoint_validation(self, client):
        """اختبار التحقق من صحة نقطة الإنتاج"""
        # اختبار بدون prompt
        response = client.post("/api/v1/generate", json={})
        assert response.status_code == 400
        
        # اختبار مع prompt صحيح
        response = client.post("/api/v1/generate", json={
            "prompt": "مرحبا",
            "model": "test"
        })
        assert response.status_code == 200
        
        data = response.json()
        assert "response" in data
        assert "model" in data
        assert "prompt" in data

class TestProjectStructure:
    """اختبارات هيكل المشروع"""
    
    def test_required_directories(self):
        """اختبار وجود المجلدات المطلوبة"""
        required_dirs = [
            "src", "src/core", "src/cli", "config", 
            "data", "docs", "tests", "logs", "scripts"
        ]
        
        for dir_path in required_dirs:
            full_path = project_root / dir_path
            assert full_path.exists(), f"المجلد المطلوب غير موجود: {dir_path}"
            assert full_path.is_dir(), f"المسار ليس مجلد: {dir_path}"
    
    def test_required_files(self):
        """اختبار وجود الملفات المطلوبة"""
        required_files = [
            "main.py", "requirements.txt", "docker-compose.yml",
            "src/cli/anubis_cli.py", ".env.example"
        ]
        
        for file_path in required_files:
            full_path = project_root / file_path
            assert full_path.exists(), f"الملف المطلوب غير موجود: {file_path}"
            assert full_path.is_file(), f"المسار ليس ملف: {file_path}"
    
    def test_requirements_file(self):
        """اختبار ملف المتطلبات"""
        req_file = project_root / "requirements.txt"
        assert req_file.exists()
        
        content = req_file.read_text(encoding="utf-8")
        
        # فحص وجود المتطلبات الأساسية
        essential_packages = [
            "fastapi", "uvicorn", "pydantic", "requests", "click"
        ]
        
        for package in essential_packages:
            assert package in content, f"المتطلب الأساسي مفقود: {package}"

class TestCLI:
    """اختبارات واجهة سطر الأوامر"""
    
    def test_cli_import(self):
        """اختبار استيراد CLI"""
        try:
            from cli.anubis_cli import anubis
            assert anubis is not None
        except ImportError as e:
            pytest.fail(f"فشل في استيراد CLI: {e}")
    
    def test_cli_help(self):
        """اختبار مساعدة CLI"""
        import subprocess
        
        cli_path = project_root / "src" / "cli" / "anubis_cli.py"
        result = subprocess.run([
            sys.executable, str(cli_path), "--help"
        ], capture_output=True, text=True)
        
        assert result.returncode == 0
        assert "أنوبيس" in result.stdout or "Anubis" in result.stdout
        assert "Commands:" in result.stdout

class TestConfiguration:
    """اختبارات الإعدادات"""
    
    def test_config_directory(self):
        """اختبار مجلد الإعدادات"""
        config_dir = project_root / "config"
        assert config_dir.exists()
        assert config_dir.is_dir()
    
    def test_env_example(self):
        """اختبار ملف .env.example"""
        env_file = project_root / ".env.example"
        assert env_file.exists()
        
        content = env_file.read_text(encoding="utf-8")
        
        # فحص وجود متغيرات مهمة
        important_vars = [
            "ANUBIS_ENV", "SECRET_KEY", "DATABASE_URL", 
            "MYSQL_PASSWORD", "REDIS_PASSWORD"
        ]
        
        for var in important_vars:
            assert var in content, f"متغير البيئة المهم مفقود: {var}"

class TestDockerConfiguration:
    """اختبارات إعدادات Docker"""
    
    def test_docker_compose_file(self):
        """اختبار ملف docker-compose.yml"""
        compose_file = project_root / "docker-compose.yml"
        assert compose_file.exists()
        
        content = compose_file.read_text(encoding="utf-8")
        
        # فحص وجود الخدمات الأساسية
        essential_services = [
            "anubis-core", "anubis-mysql", "anubis-redis"
        ]
        
        for service in essential_services:
            assert service in content, f"الخدمة الأساسية مفقودة: {service}"
    
    def test_dockerfile(self):
        """اختبار ملف Dockerfile"""
        dockerfile = project_root / "Dockerfile"
        assert dockerfile.exists()
        
        content = dockerfile.read_text(encoding="utf-8")
        assert "FROM python:" in content
        assert "COPY requirements.txt" in content

class TestSystemHealth:
    """اختبارات صحة النظام"""
    
    def test_python_version(self):
        """اختبار إصدار Python"""
        assert sys.version_info >= (3, 8), "يتطلب Python 3.8 أو أحدث"
    
    def test_disk_space(self):
        """اختبار مساحة القرص"""
        import shutil
        
        total, used, free = shutil.disk_usage(project_root)
        free_gb = free // (1024**3)
        
        assert free_gb >= 1, f"مساحة قرص غير كافية: {free_gb}GB متوفرة"
    
    def test_memory_usage(self):
        """اختبار استخدام الذاكرة"""
        try:
            import psutil
            memory = psutil.virtual_memory()
            available_gb = memory.available // (1024**3)
            
            assert available_gb >= 1, f"ذاكرة غير كافية: {available_gb}GB متوفرة"
        except ImportError:
            pytest.skip("psutil غير متوفر")

# تشغيل الاختبارات
if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])
