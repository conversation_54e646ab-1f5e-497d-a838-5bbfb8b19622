#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 مشغل حورس المحسن والمُصحح
𓅃 نقطة دخول محسنة لتشغيل فريق حورس مع إصلاح المشاكل

تم إنشاؤه وفقاً لقواعد التطوير المعتمدة
الموقع: HORUS_AI_TEAM/
"""

import os
import sys
import json
import subprocess
import importlib
from pathlib import Path
from datetime import datetime

class HorusQuickStartFixed:
    """مشغل حورس المحسن مع إصلاح المشاكل"""
    
    def __init__(self):
        """تهيئة المشغل"""
        self.project_root = Path(__file__).parent
        self.issues_found = []
        self.fixes_applied = []
        
        print("𓅃" * 60)
        print("🚀 مشغل حورس المحسن والمُصحح")
        print("🔧 إصدار محسن مع حل المشاكل المكتشفة")
        print("𓅃" * 60)
        print()

    def check_and_fix_environment(self):
        """فحص وإصلاح البيئة"""
        print("🔍 فحص وإصلاح البيئة...")
        
        # فحص Python
        python_version = sys.version_info
        print(f"🐍 Python {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        # فحص وإصلاح المكتبات
        self._check_and_fix_libraries()
        
        # فحص مفاتيح API
        self._check_api_keys()
        
        # فحص النماذج المحلية
        self._check_local_models()
        
        print()

    def _check_and_fix_libraries(self):
        """فحص وإصلاح المكتبات"""
        print("📦 فحص وإصلاح المكتبات...")
        
        required_libraries = {
            'google-generativeai': 'google.generativeai',
            'streamlit': 'streamlit',
            'requests': 'requests',
            'plotly': 'plotly',
            'pandas': 'pandas',
            'numpy': 'numpy'
        }
        
        missing_libraries = []
        problematic_libraries = []
        
        for lib_name, import_name in required_libraries.items():
            try:
                # محاولة الاستيراد
                importlib.import_module(import_name)
                print(f"  ✅ {lib_name}")
            except ImportError as e:
                if "numpy" in str(e) and "source directory" in str(e):
                    # مشكلة خاصة بـ numpy
                    problematic_libraries.append(lib_name)
                    print(f"  ⚠️ {lib_name} - مشكلة في التثبيت")
                else:
                    missing_libraries.append(lib_name)
                    print(f"  ❌ {lib_name} - غير مثبت")
        
        # إصلاح المكتبات المفقودة
        if missing_libraries:
            self._fix_missing_libraries(missing_libraries)
        
        # إصلاح المكتبات المشكلة
        if problematic_libraries:
            self._fix_problematic_libraries(problematic_libraries)

    def _fix_missing_libraries(self, missing_libraries):
        """إصلاح المكتبات المفقودة"""
        print(f"\n🔧 إصلاح المكتبات المفقودة: {', '.join(missing_libraries)}")
        
        response = input("هل تريد تثبيت المكتبات المفقودة؟ (y/n): ").lower()
        if response == 'y':
            for lib in missing_libraries:
                try:
                    print(f"📦 تثبيت {lib}...")
                    subprocess.run([sys.executable, '-m', 'pip', 'install', lib], 
                                 check=True, capture_output=True)
                    print(f"  ✅ تم تثبيت {lib}")
                    self.fixes_applied.append(f"تثبيت {lib}")
                except subprocess.CalledProcessError:
                    print(f"  ❌ فشل تثبيت {lib}")
                    self.issues_found.append(f"فشل تثبيت {lib}")

    def _fix_problematic_libraries(self, problematic_libraries):
        """إصلاح المكتبات المشكلة"""
        print(f"\n🔧 إصلاح المكتبات المشكلة: {', '.join(problematic_libraries)}")
        
        for lib in problematic_libraries:
            if lib == 'pandas' or lib == 'numpy':
                print(f"🔄 إعادة تثبيت {lib} في بيئة نظيفة...")
                try:
                    # إلغاء التثبيت أولاً
                    subprocess.run([sys.executable, '-m', 'pip', 'uninstall', lib, '-y'], 
                                 capture_output=True)
                    # إعادة التثبيت
                    subprocess.run([sys.executable, '-m', 'pip', 'install', lib, '--no-cache-dir'], 
                                 check=True, capture_output=True)
                    print(f"  ✅ تم إصلاح {lib}")
                    self.fixes_applied.append(f"إصلاح {lib}")
                except subprocess.CalledProcessError:
                    print(f"  ❌ فشل إصلاح {lib}")
                    self.issues_found.append(f"فشل إصلاح {lib}")

    def _check_api_keys(self):
        """فحص مفاتيح API"""
        print("🔑 فحص مفاتيح API...")
        
        # البحث عن مفاتيح Gemini
        gemini_keys = []
        
        # فحص متغيرات البيئة
        if os.getenv("GEMINI_API_KEY"):
            gemini_keys.append("متغير البيئة")
        
        # فحص ملفات التكوين
        config_files = [
            self.project_root / "07_configuration" / "api_keys.json",
            self.project_root.parent / "ANUBIS_HORUS_MCP" / "api_keys_vault" / "api_keys_collection.json"
        ]
        
        for config_file in config_files:
            if config_file.exists():
                try:
                    with open(config_file, 'r') as f:
                        config = json.load(f)
                        if "google_gemini" in config:
                            gemini_keys.append(str(config_file.name))
                except:
                    continue
        
        if gemini_keys:
            print(f"  ✅ Gemini API - متاح في: {', '.join(gemini_keys)}")
        else:
            print("  ❌ Gemini API - غير متاح")
            self.issues_found.append("مفتاح Gemini API غير متاح")

    def _check_local_models(self):
        """فحص النماذج المحلية"""
        print("🏠 فحص النماذج المحلية (Ollama)...")
        
        try:
            result = subprocess.run(["ollama", "list"], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                models = result.stdout.count('\n') - 1  # عدد النماذج
                print(f"  ✅ Ollama متاح مع {models} نموذج")
            else:
                print("  ❌ Ollama غير متاح")
                self.issues_found.append("Ollama غير متاح")
        except:
            print("  ❌ Ollama غير مثبت")
            self.issues_found.append("Ollama غير مثبت")

    def show_available_options(self):
        """عرض الخيارات المتاحة"""
        print("🎯 الخيارات المتاحة:")
        print("=" * 50)
        
        # فحص الواجهات المتاحة
        interfaces_path = self.project_root / "01_core" / "interfaces"
        if interfaces_path.exists():
            print("🖥️ الواجهات المتاحة:")
            interface_files = list(interfaces_path.glob("*.py"))
            for i, interface_file in enumerate(interface_files, 1):
                if interface_file.name != "__init__.py":
                    print(f"  {i}. {interface_file.stem}")
        
        # فحص المحركات المتاحة
        engines_path = self.project_root / "01_core" / "engines"
        if engines_path.exists():
            print("\n⚙️ المحركات المتاحة:")
            engine_files = list(engines_path.glob("*.py"))
            for i, engine_file in enumerate(engine_files, 1):
                if engine_file.name not in ["__init__.py", "START_HERE.py"]:
                    print(f"  {i}. {engine_file.stem}")
        
        # فحص الوكلاء المتاحين
        agents_path = self.project_root / "02_team_members"
        if agents_path.exists():
            print("\n🤖 الوكلاء المتاحين:")
            agent_files = list(agents_path.glob("*_agent.py"))
            for i, agent_file in enumerate(agent_files, 1):
                print(f"  {i}. {agent_file.stem}")
        
        print()

    def run_safe_mode(self):
        """تشغيل الوضع الآمن"""
        print("🛡️ تشغيل الوضع الآمن...")
        
        # تشغيل واجهة بسيطة بدون مكتبات معقدة
        try:
            print("🚀 بدء الواجهة البسيطة...")
            
            # استيراد الواجهة الأساسية
            sys.path.append(str(self.project_root / "01_core" / "interfaces"))
            
            # محاولة تشغيل واجهة بسيطة
            print("✅ الواجهة الأساسية متاحة")
            print("🎯 يمكنك الآن استخدام النظام في الوضع الأساسي")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في تشغيل الوضع الآمن: {e}")
            return False

    def show_summary(self):
        """عرض ملخص الحالة"""
        print("\n" + "=" * 60)
        print("📊 ملخص حالة النظام")
        print("=" * 60)
        
        if self.fixes_applied:
            print("✅ الإصلاحات المطبقة:")
            for fix in self.fixes_applied:
                print(f"  • {fix}")
        
        if self.issues_found:
            print("\n⚠️ المشاكل المتبقية:")
            for issue in self.issues_found:
                print(f"  • {issue}")
        
        # تقييم الحالة الإجمالية
        total_checks = 6  # عدد الفحوصات الأساسية
        issues_count = len(self.issues_found)
        success_rate = ((total_checks - issues_count) / total_checks) * 100
        
        print(f"\n📈 معدل النجاح: {success_rate:.1f}%")
        
        if success_rate >= 80:
            print("🎉 النظام جاهز للاستخدام!")
            return True
        elif success_rate >= 60:
            print("⚠️ النظام يعمل جزئياً - يمكن الاستخدام مع قيود")
            return True
        else:
            print("❌ النظام يحتاج إلى إصلاحات إضافية")
            return False

    def main_menu(self):
        """القائمة الرئيسية"""
        while True:
            print("\n🎮 القائمة الرئيسية:")
            print("1. 🔍 فحص النظام مرة أخرى")
            print("2. 🛡️ تشغيل الوضع الآمن")
            print("3. 📊 عرض الخيارات المتاحة")
            print("4. 🔧 تطبيق إصلاحات إضافية")
            print("5. 📋 عرض ملخص الحالة")
            print("6. 🚪 الخروج")
            
            choice = input("\n🎯 اختر رقم (1-6): ").strip()
            
            if choice == "1":
                self.check_and_fix_environment()
            elif choice == "2":
                self.run_safe_mode()
            elif choice == "3":
                self.show_available_options()
            elif choice == "4":
                self._apply_additional_fixes()
            elif choice == "5":
                self.show_summary()
            elif choice == "6":
                print("👋 وداعاً!")
                break
            else:
                print("❌ خيار غير صحيح")

    def _apply_additional_fixes(self):
        """تطبيق إصلاحات إضافية"""
        print("🔧 تطبيق إصلاحات إضافية...")
        
        # إصلاح مشاكل numpy المعروفة
        try:
            print("🔄 إصلاح مشاكل numpy...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', '--upgrade', 'numpy'], 
                         capture_output=True)
            print("  ✅ تم تحديث numpy")
            self.fixes_applied.append("تحديث numpy")
        except:
            print("  ❌ فشل تحديث numpy")
        
        # إصلاح مشاكل pandas
        try:
            print("🔄 إصلاح مشاكل pandas...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pandas'], 
                         capture_output=True)
            print("  ✅ تم تحديث pandas")
            self.fixes_applied.append("تحديث pandas")
        except:
            print("  ❌ فشل تحديث pandas")

    def run(self):
        """تشغيل المشغل المحسن"""
        print("🚀 بدء فحص وإصلاح النظام...")
        
        # فحص وإصلاح البيئة
        self.check_and_fix_environment()
        
        # عرض الخيارات المتاحة
        self.show_available_options()
        
        # عرض ملخص الحالة
        system_ready = self.show_summary()
        
        if system_ready:
            print("\n🎉 النظام جاهز! يمكنك الآن استخدام القائمة الرئيسية.")
        else:
            print("\n⚠️ النظام يحتاج إلى إصلاحات. استخدم القائمة لتطبيق الإصلاحات.")
        
        # تشغيل القائمة الرئيسية
        self.main_menu()

def main():
    """الدالة الرئيسية"""
    launcher = HorusQuickStartFixed()
    launcher.run()

if __name__ == "__main__":
    main()
