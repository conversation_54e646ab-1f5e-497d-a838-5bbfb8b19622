#!/usr/bin/env python3
"""
🔄 نظام الصيانة الآلي
Automated Maintenance System
"""

import os
import shutil
import json
from pathlib import Path
from datetime import datetime, timedelta

class MaintenanceSystem:
    def __init__(self):
        self.maintenance_log = Path("maintenance_log.json")
        self.config_file = Path("maintenance_config.json")
        self.load_config()
        
    def load_config(self):
        """تحميل إعدادات الصيانة"""
        default_config = {
            "cleanup": {
                "temp_files_older_than_days": 7,
                "log_files_older_than_days": 30,
                "backup_retention_days": 60
            },
            "optimization": {
                "defrag_enabled": False,
                "cache_cleanup": True,
                "index_rebuild": True
            },
            "monitoring": {
                "disk_usage_threshold": 85,
                "memory_usage_threshold": 90,
                "cpu_usage_threshold": 95
            }
        }
        
        if self.config_file.exists():
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
        else:
            self.config = default_config
            self.save_config()
    
    def save_config(self):
        """حفظ إعدادات الصيانة"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, ensure_ascii=False, indent=2)
    
    def cleanup_temp_files(self):
        """تنظيف الملفات المؤقتة"""
        print("🧹 تنظيف الملفات المؤقتة...")
        
        temp_dirs = [
            Path("temp"),
            Path("tmp"),
            Path("cache"),
            Path(".cache"),
            Path("__pycache__")
        ]
        
        cleaned_files = 0
        cleanup_threshold = datetime.now() - timedelta(
            days=self.config["cleanup"]["temp_files_older_than_days"]
        )
        
        for temp_dir in temp_dirs:
            if temp_dir.exists():
                for file_path in temp_dir.rglob("*"):
                    if file_path.is_file():
                        file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                        if file_time < cleanup_threshold:
                            try:
                                file_path.unlink()
                                cleaned_files += 1
                            except Exception as e:
                                print(f"⚠️ لا يمكن حذف {file_path}: {e}")
        
        self.log_maintenance_action("cleanup_temp_files", {
            "cleaned_files": cleaned_files,
            "threshold_days": self.config["cleanup"]["temp_files_older_than_days"]
        })
        
        return cleaned_files
    
    def cleanup_logs(self):
        """تنظيف ملفات السجلات القديمة"""
        print("📋 تنظيف ملفات السجلات...")
        
        log_patterns = ["*.log", "*.log.*", "*_log_*.json"]
        cleaned_logs = 0
        cleanup_threshold = datetime.now() - timedelta(
            days=self.config["cleanup"]["log_files_older_than_days"]
        )
        
        for pattern in log_patterns:
            for log_file in Path(".").rglob(pattern):
                if log_file.is_file():
                    file_time = datetime.fromtimestamp(log_file.stat().st_mtime)
                    if file_time < cleanup_threshold:
                        try:
                            log_file.unlink()
                            cleaned_logs += 1
                        except Exception as e:
                            print(f"⚠️ لا يمكن حذف {log_file}: {e}")
        
        self.log_maintenance_action("cleanup_logs", {
            "cleaned_logs": cleaned_logs,
            "threshold_days": self.config["cleanup"]["log_files_older_than_days"]
        })
        
        return cleaned_logs
    
    def optimize_system(self):
        """تحسين النظام"""
        print("⚡ تحسين النظام...")
        
        optimizations = []
        
        # تنظيف ذاكرة التخزين المؤقت
        if self.config["optimization"]["cache_cleanup"]:
            cache_cleaned = self.cleanup_cache()
            optimizations.append(f"تنظيف Cache: {cache_cleaned} ملف")
        
        # فحص سلامة الملفات
        integrity_issues = self.check_file_integrity()
        if integrity_issues:
            optimizations.append(f"مشاكل السلامة: {len(integrity_issues)}")
        
        self.log_maintenance_action("system_optimization", {
            "optimizations": optimizations,
            "integrity_issues": len(integrity_issues)
        })
        
        return optimizations
    
    def cleanup_cache(self):
        """تنظيف ذاكرة التخزين المؤقت"""
        cache_dirs = [".cache", "cache", "__pycache__"]
        cleaned = 0
        
        for cache_dir in cache_dirs:
            cache_path = Path(cache_dir)
            if cache_path.exists():
                for cache_file in cache_path.rglob("*"):
                    if cache_file.is_file():
                        try:
                            cache_file.unlink()
                            cleaned += 1
                        except:
                            pass
        
        return cleaned
    
    def check_file_integrity(self):
        """فحص سلامة الملفات"""
        issues = []
        
        # فحص ملفات التكوين المهمة
        important_files = [
            "configs/ai_config.json",
            "configs/database_config.json",
            "configs/default_config.json"
        ]
        
        for file_path in important_files:
            path = Path(file_path)
            if path.exists():
                try:
                    with open(path, 'r', encoding='utf-8') as f:
                        json.load(f)  # محاولة قراءة JSON
                except json.JSONDecodeError:
                    issues.append(f"ملف JSON تالف: {file_path}")
                except Exception as e:
                    issues.append(f"مشكلة في {file_path}: {e}")
            else:
                issues.append(f"ملف مفقود: {file_path}")
        
        return issues
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        print("💾 إنشاء نسخة احتياطية...")
        
        backup_dir = Path("backups") / datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        # نسخ احتياطية للملفات المهمة
        important_dirs = ["configs", "src", "database"]
        backed_up_files = 0
        
        for dir_name in important_dirs:
            source_dir = Path(dir_name)
            if source_dir.exists():
                target_dir = backup_dir / dir_name
                try:
                    shutil.copytree(source_dir, target_dir)
                    backed_up_files += len(list(target_dir.rglob("*")))
                except Exception as e:
                    print(f"⚠️ خطأ في نسخ {dir_name}: {e}")
        
        self.log_maintenance_action("backup_creation", {
            "backup_location": str(backup_dir),
            "backed_up_files": backed_up_files
        })
        
        return str(backup_dir)
    
    def run_full_maintenance(self):
        """تشغيل صيانة شاملة"""
        print("🔄 بدء الصيانة الشاملة...")
        
        start_time = datetime.now()
        
        # تنفيذ جميع عمليات الصيانة
        temp_cleaned = self.cleanup_temp_files()
        logs_cleaned = self.cleanup_logs()
        optimizations = self.optimize_system()
        backup_location = self.create_backup()
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        summary = {
            "start_time": start_time.isoformat(),
            "end_time": end_time.isoformat(),
            "duration_seconds": duration,
            "temp_files_cleaned": temp_cleaned,
            "log_files_cleaned": logs_cleaned,
            "optimizations": optimizations,
            "backup_location": backup_location
        }
        
        self.log_maintenance_action("full_maintenance", summary)
        
        print(f"\n✅ انتهت الصيانة الشاملة:")
        print(f"   🧹 ملفات مؤقتة: {temp_cleaned}")
        print(f"   📋 ملفات سجلات: {logs_cleaned}")
        print(f"   ⚡ تحسينات: {len(optimizations)}")
        print(f"   💾 نسخة احتياطية: {backup_location}")
        print(f"   ⏱️ المدة: {duration:.1f} ثانية")
        
        return summary
    
    def log_maintenance_action(self, action, details):
        """تسجيل عمل الصيانة"""
        log_entry = {
            "action": action,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        
        logs = []
        if self.maintenance_log.exists():
            with open(self.maintenance_log, 'r', encoding='utf-8') as f:
                logs = json.load(f)
        
        logs.append(log_entry)
        
        with open(self.maintenance_log, 'w', encoding='utf-8') as f:
            json.dump(logs, f, ensure_ascii=False, indent=2)

if __name__ == "__main__":
    maintenance = MaintenanceSystem()
    maintenance.run_full_maintenance()
