#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🤖 طلب مساعدة من فريق حورس لفحص وتحليل مجلد ANUBIS_HORUS_MCP
"""

import os
import json
import subprocess
from datetime import datetime

class HorusAnalysisRequest:
    def __init__(self):
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.analysis_results = {}
        
    def analyze_anubis_mcp_structure(self):
        """تحليل هيكل مجلد ANUBIS_HORUS_MCP"""
        print("🔍 بدء تحليل مجلد ANUBIS_HORUS_MCP...")
        
        mcp_path = "ANUBIS_HORUS_MCP"
        if not os.path.exists(mcp_path):
            print(f"❌ المجلد غير موجود: {mcp_path}")
            return
            
        # فحص الملفات والمجلدات
        structure = self.scan_directory_structure(mcp_path)
        
        # تحليل المكونات الرئيسية
        components = self.analyze_main_components(mcp_path)
        
        # فحص حالة النظام
        system_status = self.check_system_status(mcp_path)
        
        # إنشاء تقرير شامل
        report = self.generate_comprehensive_report(structure, components, system_status)
        
        return report
    
    def scan_directory_structure(self, path):
        """فحص هيكل المجلد"""
        structure = {
            "total_files": 0,
            "total_directories": 0,
            "file_types": {},
            "main_directories": [],
            "key_files": []
        }
        
        for root, dirs, files in os.walk(path):
            structure["total_directories"] += len(dirs)
            structure["total_files"] += len(files)
            
            # تحليل أنواع الملفات
            for file in files:
                ext = os.path.splitext(file)[1].lower()
                if ext:
                    structure["file_types"][ext] = structure["file_types"].get(ext, 0) + 1
                
                # تحديد الملفات المهمة
                if file in ["package.json", "requirements_mcp.txt", "mcp_server.py", "README.md"]:
                    structure["key_files"].append(os.path.join(root, file))
            
            # تحديد المجلدات الرئيسية
            if root == path:
                structure["main_directories"] = dirs
        
        return structure
    
    def analyze_main_components(self, path):
        """تحليل المكونات الرئيسية"""
        components = {
            "api_keys_vault": self.analyze_api_vault(os.path.join(path, "api_keys_vault")),
            "core_system": self.analyze_core_system(os.path.join(path, "core")),
            "horus_integration": self.analyze_horus_integration(os.path.join(path, "horus_integration")),
            "nodejs_setup": self.analyze_nodejs_setup(path),
            "configuration": self.analyze_configuration(os.path.join(path, "config"))
        }
        return components
    
    def analyze_api_vault(self, vault_path):
        """تحليل نظام إدارة مفاتيح API"""
        if not os.path.exists(vault_path):
            return {"status": "غير موجود", "files": 0}
        
        files = os.listdir(vault_path)
        python_files = [f for f in files if f.endswith('.py')]
        log_files = [f for f in files if f.endswith('.log')]
        
        return {
            "status": "موجود",
            "total_files": len(files),
            "python_modules": len(python_files),
            "log_files": len(log_files),
            "key_modules": python_files[:5]  # أول 5 ملفات
        }
    
    def analyze_core_system(self, core_path):
        """تحليل النظام الأساسي"""
        if not os.path.exists(core_path):
            return {"status": "غير موجود"}
        
        files = os.listdir(core_path)
        return {
            "status": "موجود",
            "files": files,
            "mcp_server_exists": "mcp_server.py" in files
        }
    
    def analyze_horus_integration(self, horus_path):
        """تحليل تكامل فريق حورس"""
        if not os.path.exists(horus_path):
            return {"status": "غير موجود"}
        
        files = os.listdir(horus_path)
        return {
            "status": "موجود",
            "files": files,
            "team_connector_exists": "team_connector.py" in files
        }
    
    def analyze_nodejs_setup(self, path):
        """تحليل إعداد Node.js"""
        package_json = os.path.join(path, "package.json")
        node_modules = os.path.join(path, "node_modules")
        
        setup = {
            "package_json_exists": os.path.exists(package_json),
            "node_modules_exists": os.path.exists(node_modules),
            "dependencies": []
        }
        
        if setup["package_json_exists"]:
            try:
                with open(package_json, 'r', encoding='utf-8') as f:
                    package_data = json.load(f)
                    setup["dependencies"] = list(package_data.get("dependencies", {}).keys())
            except:
                pass
        
        return setup
    
    def analyze_configuration(self, config_path):
        """تحليل ملفات التكوين"""
        if not os.path.exists(config_path):
            return {"status": "غير موجود"}
        
        files = os.listdir(config_path)
        return {
            "status": "موجود",
            "config_files": files,
            "mcp_config_exists": "mcp_config.json" in files
        }
    
    def check_system_status(self, path):
        """فحص حالة النظام"""
        status = {
            "readiness_score": 0,
            "issues": [],
            "recommendations": []
        }
        
        # فحص الملفات الأساسية
        essential_files = [
            "package.json",
            "core/mcp_server.py",
            "api_keys_vault/keys_manager.py",
            "requirements_mcp.txt"
        ]
        
        missing_files = []
        for file in essential_files:
            if not os.path.exists(os.path.join(path, file)):
                missing_files.append(file)
        
        if missing_files:
            status["issues"].append(f"ملفات أساسية مفقودة: {missing_files}")
            status["recommendations"].append("إضافة الملفات المفقودة")
        else:
            status["readiness_score"] += 30
        
        # فحص Node.js
        if os.path.exists(os.path.join(path, "node_modules")):
            status["readiness_score"] += 25
        else:
            status["issues"].append("node_modules غير موجود")
            status["recommendations"].append("تشغيل npm install")
        
        # فحص Python modules
        if os.path.exists(os.path.join(path, "api_keys_vault")):
            status["readiness_score"] += 25
        
        # فحص التكوين
        if os.path.exists(os.path.join(path, "config/mcp_config.json")):
            status["readiness_score"] += 20
        else:
            status["issues"].append("ملف التكوين مفقود")
            status["recommendations"].append("إنشاء ملف mcp_config.json")
        
        return status
    
    def generate_comprehensive_report(self, structure, components, status):
        """إنشاء تقرير شامل"""
        report = {
            "timestamp": self.timestamp,
            "project_name": "ANUBIS_HORUS_MCP",
            "analysis_summary": {
                "total_files": structure["total_files"],
                "total_directories": structure["total_directories"],
                "readiness_score": f"{status['readiness_score']}/100",
                "main_components": len([c for c in components.values() if c.get('status') == 'موجود'])
            },
            "detailed_structure": structure,
            "components_analysis": components,
            "system_status": status,
            "horus_consultation": self.request_horus_consultation(status)
        }
        
        return report
    
    def request_horus_consultation(self, status):
        """طلب استشارة من فريق حورس"""
        consultation = {
            "request_type": "تحليل وتقييم مشروع ANUBIS_HORUS_MCP",
            "current_score": status["readiness_score"],
            "issues_found": len(status["issues"]),
            "horus_recommendations": []
        }
        
        # محاكاة استشارة فريق حورس
        if status["readiness_score"] < 50:
            consultation["horus_recommendations"].extend([
                "🔧 PTAH يوصي: إصلاح الملفات المفقودة أولاً",
                "⚡ THOTH يحلل: النظام يحتاج تحسينات أساسية",
                "🎯 RA يخطط: وضع خطة تطوير مرحلية"
            ])
        elif status["readiness_score"] < 80:
            consultation["horus_recommendations"].extend([
                "💡 KHNUM يبتكر: إضافة ميزات جديدة للتميز",
                "👁️ SESHAT توثق: تحسين التوثيق والواجهات",
                "🔐 ANUBIS يؤمن: تعزيز الأمان والحماية"
            ])
        else:
            consultation["horus_recommendations"].extend([
                "𓅃 HORUS ينسق: النظام جاهز للإنتاج",
                "⚖️ MAAT تراجع: التأكد من الجودة الأخلاقية",
                "🚀 الفريق يوافق: جاهز للإطلاق!"
            ])
        
        return consultation
    
    def save_analysis_report(self, report):
        """حفظ تقرير التحليل"""
        filename = f"horus_anubis_mcp_analysis_{self.timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"📄 تم حفظ التقرير: {filename}")
        return filename
    
    def print_summary_report(self, report):
        """طباعة ملخص التقرير"""
        print("\n" + "="*60)
        print("🤖 تقرير تحليل فريق حورس لمشروع ANUBIS_HORUS_MCP")
        print("="*60)
        
        summary = report["analysis_summary"]
        print(f"📁 إجمالي الملفات: {summary['total_files']}")
        print(f"📂 إجمالي المجلدات: {summary['total_directories']}")
        print(f"🎯 درجة الجاهزية: {summary['readiness_score']}")
        print(f"🔧 المكونات النشطة: {summary['main_components']}")
        
        print("\n🔍 حالة المكونات الرئيسية:")
        for component, data in report["components_analysis"].items():
            status = data.get("status", "غير محدد")
            print(f"  • {component}: {status}")
        
        print("\n🤖 توصيات فريق حورس:")
        for rec in report["horus_consultation"]["horus_recommendations"]:
            print(f"  {rec}")
        
        if report["system_status"]["issues"]:
            print("\n⚠️ المشاكل المكتشفة:")
            for issue in report["system_status"]["issues"]:
                print(f"  • {issue}")
        
        if report["system_status"]["recommendations"]:
            print("\n💡 التوصيات للتحسين:")
            for rec in report["system_status"]["recommendations"]:
                print(f"  • {rec}")
        
        print("\n" + "="*60)

def main():
    """الدالة الرئيسية"""
    print("🤖 مرحباً! سأطلب مساعدة فريق حورس لفحص مجلد ANUBIS_HORUS_MCP")
    print("𓅃 بعين حورس الثاقبة، سنحلل المشروع بدقة...")
    
    analyzer = HorusAnalysisRequest()
    
    try:
        # تحليل المشروع
        report = analyzer.analyze_anubis_mcp_structure()
        
        # طباعة التقرير
        analyzer.print_summary_report(report)
        
        # حفظ التقرير
        filename = analyzer.save_analysis_report(report)
        
        print(f"\n✅ تم إكمال التحليل بنجاح!")
        print(f"📄 التقرير محفوظ في: {filename}")
        
        return report
        
    except Exception as e:
        print(f"❌ خطأ في التحليل: {str(e)}")
        return None

if __name__ == "__main__":
    main()
