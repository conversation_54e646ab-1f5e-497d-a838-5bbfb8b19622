#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 الاختبار النهائي الشامل لنظام حورس المتكامل
Final Comprehensive Test for Integrated Horus System
"""

import sys
import os
import json
import time
from datetime import datetime
from pathlib import Path

# إعداد المسارات
current_dir = Path(__file__).parent
horus_dir = current_dir / "HORUS_AI_TEAM"
anubis_dir = current_dir / "ANUBIS_SYSTEM"
mcp_dir = current_dir / "ANUBIS_HORUS_MCP"

class FinalSystemTest:
    def __init__(self):
        self.test_id = f"final_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.results = {
            "test_id": self.test_id,
            "start_time": datetime.now().isoformat(),
            "tests": {},
            "summary": {}
        }
        
    def print_header(self, title):
        """طباعة عنوان مع تنسيق جميل"""
        print(f"\n{'='*80}")
        print(f"🧪 {title}")
        print(f"{'='*80}")
        
    def test_horus_team(self):
        """اختبار فريق حورس"""
        self.print_header("اختبار فريق حورس")
        
        test_result = {
            "name": "HORUS AI Team Test",
            "status": "running",
            "details": {}
        }
        
        try:
            # فحص الهيكل
            if horus_dir.exists():
                print("✅ مجلد فريق حورس موجود")
                test_result["details"]["directory"] = "exists"
                
                # فحص المجلدات الفرعية
                expected_dirs = [
                    "01_core", "02_team_members", "03_memory_system",
                    "04_collaboration", "05_analysis", "06_documentation",
                    "07_configuration", "08_utilities", "09_archive"
                ]
                
                missing_dirs = []
                for dir_name in expected_dirs:
                    if not (horus_dir / dir_name).exists():
                        missing_dirs.append(dir_name)
                
                if not missing_dirs:
                    print("✅ جميع المجلدات الفرعية موجودة")
                    test_result["details"]["subdirectories"] = "complete"
                else:
                    print(f"⚠️ مجلدات مفقودة: {missing_dirs}")
                    test_result["details"]["subdirectories"] = f"missing: {missing_dirs}"
                
                # فحص الملفات الأساسية
                key_files = ["quick_start.py", "README.md"]
                for file_name in key_files:
                    if (horus_dir / file_name).exists():
                        print(f"✅ {file_name} موجود")
                    else:
                        print(f"❌ {file_name} مفقود")
                
                test_result["status"] = "passed"
                
            else:
                print("❌ مجلد فريق حورس غير موجود")
                test_result["status"] = "failed"
                test_result["details"]["directory"] = "missing"
                
        except Exception as e:
            print(f"❌ خطأ في اختبار فريق حورس: {e}")
            test_result["status"] = "error"
            test_result["details"]["error"] = str(e)
            
        self.results["tests"]["horus_team"] = test_result
        
    def test_anubis_system(self):
        """اختبار نظام أنوبيس"""
        self.print_header("اختبار نظام أنوبيس")
        
        test_result = {
            "name": "ANUBIS System Test",
            "status": "running",
            "details": {}
        }
        
        try:
            if anubis_dir.exists():
                print("✅ مجلد نظام أنوبيس موجود")
                test_result["details"]["directory"] = "exists"
                
                # فحص الملفات الأساسية
                key_files = ["main.py", "README.md", "requirements.txt"]
                for file_name in key_files:
                    if (anubis_dir / file_name).exists():
                        print(f"✅ {file_name} موجود")
                    else:
                        print(f"❌ {file_name} مفقود")
                
                test_result["status"] = "passed"
                
            else:
                print("❌ مجلد نظام أنوبيس غير موجود")
                test_result["status"] = "failed"
                test_result["details"]["directory"] = "missing"
                
        except Exception as e:
            print(f"❌ خطأ في اختبار نظام أنوبيس: {e}")
            test_result["status"] = "error"
            test_result["details"]["error"] = str(e)
            
        self.results["tests"]["anubis_system"] = test_result
        
    def test_mcp_system(self):
        """اختبار نظام MCP"""
        self.print_header("اختبار نظام MCP")
        
        test_result = {
            "name": "MCP System Test",
            "status": "running",
            "details": {}
        }
        
        try:
            if mcp_dir.exists():
                print("✅ مجلد نظام MCP موجود")
                test_result["details"]["directory"] = "exists"
                
                # فحص الملفات الأساسية
                key_files = [
                    "advanced_collaborative_system.py",
                    "package.json",
                    "README.md"
                ]
                
                for file_name in key_files:
                    if (mcp_dir / file_name).exists():
                        print(f"✅ {file_name} موجود")
                    else:
                        print(f"❌ {file_name} مفقود")
                
                test_result["status"] = "passed"
                
            else:
                print("❌ مجلد نظام MCP غير موجود")
                test_result["status"] = "failed"
                test_result["details"]["directory"] = "missing"
                
        except Exception as e:
            print(f"❌ خطأ في اختبار نظام MCP: {e}")
            test_result["status"] = "error"
            test_result["details"]["error"] = str(e)
            
        self.results["tests"]["mcp_system"] = test_result
        
    def test_collaborative_system(self):
        """اختبار النظام التعاوني"""
        self.print_header("اختبار النظام التعاوني المتقدم")
        
        test_result = {
            "name": "Advanced Collaborative System Test",
            "status": "running",
            "details": {}
        }
        
        try:
            collab_file = mcp_dir / "advanced_collaborative_system.py"
            if collab_file.exists():
                print("✅ ملف النظام التعاوني موجود")
                
                # قراءة الملف للتحقق من المحتوى
                with open(collab_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # فحص المكونات الأساسية
                required_components = [
                    "class AdvancedCollaborativeSystem",
                    "call_api_model",
                    "horus_team",
                    "api_models"
                ]
                
                missing_components = []
                for component in required_components:
                    if component not in content:
                        missing_components.append(component)
                
                if not missing_components:
                    print("✅ جميع المكونات الأساسية موجودة")
                    test_result["details"]["components"] = "complete"
                else:
                    print(f"⚠️ مكونات مفقودة: {missing_components}")
                    test_result["details"]["components"] = f"missing: {missing_components}"
                
                test_result["status"] = "passed"
                
            else:
                print("❌ ملف النظام التعاوني غير موجود")
                test_result["status"] = "failed"
                test_result["details"]["file"] = "missing"
                
        except Exception as e:
            print(f"❌ خطأ في اختبار النظام التعاوني: {e}")
            test_result["status"] = "error"
            test_result["details"]["error"] = str(e)
            
        self.results["tests"]["collaborative_system"] = test_result
        
    def generate_summary(self):
        """إنشاء ملخص النتائج"""
        self.print_header("ملخص نتائج الاختبار النهائي")
        
        total_tests = len(self.results["tests"])
        passed_tests = sum(1 for test in self.results["tests"].values() if test["status"] == "passed")
        failed_tests = sum(1 for test in self.results["tests"].values() if test["status"] == "failed")
        error_tests = sum(1 for test in self.results["tests"].values() if test["status"] == "error")
        
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        
        print(f"📊 إجمالي الاختبارات: {total_tests}")
        print(f"✅ نجح: {passed_tests}")
        print(f"❌ فشل: {failed_tests}")
        print(f"⚠️ خطأ: {error_tests}")
        print(f"📈 معدل النجاح: {success_rate:.1f}%")
        
        # تحديد الحالة العامة
        if success_rate >= 90:
            overall_status = "ممتاز"
            status_emoji = "🏆"
        elif success_rate >= 75:
            overall_status = "جيد جداً"
            status_emoji = "✅"
        elif success_rate >= 50:
            overall_status = "جيد"
            status_emoji = "⚠️"
        else:
            overall_status = "يحتاج تحسين"
            status_emoji = "❌"
            
        print(f"\n{status_emoji} الحالة العامة: {overall_status}")
        
        self.results["summary"] = {
            "total_tests": total_tests,
            "passed": passed_tests,
            "failed": failed_tests,
            "errors": error_tests,
            "success_rate": success_rate,
            "overall_status": overall_status,
            "end_time": datetime.now().isoformat()
        }
        
    def save_results(self):
        """حفظ النتائج في ملف JSON"""
        results_file = current_dir / f"final_test_results_{self.test_id}.json"
        
        try:
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, ensure_ascii=False, indent=2)
            print(f"\n💾 تم حفظ النتائج في: {results_file}")
        except Exception as e:
            print(f"❌ خطأ في حفظ النتائج: {e}")
            
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🚀 بدء الاختبار النهائي الشامل لنظام حورس المتكامل")
        print(f"🆔 معرف الاختبار: {self.test_id}")
        
        # تشغيل جميع الاختبارات
        self.test_horus_team()
        self.test_anubis_system()
        self.test_mcp_system()
        self.test_collaborative_system()
        
        # إنشاء الملخص
        self.generate_summary()
        
        # حفظ النتائج
        self.save_results()
        
        print("\n🎉 تم إكمال الاختبار النهائي الشامل!")

if __name__ == "__main__":
    tester = FinalSystemTest()
    tester.run_all_tests()
