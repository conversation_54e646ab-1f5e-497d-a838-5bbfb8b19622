
# إصلاح محسن لقاعدة البيانات
async def create_tables_enhanced(self, database: str = "sqlite"):
    """إنشاء الجداول مع دعم محسن لـ MySQL و SQLite"""
    
    # جداول SQLite
    sqlite_tables = {
        "users": '''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT TRUE
            )
        ''',
        # المزيد من الجداول...
    }
    
    # جداول MySQL
    mysql_tables = {
        "users": '''
            CREATE TABLE IF NOT EXISTS users (
                id INT PRIMARY KEY AUTO_INCREMENT,
                username VARCHAR(255) UNIQUE NOT NULL,
                email VARCHAR(255) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT TRUE
            )
        ''',
        # المزيد من الجداول...
    }
    
    tables_to_use = mysql_tables if database == "mysql" else sqlite_tables
    
    for table_name, sql in tables_to_use.items():
        try:
            await self.execute_query(sql, database=database)
            self.logger.info(f"✅ تم إنشاء جدول {table_name} في {database}")
        except Exception as e:
            self.logger.error(f"❌ خطأ في إنشاء جدول {table_name}: {e}")
