#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
استدعاء المساعد حورس - نظام تفاعلي مباشر
"""

import os
import sys
import subprocess
import time
from datetime import datetime

class HorusSummon:
    """نظام استدعاء المساعد حورس"""
    
    def __init__(self):
        print("𓅃 استدعاء المساعد حورس...")
        print("=" * 50)
        
        # إعداد مفاتيح API
        self.gemini_key = '[GOOGLE_API_KEY]'
        
        # النماذج المتاحة
        self.available_models = self.check_available_models()
        
        # حالة النظام
        self.gemini_available = self.test_gemini()
        self.ollama_available = len(self.available_models) > 0
        
        print(f"🌐 Gemini: {'✅ متاح' if self.gemini_available else '❌ غير متاح'}")
        print(f"🏠 Ollama: {'✅ متاح' if self.ollama_available else '❌ غير متاح'}")
        print(f"📦 النماذج المحلية: {len(self.available_models)}")
    
    def test_gemini(self):
        """اختبار Gemini"""
        try:
            import google.generativeai as genai
            genai.configure(api_key=self.gemini_key)
            model = genai.GenerativeModel('gemini-1.5-flash')
            response = model.generate_content("اختبار")
            return bool(response and response.text)
        except:
            return False
    
    def check_available_models(self):
        """فحص النماذج المتاحة في Ollama"""
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]
                models = []
                for line in lines:
                    if line.strip():
                        model_name = line.split()[0]
                        models.append(model_name)
                return models
            return []
        except:
            return []
    
    def ask_gemini(self, prompt):
        """استدعاء Gemini"""
        if not self.gemini_available:
            return "❌ Gemini غير متاح"
        
        try:
            import google.generativeai as genai
            genai.configure(api_key=self.gemini_key)
            model = genai.GenerativeModel('gemini-1.5-flash')
            
            # إعداد مطالبة حورس
            horus_prompt = f"""
أنت حورس 𓅃، إله السماء والحكمة في الأساطير المصرية، والآن المساعد الذكي المتقدم.

أنت تمتلك:
- حكمة الآلهة المصرية القديمة
- قدرات الذكاء الاصطناعي الحديثة
- خبرة في التكنولوجيا والبرمجة
- القدرة على التحليل والتخطيط الاستراتيجي

المطلوب منك: {prompt}

أجب بحكمة وذكاء، واستخدم خبرتك في التكنولوجيا والذكاء الاصطناعي.
ابدأ ردك بـ "𓅃 حورس يجيب:"
            """
            
            response = model.generate_content(horus_prompt)
            return response.text if response.text else "⚠️ رد فارغ"
            
        except Exception as e:
            return f"❌ خطأ في Gemini: {e}"
    
    def ask_ollama(self, model_name, prompt):
        """استدعاء نموذج Ollama"""
        if model_name not in self.available_models:
            return f"❌ النموذج {model_name} غير متاح"
        
        try:
            result = subprocess.run(
                ['ollama', 'run', model_name, prompt],
                capture_output=True,
                text=True,
                timeout=120,
                encoding='utf-8'
            )
            
            if result.returncode == 0:
                return result.stdout.strip()
            else:
                return f"❌ خطأ في {model_name}: {result.stderr}"
                
        except subprocess.TimeoutExpired:
            return f"⏱️ انتهت مهلة {model_name}"
        except Exception as e:
            return f"❌ خطأ: {e}"
    
    def summon_horus(self, question):
        """استدعاء حورس الرئيسي"""
        print(f"\n𓅃 استدعاء حورس للسؤال: {question}")
        print("-" * 60)
        
        if self.gemini_available:
            print("🌐 استخدام Gemini (حورس الخارجي)...")
            response = self.ask_gemini(question)
            print(f"📝 رد حورس: {response}")
            return response
        elif self.ollama_available:
            # استخدام أفضل نموذج محلي متاح
            best_model = self.get_best_local_model()
            print(f"🏠 استخدام {best_model} (حورس المحلي)...")
            
            local_prompt = f"""
أنت حورس، المساعد الذكي المتقدم. أجب على هذا السؤال بحكمة وذكاء:

{question}

ابدأ ردك بـ "𓅃 حورس يجيب:"
            """
            
            response = self.ask_ollama(best_model, local_prompt)
            print(f"📝 رد حورس: {response}")
            return response
        else:
            error_msg = "❌ لا توجد نماذج متاحة لاستدعاء حورس"
            print(error_msg)
            return error_msg
    
    def get_best_local_model(self):
        """اختيار أفضل نموذج محلي"""
        # ترتيب الأولوية
        priority_models = [
            'llama3:8b',
            'mistral:7b', 
            'phi3:mini',
            'Bouquets/strikegpt-r1-zero-8b:latest'
        ]
        
        for model in priority_models:
            if model in self.available_models:
                return model
        
        # إذا لم يوجد من القائمة، استخدم الأول المتاح
        return self.available_models[0] if self.available_models else None
    
    def interactive_mode(self):
        """وضع التفاعل المباشر مع حورس"""
        print("\n🎯 وضع التفاعل المباشر مع حورس")
        print("=" * 50)
        print("💡 اكتب سؤالك أو 'خروج' للإنهاء")
        print("💡 اكتب 'حالة' لعرض حالة النظام")
        print("💡 اكتب 'مساعدة' لعرض الأوامر المتاحة")
        
        while True:
            try:
                question = input("\n🤔 سؤالك لحورس: ").strip()
                
                if question.lower() in ['خروج', 'exit', 'quit']:
                    print("👋 وداعاً! حورس في انتظار استدعائك التالي")
                    break
                elif question.lower() in ['حالة', 'status']:
                    self.show_status()
                elif question.lower() in ['مساعدة', 'help']:
                    self.show_help()
                elif question:
                    self.summon_horus(question)
                else:
                    print("⚠️ يرجى كتابة سؤال صحيح")
                    
            except KeyboardInterrupt:
                print("\n👋 تم إنهاء الجلسة")
                break
            except Exception as e:
                print(f"❌ خطأ: {e}")
    
    def show_status(self):
        """عرض حالة النظام"""
        print("\n📊 حالة نظام حورس:")
        print("-" * 30)
        print(f"🌐 Gemini: {'✅ متاح' if self.gemini_available else '❌ غير متاح'}")
        print(f"🏠 Ollama: {'✅ متاح' if self.ollama_available else '❌ غير متاح'}")
        print(f"📦 النماذج المحلية: {len(self.available_models)}")
        
        if self.available_models:
            print("📋 النماذج المتاحة:")
            for model in self.available_models:
                print(f"   - {model}")
    
    def show_help(self):
        """عرض المساعدة"""
        print("\n📋 أوامر حورس المتاحة:")
        print("-" * 30)
        print("🤔 اكتب أي سؤال - حورس سيجيب عليه")
        print("📊 'حالة' - عرض حالة النظام")
        print("📋 'مساعدة' - عرض هذه القائمة")
        print("👋 'خروج' - إنهاء الجلسة")
        print("\n💡 أمثلة على الأسئلة:")
        print("   - ما هو الذكاء الاصطناعي؟")
        print("   - كيف أتعلم البرمجة؟")
        print("   - اشرح لي تقنية البلوك تشين")
        print("   - ما هي أفضل لغة برمجة؟")

def main():
    """الدالة الرئيسية"""
    print("🚀 نظام استدعاء المساعد حورس")
    print("=" * 60)
    print(f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # إنشاء نظام الاستدعاء
    horus_system = HorusSummon()
    
    # اختبار سريع
    print("\n🧪 اختبار سريع لحورس...")
    test_response = horus_system.summon_horus("مرحبا حورس، هل تعمل بشكل جيد؟")
    
    if not test_response.startswith("❌"):
        print("\n✅ حورس جاهز للاستخدام!")
        
        # بدء الوضع التفاعلي
        horus_system.interactive_mode()
    else:
        print("\n❌ حورس غير متاح حالياً")
        print("🔧 تحقق من:")
        print("   - اتصال الإنترنت")
        print("   - مفاتيح API")
        print("   - تثبيت Ollama")

if __name__ == "__main__":
    main()
