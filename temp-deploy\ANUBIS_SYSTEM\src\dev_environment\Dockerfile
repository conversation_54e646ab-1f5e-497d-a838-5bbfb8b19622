# حاوية أدوات أنوبيس المعزولة والآمنة
FROM python:3.11-slim

# إعداد متغيرات البيئة
ENV PYTHONUNBUFFERED=1
ENV TOOLS_ENV=isolated
ENV USER_ID=1001
ENV GROUP_ID=1001

# إنشاء مستخدم غير مميز
RUN groupadd -g $GROUP_ID anubis_tools && \
    useradd -u $USER_ID -g $GROUP_ID -m -s /bin/bash anubis_tools

# تثبيت المتطلبات الأساسية
RUN apt-get update && apt-get install -y --no-install-recommends \
    htop \
    curl \
    psutil \
    procps \
    cron \
    && pip install --no-cache-dir \
    psutil \
    watchdog \
    schedule \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# إعداد مجلد العمل
WORKDIR /app/tools

# إنشاء المجلدات المطلوبة
RUN mkdir -p /app/tools/data /app/tools/logs /app/tools/temp /app/tools/backups \
    && chown -R anubis_tools:anubis_tools /app/tools

# نسخ أدوات النظام
COPY --chown=anubis_tools:anubis_tools . .

# التبديل للمستخدم غير المميز
USER anubis_tools

# فحص الصحة المتقدم
HEALTHCHECK --interval=30s --timeout=15s --start-period=10s --retries=3 \
    CMD python -c "import psutil; print('OK')" || exit 1

# المنافذ المكشوفة
EXPOSE 8080

# نقطة الدخول
ENTRYPOINT ["python", "-m", "maintenance.maintenance_system"]
