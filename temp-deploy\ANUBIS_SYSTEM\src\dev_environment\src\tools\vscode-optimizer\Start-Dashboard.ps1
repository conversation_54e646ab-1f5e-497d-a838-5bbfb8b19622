# VS Code Process Control Dashboard - PowerShell Launcher
# =====================================================

param(
    [switch]$SkipChecks,
    [switch]$Verbose,
    [switch]$InstallDeps,
    [string]$ConfigFile = "dashboard_config.json"
)

# إعداد الألوان والرموز
$Colors = @{
    Success = "Green"
    Error = "Red"
    Warning = "Yellow"
    Info = "Cyan"
    Header = "Magenta"
}

$Symbols = @{
    Success = "✅"
    Error = "❌"
    Warning = "⚠️"
    Info = "ℹ️"
    Rocket = "🚀"
    Gear = "⚙️"
    Dashboard = "🎛️"
}

function Write-ColorMessage {
    param(
        [string]$Message,
        [string]$Color = "White",
        [string]$Symbol = ""
    )
    
    if ($Symbol) {
        Write-Host "$Symbol " -NoNewline
    }
    Write-Host $Message -ForegroundColor $Color
}

function Show-Header {
    Clear-Host
    Write-Host "████████████████████████████████████████████████████████████████" -ForegroundColor $Colors.Header
    Write-Host "█                                                              █" -ForegroundColor $Colors.Header
    Write-Host "█           $($Symbols.Dashboard) VS Code Process Control Dashboard $($Symbols.Dashboard)          █" -ForegroundColor $Colors.Header
    Write-Host "█                                                              █" -ForegroundColor $Colors.Header
    Write-Host "█  واجهة تحكم شاملة لإدارة العمليات والإضافات                █" -ForegroundColor $Colors.Header
    Write-Host "█                                                              █" -ForegroundColor $Colors.Header
    Write-Host "████████████████████████████████████████████████████████████████" -ForegroundColor $Colors.Header
    Write-Host ""
}

function Test-PythonInstallation {
    Write-ColorMessage "فحص تثبيت Python..." $Colors.Info $Symbols.Info
    
    try {
        $pythonVersion = python --version 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-ColorMessage "Python متاح: $pythonVersion" $Colors.Success $Symbols.Success
            return $true
        }
    }
    catch {
        Write-ColorMessage "Python غير مثبت أو غير متاح في PATH" $Colors.Error $Symbols.Error
        Write-ColorMessage "يرجى تثبيت Python من: https://python.org" $Colors.Warning $Symbols.Warning
        return $false
    }
    
    return $false
}

function Test-RequiredPackages {
    Write-ColorMessage "فحص المكتبات المطلوبة..." $Colors.Info $Symbols.Info
    
    $packages = @("psutil")
    $missingPackages = @()
    
    foreach ($package in $packages) {
        try {
            $result = python -c "import $package" 2>&1
            if ($LASTEXITCODE -eq 0) {
                Write-ColorMessage "$package متاح" $Colors.Success $Symbols.Success
            }
            else {
                Write-ColorMessage "$package غير مثبت" $Colors.Error $Symbols.Error
                $missingPackages += $package
            }
        }
        catch {
            Write-ColorMessage "$package غير مثبت" $Colors.Error $Symbols.Error
            $missingPackages += $package
        }
    }
    
    # فحص tkinter
    try {
        $result = python -c "import tkinter" 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-ColorMessage "tkinter متاح" $Colors.Success $Symbols.Success
        }
        else {
            Write-ColorMessage "tkinter غير متاح - قد تحتاج لإعادة تثبيت Python" $Colors.Warning $Symbols.Warning
        }
    }
    catch {
        Write-ColorMessage "tkinter غير متاح" $Colors.Warning $Symbols.Warning
    }
    
    if ($missingPackages.Count -gt 0) {
        if ($InstallDeps) {
            Install-MissingPackages $missingPackages
        }
        else {
            Write-ColorMessage "المكتبات المفقودة: $($missingPackages -join ', ')" $Colors.Warning $Symbols.Warning
            Write-ColorMessage "استخدم -InstallDeps لتثبيتها تلقائياً" $Colors.Info $Symbols.Info
            return $false
        }
    }
    
    return $true
}

function Install-MissingPackages {
    param([array]$Packages)
    
    Write-ColorMessage "تثبيت المكتبات المفقودة..." $Colors.Info $Symbols.Gear
    
    foreach ($package in $Packages) {
        Write-ColorMessage "تثبيت $package..." $Colors.Info $Symbols.Gear
        try {
            pip install $package
            if ($LASTEXITCODE -eq 0) {
                Write-ColorMessage "تم تثبيت $package بنجاح" $Colors.Success $Symbols.Success
            }
            else {
                Write-ColorMessage "فشل في تثبيت $package" $Colors.Error $Symbols.Error
                return $false
            }
        }
        catch {
            Write-ColorMessage "خطأ في تثبيت $package" $Colors.Error $Symbols.Error
            return $false
        }
    }
    
    return $true
}

function Test-VSCodeInstallation {
    Write-ColorMessage "فحص تثبيت VS Code..." $Colors.Info $Symbols.Info
    
    try {
        $vscodeVersion = code --version 2>&1
        if ($LASTEXITCODE -eq 0) {
            $version = ($vscodeVersion -split "`n")[0]
            Write-ColorMessage "VS Code متاح: $version" $Colors.Success $Symbols.Success
            return $true
        }
    }
    catch {
        Write-ColorMessage "VS Code غير متاح في PATH" $Colors.Warning $Symbols.Warning
        Write-ColorMessage "بعض المميزات قد لا تعمل" $Colors.Warning $Symbols.Warning
        return $false
    }
    
    return $false
}

function Test-DashboardFiles {
    Write-ColorMessage "فحص ملفات لوحة التحكم..." $Colors.Info $Symbols.Info
    
    $requiredFiles = @(
        "process_control_dashboard.py",
        "dashboard_config.json"
    )
    
    $missingFiles = @()
    
    foreach ($file in $requiredFiles) {
        if (Test-Path $file) {
            Write-ColorMessage "$file موجود" $Colors.Success $Symbols.Success
        }
        else {
            Write-ColorMessage "$file غير موجود" $Colors.Error $Symbols.Error
            $missingFiles += $file
        }
    }
    
    if ($missingFiles.Count -gt 0) {
        Write-ColorMessage "الملفات المفقودة: $($missingFiles -join ', ')" $Colors.Error $Symbols.Error
        return $false
    }
    
    return $true
}

function Start-Dashboard {
    Write-ColorMessage "تشغيل لوحة التحكم..." $Colors.Info $Symbols.Rocket
    Write-Host "=" * 60 -ForegroundColor $Colors.Header
    
    try {
        if ($Verbose) {
            python process_control_dashboard.py --verbose
        }
        else {
            python process_control_dashboard.py
        }
    }
    catch {
        Write-ColorMessage "خطأ في تشغيل لوحة التحكم: $_" $Colors.Error $Symbols.Error
        return $false
    }
    
    return $true
}

function Show-SystemInfo {
    Write-ColorMessage "معلومات النظام:" $Colors.Info $Symbols.Info
    Write-Host "  نظام التشغيل: $($PSVersionTable.OS)" -ForegroundColor White
    Write-Host "  PowerShell: $($PSVersionTable.PSVersion)" -ForegroundColor White
    Write-Host "  المعمارية: $($env:PROCESSOR_ARCHITECTURE)" -ForegroundColor White
    Write-Host ""
}

function Main {
    Show-Header
    
    if ($Verbose) {
        Show-SystemInfo
    }
    
    # فحص المتطلبات
    if (-not $SkipChecks) {
        Write-ColorMessage "فحص المتطلبات..." $Colors.Info $Symbols.Gear
        Write-Host ""
        
        # فحص Python
        if (-not (Test-PythonInstallation)) {
            Write-ColorMessage "فشل في فحص Python" $Colors.Error $Symbols.Error
            Read-Host "اضغط Enter للخروج"
            return
        }
        
        # فحص المكتبات
        if (-not (Test-RequiredPackages)) {
            Write-ColorMessage "فشل في فحص المكتبات" $Colors.Error $Symbols.Error
            Read-Host "اضغط Enter للخروج"
            return
        }
        
        # فحص VS Code (اختياري)
        $vscodeAvailable = Test-VSCodeInstallation
        
        # فحص ملفات لوحة التحكم
        if (-not (Test-DashboardFiles)) {
            Write-ColorMessage "فشل في فحص ملفات لوحة التحكم" $Colors.Error $Symbols.Error
            Read-Host "اضغط Enter للخروج"
            return
        }
        
        Write-Host ""
        Write-ColorMessage "جميع المتطلبات متوفرة!" $Colors.Success $Symbols.Success
        Write-Host ""
    }
    
    # تشغيل لوحة التحكم
    if (Start-Dashboard) {
        Write-ColorMessage "تم إغلاق لوحة التحكم بنجاح" $Colors.Success $Symbols.Success
    }
    else {
        Write-ColorMessage "حدث خطأ في تشغيل لوحة التحكم" $Colors.Error $Symbols.Error
    }
    
    if (-not $SkipChecks) {
        Read-Host "اضغط Enter للخروج"
    }
}

# تشغيل البرنامج الرئيسي
try {
    Main
}
catch {
    Write-ColorMessage "خطأ غير متوقع: $_" $Colors.Error $Symbols.Error
    Read-Host "اضغط Enter للخروج"
}
