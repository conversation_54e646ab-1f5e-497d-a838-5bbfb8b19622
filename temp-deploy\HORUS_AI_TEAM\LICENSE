MIT License

Copyright (c) 2024 Amr Ashour - HORUS AI TEAM Project

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

---

## Additional Terms for HORUS AI TEAM

### Attribution
When using this software, please provide appropriate attribution to:
- The HORUS AI TEAM Project
- Amr Ashour (Original Author)
- The Egyptian Mythology-Inspired AI Agents

### Team Members Recognition
This project features AI agents inspired by Egyptian mythology:
- 𓁟 THOTH (تحوت) - The Quick Analyzer
- 𓊪 PTAH (بتاح) - The Expert Developer
- ☀️ RA (رع) - The Strategic Advisor
- 𓎡 KHNUM (خنوم) - The Creative Innovator
- 𓋇 SESHAT (سشات) - The Visual Analyst
- 𓂀 ANUBIS (أنوبيس) - The Security Guardian
- ⚖️ MAAT (ماعت) - The Ethics Guardian
- 𓅃 HORUS (حورس) - The Supreme Coordinator

### Commercial Use
This software may be used for commercial purposes under the MIT License terms.
However, we appreciate acknowledgment in commercial products.

### Contributions
By contributing to this project, you agree that your contributions will be 
licensed under the same MIT License.

### AI Models Integration
This software integrates with various AI models and services. Users are 
responsible for complying with the terms of service of these providers:
- Ollama (Local Models)
- OpenAI (GPT Models)
- Google (Gemini Models)
- Anthropic (Claude Models)

### Disclaimer
This software is provided for educational and research purposes. The AI agents
are designed to assist and collaborate, but users should verify all outputs
and use their judgment in critical applications.

### Support
While this software is provided "as is", the community and maintainers
strive to provide support through GitHub issues and discussions.

---

**HORUS AI TEAM** - Collaborative AI with Egyptian Wisdom
𓅃 Eight Minds, One Vision, Infinite Possibilities
