# 🚀 دليل البدء السريع - نظام حورس

## ⚡ التشغيل الفوري

### الطريقة الأسهل (موصى بها)
```bash
python horus_fixed_launcher.py
```

### النظام المستقر
```bash
python horus_stable_system.py
```

### النظام الكامل
```bash
python horus_complete_system.py
```

## 🛠️ حل مشاكل التشغيل

### إذا كانت الترمينالات لا تعمل:

#### 1. إيقاف العمليات المعلقة
```bash
taskkill /f /im python.exe
taskkill /f /im streamlit.exe
```

#### 2. استخدام المشغل المحسن
```bash
python horus_fixed_launcher.py
```
ثم اختر الخيار 5 لإيقاف العمليات المعلقة

#### 3. فحص النظام
في المشغل، اختر الخيار 4 لفحص حالة النظام

## 🎮 أنماط الاستخدام

### 1. المشغل التفاعلي (الأفضل للمبتدئين)
- تشغيل: `python horus_fixed_launcher.py`
- يفحص النظام تلقائياً
- يوفر قائمة خيارات واضحة
- يحل المشاكل تلقائياً

### 2. النظام المستقر (للاستخدام اليومي)
- تشغيل: `python horus_stable_system.py`
- نظام محسن ومستقر
- معالجة أفضل للأخطاء
- واجهة سطر أوامر بسيطة

### 3. النظام الكامل (للمستخدمين المتقدمين)
- تشغيل: `python horus_complete_system.py`
- جميع الميزات المتقدمة
- إدارة المهام المعقدة
- واجهة ويب متكاملة

## 🤖 الوكلاء المتاحون

| الوكيل | الأمر | التخصص |
|--------|-------|----------|
| ⚡ تحوت | `ask تحليل سريع...` | التحليل والبحث |
| 🔧 بتاح | `ask اكتب كود...` | البرمجة والتطوير |
| 🎯 رع | `ask ما هي الاستراتيجية...` | التخطيط والاستراتيجية |
| 𓅃 حورس | `ask سؤال عام...` | التنسيق والحكمة |

## 💬 أمثلة سريعة

### في النمط التفاعلي:
```
𓅃 حورس> ask ما هو الذكاء الاصطناعي؟
𓅃 حورس> ask اكتب دالة Python لحساب المضروب
𓅃 حورس> ask ما هي أفضل استراتيجية للتعلم؟
𓅃 حورس> status
𓅃 حورس> help
```

### في المشغل:
- اختر 1 للنظام الأساسي
- اختر 2 لواجهة الويب
- اختر 3 للمحادثة مع Gemini
- اختر 4 لفحص النظام

## 🔧 حل المشاكل الشائعة

### مشكلة: "خطأ في استيراد الوحدات"
**الحل:**
```bash
pip install google-generativeai streamlit plotly pandas
```

### مشكلة: "Gemini غير متاح"
**الحل:**
1. تحقق من مفاتيح API
2. تحقق من الاتصال بالإنترنت
3. استخدم Gemini CLI المحلي

### مشكلة: "Ollama غير متاح"
**الحل:**
```bash
# تثبيت Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# تحميل النماذج
ollama pull phi3:mini
ollama pull mistral:7b
```

### مشكلة: "الترمينال معلق"
**الحل:**
1. اضغط Ctrl+C لإيقاف العملية
2. استخدم المشغل المحسن
3. اختر الخيار 5 لإيقاف العمليات

## 🌐 واجهة الويب

### تشغيل واجهة الويب:
```bash
streamlit run web_interface.py
```

### أو من المشغل:
```bash
python horus_fixed_launcher.py
# اختر الخيار 2
```

### الوصول:
افتح المتصفح على: `http://localhost:8501`

## 📊 الميزات المتقدمة

### إدارة المهام:
- إضافة مهام بأولويات مختلفة
- تتبع التقدم
- إحصائيات الأداء

### التعاون بين الوكلاء:
- مهام تعاونية
- تجميع النتائج
- تحليل شامل

### الإحصائيات:
- أداء الوكلاء
- معدلات النجاح
- أوقات الاستجابة

## 🆘 الحصول على المساعدة

### داخل النظام:
```
𓅃 حورس> help
```

### فحص الحالة:
```
𓅃 حورس> status
```

### اختبار الوكلاء:
```
𓅃 حورس> test
```

## 🎯 نصائح للاستخدام الأمثل

1. **ابدأ بالمشغل المحسن** للتأكد من عمل النظام
2. **استخدم النظام المستقر** للاستخدام اليومي
3. **جرب واجهة الويب** للإدارة البصرية
4. **اختبر الوكلاء** قبل المهام المعقدة
5. **راقب الإحصائيات** لتحسين الأداء

## 🔄 التحديث والصيانة

### تحديث المكتبات:
```bash
pip install --upgrade google-generativeai streamlit plotly pandas
```

### تحديث نماذج Ollama:
```bash
ollama pull phi3:mini
ollama pull mistral:7b
ollama pull llama3:8b
```

### تنظيف النظام:
```bash
# إيقاف العمليات
taskkill /f /im python.exe

# حذف الملفات المؤقتة
del *.log
del advanced_memory\*.json
```

---

**𓅃 نظام حورس جاهز للعمل! ابدأ رحلتك مع الذكاء الاصطناعي التعاوني 𓅃**