#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 مدير قاعدة البيانات المبسط لنظام أنوبيس
Anubis Simple Database Manager

نسخة مبسطة تعمل مع المكتبات المتاحة فقط
"""

import json
import logging
import asyncio
import sqlite3
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any, List
from contextlib import asynccontextmanager

try:
    import aiosqlite
    AIOSQLITE_AVAILABLE = True
except ImportError:
    AIOSQLITE_AVAILABLE = False

try:
    import mysql.connector
    from mysql.connector import pooling
    MYSQL_AVAILABLE = True
except ImportError:
    MYSQL_AVAILABLE = False

class SimpleDatabaseManager:
    """مدير قاعدة البيانات المبسط"""
    
    def __init__(self, config_path: str = "config/database_config.json"):
        self.config_path = Path(config_path)
        self.config = self._load_config()
        self.mysql_pool = None
        self.sqlite_connection = None
        self.logger = self._setup_logging()
        
    def _load_config(self) -> Dict[str, Any]:
        """تحميل إعدادات قاعدة البيانات"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            # إعدادات افتراضية
            return {
                "database": {
                    "type": "sqlite",
                    "mysql": {
                        "host": "localhost",
                        "port": 3306,
                        "user": "root", 
                        "password": "[DATABASE_PASSWORD]",
                        "database": "anubis_system",
                        "charset": "utf8mb4"
                    },
                    "sqlite": {
                        "db_path": "data/anubis.db"
                    }
                }
            }
    
    def _setup_logging(self) -> logging.Logger:
        """إعداد نظام السجلات"""
        logger = logging.getLogger("anubis_simple_database")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    async def initialize_sqlite(self) -> bool:
        """تهيئة اتصال SQLite"""
        try:
            sqlite_config = self.config["database"]["sqlite"]
            db_path = Path(sqlite_config["db_path"])
            
            # إنشاء المجلد إذا لم يكن موجوداً
            db_path.parent.mkdir(parents=True, exist_ok=True)
            
            # اختبار الاتصال
            if AIOSQLITE_AVAILABLE:
                async with aiosqlite.connect(str(db_path)) as db:
                    await db.execute("SELECT 1")
                    await db.commit()
            else:
                # استخدام sqlite3 العادي
                conn = sqlite3.connect(str(db_path))
                conn.execute("SELECT 1")
                conn.close()
            
            self.logger.info("✅ تم تهيئة SQLite بنجاح")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في تهيئة SQLite: {e}")
            return False
    
    async def initialize_mysql(self) -> bool:
        """تهيئة اتصال MySQL"""
        if not MYSQL_AVAILABLE:
            self.logger.warning("⚠️ MySQL غير متاح - تخطي التهيئة")
            return False
            
        try:
            mysql_config = self.config["database"]["mysql"]
            
            # اختبار الاتصال البسيط
            connection = mysql.connector.connect(
                host=mysql_config["host"],
                port=mysql_config["port"],
                user=mysql_config["user"],
                password=mysql_config["password"],
                database=mysql_config["database"],
                charset=mysql_config.get("charset", "utf8mb4")
            )
            
            if connection.is_connected():
                cursor = connection.cursor()
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                cursor.close()
                connection.close()
                
                if result[0] == 1:
                    self.logger.info("✅ تم تهيئة MySQL بنجاح")
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في تهيئة MySQL: {e}")
            return False
    
    async def initialize_all(self) -> Dict[str, bool]:
        """تهيئة جميع قواعد البيانات"""
        results = {}
        
        # تهيئة SQLite (دائماً متاح)
        results['sqlite'] = await self.initialize_sqlite()
        
        # تهيئة MySQL (إذا كان متاحاً)
        if MYSQL_AVAILABLE:
            results['mysql'] = await self.initialize_mysql()
        else:
            results['mysql'] = False
            self.logger.info("⚠️ MySQL غير متاح")
        
        self.logger.info(f"📊 نتائج التهيئة: {results}")
        return results
    
    def get_sqlite_connection(self):
        """الحصول على اتصال SQLite متزامن"""
        sqlite_config = self.config["database"]["sqlite"]
        db_path = sqlite_config["db_path"]
        return sqlite3.connect(db_path)
    
    def get_mysql_connection(self):
        """الحصول على اتصال MySQL متزامن"""
        if not MYSQL_AVAILABLE:
            raise Exception("MySQL غير متاح")
            
        mysql_config = self.config["database"]["mysql"]
        return mysql.connector.connect(
            host=mysql_config["host"],
            port=mysql_config["port"],
            user=mysql_config["user"],
            password=mysql_config["password"],
            database=mysql_config["database"],
            charset=mysql_config.get("charset", "utf8mb4")
        )
    
    async def execute_query(self, query: str, params: tuple = None, 
                          database: str = "sqlite") -> List[Dict]:
        """تنفيذ استعلام وإرجاع النتائج"""
        try:
            if database == "sqlite":
                if AIOSQLITE_AVAILABLE:
                    # استخدام aiosqlite
                    db_path = self.config["database"]["sqlite"]["db_path"]
                    async with aiosqlite.connect(db_path) as conn:
                        conn.row_factory = aiosqlite.Row
                        async with conn.execute(query, params or ()) as cursor:
                            rows = await cursor.fetchall()
                            return [dict(row) for row in rows]
                else:
                    # استخدام sqlite3 العادي
                    conn = self.get_sqlite_connection()
                    conn.row_factory = sqlite3.Row
                    cursor = conn.cursor()
                    cursor.execute(query, params or ())
                    rows = cursor.fetchall()
                    conn.close()
                    return [dict(row) for row in rows]
                    
            elif database == "mysql" and MYSQL_AVAILABLE:
                conn = self.get_mysql_connection()
                cursor = conn.cursor(dictionary=True)
                cursor.execute(query, params or ())
                results = cursor.fetchall()
                cursor.close()
                conn.close()
                return results
            else:
                raise Exception(f"قاعدة البيانات {database} غير متاحة")
                
        except Exception as e:
            self.logger.error(f"خطأ في تنفيذ الاستعلام: {e}")
            raise
    
    async def execute_non_query(self, query: str, params: tuple = None,
                               database: str = "sqlite") -> int:
        """تنفيذ استعلام بدون إرجاع نتائج (INSERT, UPDATE, DELETE)"""
        try:
            if database == "sqlite":
                if AIOSQLITE_AVAILABLE:
                    db_path = self.config["database"]["sqlite"]["db_path"]
                    async with aiosqlite.connect(db_path) as conn:
                        await conn.execute(query, params or ())
                        await conn.commit()
                        return conn.total_changes
                else:
                    conn = self.get_sqlite_connection()
                    cursor = conn.cursor()
                    cursor.execute(query, params or ())
                    affected_rows = cursor.rowcount
                    conn.commit()
                    conn.close()
                    return affected_rows
                    
            elif database == "mysql" and MYSQL_AVAILABLE:
                conn = self.get_mysql_connection()
                cursor = conn.cursor()
                cursor.execute(query, params or ())
                affected_rows = cursor.rowcount
                conn.commit()
                cursor.close()
                conn.close()
                return affected_rows
            else:
                raise Exception(f"قاعدة البيانات {database} غير متاحة")
                
        except Exception as e:
            self.logger.error(f"خطأ في تنفيذ الاستعلام: {e}")
            raise
    
    async def create_tables(self, database: str = "sqlite"):
        """إنشاء الجداول الأساسية"""
        tables_sql = {
            "users": """
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    email TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT TRUE
                )
            """,
            "ai_sessions": """
                CREATE TABLE IF NOT EXISTS ai_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    session_id TEXT UNIQUE NOT NULL,
                    model_name TEXT NOT NULL,
                    prompt TEXT NOT NULL,
                    response TEXT,
                    tokens_used INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                )
            """,
            "system_logs": """
                CREATE TABLE IF NOT EXISTS system_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    level TEXT NOT NULL,
                    message TEXT NOT NULL,
                    component TEXT,
                    user_id INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                )
            """
        }
        
        # تعديل SQL للـ MySQL
        if database == "mysql":
            for table_name, sql in tables_sql.items():
                # إصلاح ترتيب AUTO_INCREMENT في MySQL
                sql = sql.replace("INTEGER PRIMARY KEY AUTOINCREMENT", "INT PRIMARY KEY AUTO_INCREMENT")
                sql = sql.replace("TEXT", "VARCHAR(255)")
                sql = sql.replace("BOOLEAN", "BOOLEAN")
                sql = sql.replace("CURRENT_TIMESTAMP", "CURRENT_TIMESTAMP")
                tables_sql[table_name] = sql
        
        try:
            for table_name, sql in tables_sql.items():
                await self.execute_non_query(sql, database=database)
                self.logger.info(f"✅ تم إنشاء جدول {table_name}")
                
        except Exception as e:
            self.logger.error(f"❌ خطأ في إنشاء الجداول: {e}")
            raise
    
    async def get_connection_status(self) -> Dict[str, Any]:
        """الحصول على حالة الاتصالات"""
        status = {
            "sqlite": {"connected": False, "file_exists": False, "available": AIOSQLITE_AVAILABLE},
            "mysql": {"connected": False, "available": MYSQL_AVAILABLE}
        }
        
        # فحص SQLite
        try:
            db_path = Path(self.config["database"]["sqlite"]["db_path"])
            status["sqlite"]["file_exists"] = db_path.exists()
            
            if AIOSQLITE_AVAILABLE:
                async with aiosqlite.connect(str(db_path)) as conn:
                    await conn.execute("SELECT 1")
                    status["sqlite"]["connected"] = True
            else:
                conn = sqlite3.connect(str(db_path))
                conn.execute("SELECT 1")
                conn.close()
                status["sqlite"]["connected"] = True
        except:
            pass
        
        # فحص MySQL
        if MYSQL_AVAILABLE:
            try:
                conn = self.get_mysql_connection()
                if conn.is_connected():
                    cursor = conn.cursor()
                    cursor.execute("SELECT 1")
                    cursor.close()
                    conn.close()
                    status["mysql"]["connected"] = True
            except:
                pass
        
        return status
    
    async def close_all_connections(self):
        """إغلاق جميع الاتصالات"""
        try:
            self.logger.info("🔒 تم إغلاق جميع اتصالات قاعدة البيانات")
        except Exception as e:
            self.logger.error(f"خطأ في إغلاق الاتصالات: {e}")

# إنشاء instance عام
simple_db_manager = SimpleDatabaseManager()
