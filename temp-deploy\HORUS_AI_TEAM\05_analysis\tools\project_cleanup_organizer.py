#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
منظم ومنظف مشروع حورس
HORUS Project Cleanup and Organizer
"""

import os
import sys
import shutil
import json
from datetime import datetime
from typing import Dict, List, Tuple

class HorusProjectOrganizer:
    """منظم مشروع حورس للإنتاج"""
    
    def __init__(self):
        print("🧹 منظم مشروع حورس للإنتاج")
        print("=" * 50)
        
        self.project_root = os.path.dirname(os.path.abspath(__file__))
        self.archive_dir = os.path.join(self.project_root, '09_archive')
        
        # خطة التنظيم
        self.organization_plan = {
            # الملفات الأساسية للإنتاج
            'production_core': [
                'horus_stable_system.py',
                'START_HERE.py',
                'horus_fixed_launcher.py',
                'task_management_system.py'
            ],
            
            # واجهات المستخدم
            'interfaces': [
                'web_interface.py',
                'horus_fixed_interface.py'
            ],
            
            # الأنظمة المتقدمة
            'advanced_systems': [
                'advanced_horus_system.py',
                'horus_complete_system.py',
                'collaborative_ai_system.py'
            ],
            
            # أدوات التطوير والاختبار
            'development_tools': [
                'comprehensive_test_system.py',
                'final_test_complete_system.py',
                'quick_debug_test.py',
                'test_gemini_setup.py',
                'test_new_gemini_key.py'
            ],
            
            # أدوات التحليل
            'analysis_tools': [
                'project_analyzer_organizer.py',
                'project_organizer_implementation.py',
                'anubis_analysis_task.py',
                'horus_project_analysis_request.py',
                'horus_gemini_analysis.py',
                'project_cleanup_organizer.py'
            ],
            
            # التوثيق الأساسي
            'essential_docs': [
                'README_FINAL.md',
                'QUICK_START_GUIDE.md',
                'TERMINAL_ISSUES_SOLUTION_REPORT.md',
                'HORUS_PROJECT_COMPREHENSIVE_ANALYSIS_REPORT.md'
            ],
            
            # ملفات الإعداد
            'config_files': [
                'requirements_complete.txt'
            ],
            
            # ملفات للأرشيف (غير مستخدمة أو مكررة)
            'to_archive': [
                'quick_start.py',  # مكرر مع START_HERE.py
                'start_horus.py',  # قديم
                'README.md',  # قديم
                'README_COMPREHENSIVE.md',  # مكرر
                'README_UPDATE_REPORT.md',  # تقرير قديم
                'READINESS_ENHANCEMENT_PLAN.md',  # خطة قديمة
                'BACKUP_COMPARISON_ANALYSIS.md',  # تحليل قديم
                'ARCHIVE_MIGRATION_SUCCESS_REPORT.md',  # تقرير قديم
                'FINAL_READINESS_REPORT.md',  # مكرر
                'MISSION_ACCOMPLISHED_FINAL_REPORT.md',  # مكرر
                'PROJECT_COMPLETE_UPDATE_REPORT.md',  # مكرر
                'PROJECT_ORGANIZATION_SUCCESS_REPORT.md'  # مكرر
            ]
        }
        
        # خريطة المجلدات المستهدفة
        self.target_folders = {
            'production_core': '01_core/engines',
            'interfaces': '01_core/interfaces', 
            'advanced_systems': '01_core/engines',
            'development_tools': '08_utilities/tools',
            'analysis_tools': '05_analysis/tools',
            'essential_docs': '06_documentation/guides',
            'config_files': '07_configuration/requirements'
        }
    
    def analyze_current_structure(self):
        """تحليل البنية الحالية"""
        print("\n📊 تحليل البنية الحالية...")
        
        current_files = []
        for item in os.listdir(self.project_root):
            item_path = os.path.join(self.project_root, item)
            if os.path.isfile(item_path) and not item.startswith('.'):
                current_files.append(item)
        
        print(f"📁 الملفات في الجذر: {len(current_files)}")
        
        # تصنيف الملفات
        categorized = {
            'production_core': [],
            'interfaces': [],
            'advanced_systems': [],
            'development_tools': [],
            'analysis_tools': [],
            'essential_docs': [],
            'config_files': [],
            'to_archive': [],
            'uncategorized': []
        }
        
        for file in current_files:
            categorized_flag = False
            for category, files_list in self.organization_plan.items():
                if file in files_list:
                    categorized[category].append(file)
                    categorized_flag = True
                    break
            
            if not categorized_flag:
                categorized['uncategorized'].append(file)
        
        # عرض التصنيف
        for category, files in categorized.items():
            if files:
                print(f"  📂 {category}: {len(files)} ملف")
                for file in files[:3]:  # أول 3 ملفات
                    print(f"    - {file}")
                if len(files) > 3:
                    print(f"    ... و {len(files)-3} ملف آخر")
        
        return categorized
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        print("\n💾 إنشاء نسخة احتياطية...")
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_dir = os.path.join(self.archive_dir, 'backup', f'pre_cleanup_{timestamp}')
        
        try:
            os.makedirs(backup_dir, exist_ok=True)
            
            # نسخ الملفات الموجودة في الجذر
            root_files = [f for f in os.listdir(self.project_root) 
                         if os.path.isfile(os.path.join(self.project_root, f)) 
                         and not f.startswith('.')]
            
            for file in root_files:
                src = os.path.join(self.project_root, file)
                dst = os.path.join(backup_dir, file)
                shutil.copy2(src, dst)
            
            print(f"✅ تم إنشاء نسخة احتياطية: {backup_dir}")
            print(f"📁 تم نسخ {len(root_files)} ملف")
            
            return backup_dir
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
            return None
    
    def organize_files(self, categorized_files: Dict[str, List[str]]):
        """تنظيم الملفات في المجلدات المناسبة"""
        print("\n📁 تنظيم الملفات...")
        
        moved_files = []
        errors = []
        
        for category, files in categorized_files.items():
            if not files or category == 'uncategorized':
                continue
            
            if category == 'to_archive':
                # نقل للأرشيف
                self.archive_files(files)
                continue
            
            # تحديد المجلد المستهدف
            target_folder = self.target_folders.get(category)
            if not target_folder:
                print(f"⚠️ لا يوجد مجلد مستهدف لـ {category}")
                continue
            
            target_path = os.path.join(self.project_root, target_folder)
            
            # إنشاء المجلد إذا لم يكن موجوداً
            try:
                os.makedirs(target_path, exist_ok=True)
                print(f"📂 تنظيم {category} في {target_folder}")
                
                for file in files:
                    src = os.path.join(self.project_root, file)
                    dst = os.path.join(target_path, file)
                    
                    if os.path.exists(src):
                        try:
                            shutil.move(src, dst)
                            moved_files.append((file, target_folder))
                            print(f"  ✅ {file} → {target_folder}")
                        except Exception as e:
                            errors.append((file, str(e)))
                            print(f"  ❌ خطأ في نقل {file}: {e}")
                    else:
                        print(f"  ⚠️ {file} غير موجود")
                        
            except Exception as e:
                print(f"❌ خطأ في إنشاء المجلد {target_path}: {e}")
        
        return moved_files, errors
    
    def archive_files(self, files: List[str]):
        """نقل الملفات للأرشيف"""
        print(f"\n🗄️ أرشفة {len(files)} ملف...")
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        archive_subdir = os.path.join(self.archive_dir, 'deprecated', f'cleanup_{timestamp}')
        
        try:
            os.makedirs(archive_subdir, exist_ok=True)
            
            for file in files:
                src = os.path.join(self.project_root, file)
                dst = os.path.join(archive_subdir, file)
                
                if os.path.exists(src):
                    try:
                        shutil.move(src, dst)
                        print(f"  🗄️ {file} → أرشيف")
                    except Exception as e:
                        print(f"  ❌ خطأ في أرشفة {file}: {e}")
                else:
                    print(f"  ⚠️ {file} غير موجود")
            
            print(f"✅ تم الأرشفة في: {archive_subdir}")
            
        except Exception as e:
            print(f"❌ خطأ في الأرشفة: {e}")
    
    def create_production_structure(self):
        """إنشاء بنية الإنتاج"""
        print("\n🏗️ إنشاء بنية الإنتاج...")
        
        # المجلدات الأساسية للإنتاج
        production_folders = [
            '01_core/engines',
            '01_core/interfaces',
            '01_core/managers',
            '02_team_members/configurations',
            '03_memory_system/memory',
            '04_collaboration/systems',
            '05_analysis/tools',
            '06_documentation/guides',
            '07_configuration/requirements',
            '08_utilities/tools',
            '09_archive/deprecated'
        ]
        
        for folder in production_folders:
            folder_path = os.path.join(self.project_root, folder)
            try:
                os.makedirs(folder_path, exist_ok=True)
                print(f"  📁 {folder}")
            except Exception as e:
                print(f"  ❌ خطأ في إنشاء {folder}: {e}")
    
    def create_production_launcher(self):
        """إنشاء مشغل الإنتاج"""
        print("\n🚀 إنشاء مشغل الإنتاج...")
        
        launcher_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 مشغل الإنتاج - نظام حورس
HORUS Production Launcher
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """مشغل الإنتاج الرئيسي"""
    
    print("𓅃" * 30)
    print("🚀 مشغل الإنتاج - نظام حورس")
    print("HORUS Production System Launcher")
    print("𓅃" * 30)
    
    # تحديد المسارات
    current_dir = Path(__file__).parent
    core_engines = current_dir / "01_core" / "engines"
    core_interfaces = current_dir / "01_core" / "interfaces"
    
    print("\\n🎯 اختر نمط التشغيل:")
    print("1. 🛡️ النظام المستقر (موصى به للإنتاج)")
    print("2. 🌐 واجهة الويب التفاعلية")
    print("3. 🚀 المشغل المحسن")
    print("4. 📊 لوحة التحكم")
    print("5. 🔧 أدوات التطوير")
    print("6. 🚪 الخروج")
    
    while True:
        try:
            choice = input("\\n🎮 اختر رقم (1-6): ").strip()
            
            if choice == '1':
                # النظام المستقر
                stable_system = core_engines / "horus_stable_system.py"
                if stable_system.exists():
                    subprocess.run([sys.executable, str(stable_system)])
                else:
                    print("❌ النظام المستقر غير موجود")
            
            elif choice == '2':
                # واجهة الويب
                web_interface = core_interfaces / "web_interface.py"
                if web_interface.exists():
                    print("🌐 بدء واجهة الويب...")
                    subprocess.run(['streamlit', 'run', str(web_interface)])
                else:
                    print("❌ واجهة الويب غير موجودة")
            
            elif choice == '3':
                # المشغل المحسن
                launcher = core_engines / "horus_fixed_launcher.py"
                if launcher.exists():
                    subprocess.run([sys.executable, str(launcher)])
                else:
                    print("❌ المشغل المحسن غير موجود")
            
            elif choice == '4':
                print("📊 لوحة التحكم قيد التطوير...")
            
            elif choice == '5':
                print("🔧 أدوات التطوير في 08_utilities/tools")
            
            elif choice == '6':
                print("👋 وداعاً!")
                break
            
            else:
                print("❌ اختيار غير صحيح")
        
        except KeyboardInterrupt:
            print("\\n👋 تم إيقاف النظام")
            break
        except Exception as e:
            print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    main()
'''
        
        launcher_path = os.path.join(self.project_root, 'PRODUCTION_LAUNCHER.py')
        
        try:
            with open(launcher_path, 'w', encoding='utf-8') as f:
                f.write(launcher_content)
            
            print(f"✅ تم إنشاء مشغل الإنتاج: PRODUCTION_LAUNCHER.py")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء المشغل: {e}")
    
    def create_production_readme(self):
        """إنشاء README للإنتاج"""
        print("\n📚 إنشاء README للإنتاج...")
        
        readme_content = '''# 🚀 نظام حورس - الإصدار الإنتاجي

## 🎯 نظرة عامة

نظام حورس هو نظام ذكاء اصطناعي تعاوني متقدم يجمع بين التراث المصري والتكنولوجيا الحديثة.

## ⚡ البدء السريع

### التشغيل الفوري:
```bash
python PRODUCTION_LAUNCHER.py
```

### أو استخدم نقطة البداية:
```bash
python 01_core/engines/START_HERE.py
```

## 🏗️ البنية التنظيمية

```
HORUS_AI_TEAM/
├── 01_core/                    # المكونات الأساسية
│   ├── engines/               # محركات النظام
│   ├── interfaces/            # واجهات المستخدم
│   └── managers/              # مدراء النظام
├── 02_team_members/           # تعريفات الوكلاء
├── 03_memory_system/          # نظام الذاكرة
├── 04_collaboration/          # نظام التعاون
├── 05_analysis/               # أدوات التحليل
├── 06_documentation/          # التوثيق
├── 07_configuration/          # الإعدادات
├── 08_utilities/              # الأدوات المساعدة
└── 09_archive/                # الأرشيف
```

## 🤖 الوكلاء المتاحون

- **⚡ تحوت**: المحلل السريع
- **🔧 بتاح**: المطور الخبير  
- **🎯 رع**: المستشار الاستراتيجي
- **𓅃 حورس**: المنسق الأعلى

## 📋 المتطلبات

```bash
pip install -r 07_configuration/requirements/requirements_complete.txt
```

## 🎮 أنماط التشغيل

1. **النظام المستقر**: للاستخدام اليومي الموثوق
2. **واجهة الويب**: لوحة تحكم بصرية شاملة
3. **المشغل المحسن**: للمستخدمين المتقدمين

## 📊 الميزات

- ✅ نظام وكلاء متعدد ومتخصص
- ✅ واجهة ويب تفاعلية
- ✅ إدارة مهام متقدمة
- ✅ نظام ذاكرة ذكي
- ✅ تعاون بين الوكلاء
- ✅ دعم متعدد للنماذج

## 🆘 الدعم

راجع التوثيق في `06_documentation/guides/` للمساعدة التفصيلية.

---

**𓅃 نظام حورس - حيث يلتقي التراث بالتكنولوجيا 𓅃**
'''
        
        readme_path = os.path.join(self.project_root, 'README_PRODUCTION.md')
        
        try:
            with open(readme_path, 'w', encoding='utf-8') as f:
                f.write(readme_content)
            
            print(f"✅ تم إنشاء README الإنتاج: README_PRODUCTION.md")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء README: {e}")
    
    def generate_cleanup_report(self, moved_files: List[Tuple[str, str]], errors: List[Tuple[str, str]]):
        """إنشاء تقرير التنظيف"""
        print("\n📊 إنشاء تقرير التنظيف...")
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report = {
            'cleanup_info': {
                'timestamp': timestamp,
                'total_moved': len(moved_files),
                'total_errors': len(errors)
            },
            'moved_files': [{'file': file, 'destination': dest} for file, dest in moved_files],
            'errors': [{'file': file, 'error': error} for file, error in errors],
            'organization_plan': self.organization_plan,
            'target_folders': self.target_folders
        }
        
        report_path = os.path.join(self.project_root, f'cleanup_report_{timestamp}.json')
        
        try:
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            print(f"✅ تم إنشاء تقرير التنظيف: cleanup_report_{timestamp}.json")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء التقرير: {e}")
    
    def run_full_cleanup(self):
        """تشغيل التنظيف الكامل"""
        print("\n🧹 بدء التنظيف الكامل للمشروع")
        print("=" * 60)
        
        try:
            # 1. تحليل البنية الحالية
            categorized = self.analyze_current_structure()
            
            # 2. إنشاء نسخة احتياطية
            backup_dir = self.create_backup()
            if not backup_dir:
                print("❌ فشل في إنشاء النسخة الاحتياطية - إيقاف التنظيف")
                return
            
            # 3. إنشاء بنية الإنتاج
            self.create_production_structure()
            
            # 4. تنظيم الملفات
            moved_files, errors = self.organize_files(categorized)
            
            # 5. إنشاء مشغل الإنتاج
            self.create_production_launcher()
            
            # 6. إنشاء README الإنتاج
            self.create_production_readme()
            
            # 7. إنشاء تقرير التنظيف
            self.generate_cleanup_report(moved_files, errors)
            
            # 8. عرض النتائج النهائية
            self.display_final_results(moved_files, errors)
            
        except Exception as e:
            print(f"❌ خطأ في التنظيف: {e}")
    
    def display_final_results(self, moved_files: List[Tuple[str, str]], errors: List[Tuple[str, str]]):
        """عرض النتائج النهائية"""
        print("\n" + "="*80)
        print("🎉 تم إكمال تنظيف مشروع حورس للإنتاج")
        print("="*80)
        
        print(f"\n📊 إحصائيات التنظيف:")
        print(f"   ✅ ملفات تم نقلها: {len(moved_files)}")
        print(f"   ❌ أخطاء: {len(errors)}")
        
        if moved_files:
            print(f"\n📁 الملفات المنقولة:")
            for file, dest in moved_files[:10]:  # أول 10 ملفات
                print(f"   📄 {file} → {dest}")
            if len(moved_files) > 10:
                print(f"   ... و {len(moved_files)-10} ملف آخر")
        
        if errors:
            print(f"\n❌ الأخطاء:")
            for file, error in errors:
                print(f"   ⚠️ {file}: {error}")
        
        print(f"\n🚀 ملفات الإنتاج الجديدة:")
        print(f"   📄 PRODUCTION_LAUNCHER.py - مشغل الإنتاج الرئيسي")
        print(f"   📚 README_PRODUCTION.md - دليل الإنتاج")
        
        print(f"\n🎯 للبدء:")
        print(f"   python PRODUCTION_LAUNCHER.py")
        
        print("\n" + "="*80)
        print("✅ مشروع حورس جاهز للإنتاج!")
        print("="*80)

def main():
    """الدالة الرئيسية"""
    print("🧹 منظم مشروع حورس للإنتاج")
    print("تنظيف وترتيب البنية للوصول لدرجة الإنتاج")
    print("="*70)
    
    try:
        organizer = HorusProjectOrganizer()
        
        # تأكيد من المستخدم
        confirm = input("\n⚠️ هذا سيقوم بإعادة تنظيم المشروع بالكامل. هل تريد المتابعة؟ (y/n): ").strip().lower()
        
        if confirm in ['y', 'yes', 'نعم']:
            organizer.run_full_cleanup()
        else:
            print("👋 تم إلغاء التنظيف")
            
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف التنظيف بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في التنظيف: {e}")

if __name__ == "__main__":
    main()