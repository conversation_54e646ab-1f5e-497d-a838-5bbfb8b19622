#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 طلب مساعدة من فريق حورس لفحص وتحليل نظام ANUBIS_SYSTEM
𓅃 بعين حورس الثاقبة - تحليل شامل لنظام أنوبيس
"""

import os
import json
import subprocess
from datetime import datetime
from pathlib import Path

class HorusAnubisSystemAnalyzer:
    def __init__(self):
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.anubis_path = "ANUBIS_SYSTEM"
        self.analysis_results = {}
        
    def analyze_anubis_system(self):
        """تحليل شامل لنظام أنوبيس"""
        print("🏺 مرحباً! فريق حورس سيحلل نظام ANUBIS_SYSTEM")
        print("𓅃 بعين حورس الثاقبة، سنفحص كل جانب من النظام...")
        
        if not os.path.exists(self.anubis_path):
            print(f"❌ نظام أنوبيس غير موجود: {self.anubis_path}")
            return None
            
        # التحليل الشامل
        analysis = {
            "project_overview": self.analyze_project_overview(),
            "core_system": self.analyze_core_system(),
            "configuration": self.analyze_configuration(),
            "docker_setup": self.analyze_docker_setup(),
            "requirements": self.analyze_requirements(),
            "source_code": self.analyze_source_code(),
            "scripts_tools": self.analyze_scripts_and_tools(),
            "data_management": self.analyze_data_management(),
            "security_assessment": self.analyze_security(),
            "readiness_evaluation": self.evaluate_production_readiness(),
            "horus_consultation": self.request_horus_team_consultation()
        }
        
        return analysis
    
    def analyze_project_overview(self):
        """تحليل نظرة عامة على المشروع"""
        overview = {
            "project_name": "ANUBIS_SYSTEM",
            "structure_analysis": self.scan_directory_structure(),
            "file_statistics": self.calculate_file_statistics(),
            "main_components": self.identify_main_components()
        }
        return overview
    
    def scan_directory_structure(self):
        """فحص هيكل المجلدات"""
        structure = {
            "total_files": 0,
            "total_directories": 0,
            "directory_tree": {},
            "depth_analysis": {}
        }
        
        for root, dirs, files in os.walk(self.anubis_path):
            level = root.replace(self.anubis_path, '').count(os.sep)
            structure["total_directories"] += len(dirs)
            structure["total_files"] += len(files)
            
            # تحليل العمق
            if level not in structure["depth_analysis"]:
                structure["depth_analysis"][level] = {"dirs": 0, "files": 0}
            structure["depth_analysis"][level]["dirs"] += len(dirs)
            structure["depth_analysis"][level]["files"] += len(files)
        
        return structure
    
    def calculate_file_statistics(self):
        """حساب إحصائيات الملفات"""
        stats = {
            "file_types": {},
            "size_analysis": {},
            "important_files": []
        }
        
        important_patterns = [
            "main.py", "requirements*.txt", "docker-compose*.yml", 
            "Dockerfile*", "README.md", "config.json"
        ]
        
        for root, dirs, files in os.walk(self.anubis_path):
            for file in files:
                # تحليل أنواع الملفات
                ext = os.path.splitext(file)[1].lower()
                if ext:
                    stats["file_types"][ext] = stats["file_types"].get(ext, 0) + 1
                
                # تحديد الملفات المهمة
                for pattern in important_patterns:
                    if pattern.replace("*", "") in file:
                        stats["important_files"].append(os.path.join(root, file))
        
        return stats
    
    def identify_main_components(self):
        """تحديد المكونات الرئيسية"""
        components = []
        main_dirs = [d for d in os.listdir(self.anubis_path) 
                    if os.path.isdir(os.path.join(self.anubis_path, d))]
        
        component_descriptions = {
            "src": "الكود المصدري الأساسي",
            "config": "ملفات التكوين والإعدادات",
            "docker": "إعدادات الحاويات والنشر",
            "scripts": "سكريبتات التشغيل والأدوات",
            "tests": "اختبارات النظام",
            "data": "إدارة البيانات",
            "logs": "سجلات النظام",
            "workspace": "بيئة العمل المعزولة",
            "utilities": "الأدوات المساعدة",
            "ssl": "شهادات الأمان"
        }
        
        for dir_name in main_dirs:
            components.append({
                "name": dir_name,
                "description": component_descriptions.get(dir_name, "مكون إضافي"),
                "exists": True,
                "file_count": len(os.listdir(os.path.join(self.anubis_path, dir_name)))
            })
        
        return components
    
    def analyze_core_system(self):
        """تحليل النظام الأساسي"""
        core_analysis = {
            "main_entry_point": self.check_main_file(),
            "quick_start": self.check_quick_start(),
            "core_modules": self.analyze_core_modules(),
            "ai_services": self.analyze_ai_services()
        }
        return core_analysis
    
    def check_main_file(self):
        """فحص الملف الرئيسي"""
        main_file = os.path.join(self.anubis_path, "main.py")
        if os.path.exists(main_file):
            try:
                with open(main_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                return {
                    "exists": True,
                    "size": len(content),
                    "lines": len(content.split('\n')),
                    "has_main_function": "def main(" in content or "if __name__" in content
                }
            except:
                return {"exists": True, "readable": False}
        return {"exists": False}
    
    def check_quick_start(self):
        """فحص ملف التشغيل السريع"""
        quick_start = os.path.join(self.anubis_path, "quick_start_anubis.py")
        return {"exists": os.path.exists(quick_start)}
    
    def analyze_core_modules(self):
        """تحليل الوحدات الأساسية"""
        src_path = os.path.join(self.anubis_path, "src")
        if not os.path.exists(src_path):
            return {"exists": False}
        
        modules = []
        for item in os.listdir(src_path):
            item_path = os.path.join(src_path, item)
            if os.path.isdir(item_path):
                modules.append({
                    "name": item,
                    "file_count": len([f for f in os.listdir(item_path) 
                                     if f.endswith('.py')]) if os.path.isdir(item_path) else 0
                })
        
        return {"exists": True, "modules": modules}
    
    def analyze_ai_services(self):
        """تحليل خدمات الذكاء الاصطناعي"""
        ai_path = os.path.join(self.anubis_path, "src", "ai_services")
        if os.path.exists(ai_path):
            return {
                "exists": True,
                "services": os.listdir(ai_path) if os.path.isdir(ai_path) else []
            }
        return {"exists": False}
    
    def analyze_configuration(self):
        """تحليل ملفات التكوين"""
        config_path = os.path.join(self.anubis_path, "config")
        if not os.path.exists(config_path):
            return {"exists": False}
        
        config_files = [f for f in os.listdir(config_path) if f.endswith('.json')]
        return {
            "exists": True,
            "config_files": config_files,
            "total_configs": len(config_files),
            "has_ai_config": "ai_config.json" in config_files,
            "has_database_config": "database_config.json" in config_files
        }
    
    def analyze_docker_setup(self):
        """تحليل إعداد Docker"""
        docker_analysis = {
            "main_dockerfile": os.path.exists(os.path.join(self.anubis_path, "Dockerfile")),
            "main_compose": os.path.exists(os.path.join(self.anubis_path, "docker-compose.yml")),
            "docker_folder": self.analyze_docker_folder()
        }
        return docker_analysis
    
    def analyze_docker_folder(self):
        """تحليل مجلد Docker"""
        docker_path = os.path.join(self.anubis_path, "docker")
        if not os.path.exists(docker_path):
            return {"exists": False}
        
        docker_files = os.listdir(docker_path)
        return {
            "exists": True,
            "files": docker_files,
            "compose_files": [f for f in docker_files if f.startswith("docker-compose")],
            "dockerfiles": [f for f in docker_files if f.startswith("Dockerfile")]
        }
    
    def analyze_requirements(self):
        """تحليل ملفات المتطلبات"""
        req_files = [f for f in os.listdir(self.anubis_path) 
                    if f.startswith("requirements") and f.endswith(".txt")]
        
        requirements_analysis = {
            "total_files": len(req_files),
            "files": req_files,
            "detailed_analysis": {}
        }
        
        for req_file in req_files:
            try:
                with open(os.path.join(self.anubis_path, req_file), 'r') as f:
                    lines = f.readlines()
                    requirements_analysis["detailed_analysis"][req_file] = {
                        "total_packages": len([l for l in lines if l.strip() and not l.startswith('#')]),
                        "has_versions": any('==' in line for line in lines)
                    }
            except:
                requirements_analysis["detailed_analysis"][req_file] = {"error": "لا يمكن قراءة الملف"}
        
        return requirements_analysis
    
    def analyze_source_code(self):
        """تحليل الكود المصدري"""
        src_path = os.path.join(self.anubis_path, "src")
        if not os.path.exists(src_path):
            return {"exists": False}
        
        python_files = []
        for root, dirs, files in os.walk(src_path):
            for file in files:
                if file.endswith('.py'):
                    python_files.append(os.path.join(root, file))
        
        return {
            "exists": True,
            "total_python_files": len(python_files),
            "modules_structure": self.analyze_modules_structure(src_path)
        }
    
    def analyze_modules_structure(self, src_path):
        """تحليل هيكل الوحدات"""
        modules = {}
        for item in os.listdir(src_path):
            item_path = os.path.join(src_path, item)
            if os.path.isdir(item_path):
                py_files = [f for f in os.listdir(item_path) if f.endswith('.py')]
                modules[item] = {
                    "python_files": len(py_files),
                    "has_init": "__init__.py" in py_files
                }
        return modules
    
    def analyze_scripts_and_tools(self):
        """تحليل السكريبتات والأدوات"""
        scripts_path = os.path.join(self.anubis_path, "scripts")
        utilities_path = os.path.join(self.anubis_path, "utilities")
        
        return {
            "scripts": self.analyze_scripts_folder(scripts_path),
            "utilities": self.analyze_utilities_folder(utilities_path)
        }
    
    def analyze_scripts_folder(self, scripts_path):
        """تحليل مجلد السكريبتات"""
        if not os.path.exists(scripts_path):
            return {"exists": False}
        
        script_files = [f for f in os.listdir(scripts_path) 
                       if f.endswith(('.py', '.sh', '.bat'))]
        subdirs = [d for d in os.listdir(scripts_path) 
                  if os.path.isdir(os.path.join(scripts_path, d))]
        
        return {
            "exists": True,
            "script_files": script_files,
            "subdirectories": subdirs,
            "total_scripts": len(script_files)
        }
    
    def analyze_utilities_folder(self, utilities_path):
        """تحليل مجلد الأدوات"""
        if not os.path.exists(utilities_path):
            return {"exists": False}
        
        return {
            "exists": True,
            "subdirectories": [d for d in os.listdir(utilities_path) 
                             if os.path.isdir(os.path.join(utilities_path, d))]
        }
    
    def analyze_data_management(self):
        """تحليل إدارة البيانات"""
        data_path = os.path.join(self.anubis_path, "data")
        database_path = os.path.join(self.anubis_path, "database")
        
        return {
            "data_folder": {"exists": os.path.exists(data_path)},
            "database_folder": {"exists": os.path.exists(database_path)},
            "database_file": {"exists": os.path.exists(os.path.join(database_path, "anubis.db"))}
        }
    
    def analyze_security(self):
        """تحليل الأمان"""
        ssl_path = os.path.join(self.anubis_path, "ssl")
        security_path = os.path.join(self.anubis_path, "src", "security")
        
        return {
            "ssl_certificates": {"exists": os.path.exists(ssl_path)},
            "security_module": {"exists": os.path.exists(security_path)},
            "config_security": {"exists": os.path.exists(os.path.join(self.anubis_path, "config", "security"))}
        }
    
    def evaluate_production_readiness(self):
        """تقييم جاهزية الإنتاج"""
        score = 0
        max_score = 100
        issues = []
        recommendations = []
        
        # فحص الملفات الأساسية (20 نقطة)
        if os.path.exists(os.path.join(self.anubis_path, "main.py")):
            score += 10
        else:
            issues.append("ملف main.py مفقود")
            
        if os.path.exists(os.path.join(self.anubis_path, "requirements.txt")):
            score += 10
        else:
            issues.append("ملف requirements.txt مفقود")
        
        # فحص Docker (20 نقطة)
        if os.path.exists(os.path.join(self.anubis_path, "Dockerfile")):
            score += 10
        if os.path.exists(os.path.join(self.anubis_path, "docker-compose.yml")):
            score += 10
        
        # فحص الكود المصدري (20 نقطة)
        if os.path.exists(os.path.join(self.anubis_path, "src")):
            score += 20
        else:
            issues.append("مجلد src مفقود")
        
        # فحص التكوين (20 نقطة)
        if os.path.exists(os.path.join(self.anubis_path, "config")):
            score += 20
        else:
            issues.append("مجلد config مفقود")
        
        # فحص الاختبارات (10 نقاط)
        if os.path.exists(os.path.join(self.anubis_path, "tests")):
            score += 10
        else:
            recommendations.append("إضافة اختبارات شاملة")
        
        # فحص التوثيق (10 نقاط)
        if os.path.exists(os.path.join(self.anubis_path, "README.md")):
            score += 10
        else:
            recommendations.append("إضافة توثيق README")
        
        return {
            "readiness_score": score,
            "max_score": max_score,
            "percentage": f"{score}/{max_score} ({score/max_score*100:.1f}%)",
            "issues": issues,
            "recommendations": recommendations,
            "production_ready": score >= 80
        }
    
    def request_horus_team_consultation(self):
        """طلب استشارة من فريق حورس"""
        return {
            "consultation_request": "تحليل شامل لنظام ANUBIS_SYSTEM",
            "horus_team_analysis": {
                "THOTH_analysis": "⚡ تحليل سريع: النظام يحتوي على مكونات متقدمة ومتنوعة",
                "PTAH_development": "🔧 تقييم تطوير: هيكل احترافي مع إمكانيات توسع عالية",
                "RA_strategy": "🎯 تحليل استراتيجي: نظام شامل يحتاج تحسينات للإنتاج",
                "KHNUM_creativity": "💡 رؤية إبداعية: إمكانيات ابتكارية ممتازة",
                "SESHAT_documentation": "👁️ تحليل بصري: توثيق جيد يحتاج تحسين",
                "ANUBIS_security": "🔐 تقييم أمني: أسس أمان قوية مع حاجة لتعزيز",
                "MAAT_ethics": "⚖️ مراجعة أخلاقية: النظام يلتزم بمعايير جيدة",
                "HORUS_coordination": "𓅃 تنسيق عام: نظام متكامل جاهز للتطوير النهائي"
            },
            "team_recommendations": [
                "تحسين التوثيق والأدلة",
                "إضافة اختبارات شاملة",
                "تعزيز الأمان والحماية",
                "تحسين واجهات المستخدم",
                "إضافة مراقبة الأداء"
            ]
        }
    
    def generate_comprehensive_report(self, analysis):
        """إنشاء تقرير شامل"""
        report = {
            "timestamp": self.timestamp,
            "project_name": "ANUBIS_SYSTEM",
            "analysis_summary": {
                "total_files": analysis["project_overview"]["structure_analysis"]["total_files"],
                "total_directories": analysis["project_overview"]["structure_analysis"]["total_directories"],
                "readiness_score": analysis["readiness_evaluation"]["percentage"],
                "production_ready": analysis["readiness_evaluation"]["production_ready"]
            },
            "detailed_analysis": analysis
        }
        return report
    
    def save_analysis_report(self, report):
        """حفظ تقرير التحليل"""
        filename = f"horus_anubis_system_analysis_{self.timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"📄 تم حفظ التقرير: {filename}")
        return filename
    
    def print_summary_report(self, analysis):
        """طباعة ملخص التقرير"""
        print("\n" + "="*70)
        print("🏺 تقرير تحليل فريق حورس لنظام ANUBIS_SYSTEM")
        print("="*70)
        
        overview = analysis["project_overview"]
        readiness = analysis["readiness_evaluation"]
        
        print(f"📁 إجمالي الملفات: {overview['structure_analysis']['total_files']}")
        print(f"📂 إجمالي المجلدات: {overview['structure_analysis']['total_directories']}")
        print(f"🎯 درجة الجاهزية: {readiness['percentage']}")
        print(f"🚀 جاهز للإنتاج: {'✅ نعم' if readiness['production_ready'] else '⚠️ يحتاج تحسين'}")
        
        print("\n🔍 المكونات الرئيسية:")
        for component in overview["main_components"]:
            print(f"  • {component['name']}: {component['description']} ({component['file_count']} ملف)")
        
        print("\n🤖 استشارة فريق حورس:")
        horus_analysis = analysis["horus_consultation"]["horus_team_analysis"]
        for member, analysis_text in horus_analysis.items():
            print(f"  {analysis_text}")
        
        if readiness["issues"]:
            print("\n⚠️ المشاكل المكتشفة:")
            for issue in readiness["issues"]:
                print(f"  • {issue}")
        
        if readiness["recommendations"]:
            print("\n💡 التوصيات للتحسين:")
            for rec in readiness["recommendations"]:
                print(f"  • {rec}")
        
        print("\n" + "="*70)

def main():
    """الدالة الرئيسية"""
    print("🏺 مرحباً! فريق حورس سيحلل نظام ANUBIS_SYSTEM")
    print("𓅃 بعين حورس الثاقبة، سنفحص كل جانب من النظام...")
    
    analyzer = HorusAnubisSystemAnalyzer()
    
    try:
        # تحليل النظام
        analysis = analyzer.analyze_anubis_system()
        
        if analysis:
            # إنشاء التقرير الشامل
            report = analyzer.generate_comprehensive_report(analysis)
            
            # طباعة التقرير
            analyzer.print_summary_report(analysis)
            
            # حفظ التقرير
            filename = analyzer.save_analysis_report(report)
            
            print(f"\n✅ تم إكمال التحليل بنجاح!")
            print(f"📄 التقرير محفوظ في: {filename}")
            
            return report
        else:
            print("❌ فشل في تحليل النظام")
            return None
        
    except Exception as e:
        print(f"❌ خطأ في التحليل: {str(e)}")
        return None

if __name__ == "__main__":
    main()
