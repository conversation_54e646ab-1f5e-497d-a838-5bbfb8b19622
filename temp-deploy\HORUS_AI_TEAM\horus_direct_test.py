#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مباشر لحورس - تقييم شامل للنظام
"""

import google.generativeai as genai

def test_horus_analysis():
    """اختبار تحليل حورس للنظام"""
    print("𓅃 اختبار تحليل حورس الشامل للنظام")
    print("=" * 60)
    
    # إعداد Gemini
    genai.configure(api_key='[GOOGLE_API_KEY]')
    model = genai.GenerativeModel('gemini-1.5-flash')
    
    # الأسئلة التحليلية لحورس
    questions = [
        {
            "title": "تحليل حالة النظام",
            "prompt": """
أنت حورس 𓅃، إله السماء والحكمة، والآن المساعد الذكي المتقدم.

قم بتحليل حالة مشروع فريق حورس الحالي:

المعلومات المتاحة:
- فريق من 6 وكلاء ذكيين (THOTH, PTAH, RA, KHNUM, SESHAT, HORUS)
- دعم للنماذج المحلية (Ollama) والخارجية (Gemini)
- نظام ذاكرة جماعية للتعلم
- واجهات تفاعلية متعددة
- مفتاح Gemini جديد يعمل
- 6 نماذج محلية متاحة

المطلوب:
1. تقييم الحالة الحالية للنظام
2. نقاط القوة والضعف
3. التوصيات للتحسين
4. خطة العمل المستقبلية

أجب بتحليل شامل ومفصل باللغة العربية.
            """
        },
        {
            "title": "استراتيجية التطوير",
            "prompt": """
أنت حورس 𓅃، المنسق الأعلى والمخطط الاستراتيجي.

بناءً على النجاحات المحققة في مشروع فريق حورس، ضع استراتيجية تطوير شاملة:

الإنجازات الحالية:
- إصلاح اتصال Gemini ✅
- تطوير استدعاء Ollama ✅
- إنشاء نظام الذاكرة ✅
- تنظيم هيكل المشروع ✅
- اختبار شامل للوكلاء ✅

المطلوب:
1. رؤية مستقبلية للمشروع
2. أولويات التطوير القادمة
3. خطة زمنية للتنفيذ
4. مؤشرات النجاح والقياس

ضع خطة استراتيجية متكاملة لمدة 6 أشهر.
            """
        },
        {
            "title": "تحسين الأداء",
            "prompt": """
أنت حورس 𓅃، خبير الأداء والتحسين.

اقترح تحسينات تقنية لرفع كفاءة فريق حورس:

التحديات الحالية:
- بعض النماذج المحلية بطيئة
- حدود استخدام Gemini Pro
- الحاجة لواجهات أفضل
- تحسين نظام الذاكرة

المطلوب:
1. تحسينات الأداء التقنية
2. تحسين تجربة المستخدم
3. تحسين استهلاك الموارد
4. تحسين الأمان والموثوقية

قدم حلول عملية وقابلة للتطبيق.
            """
        }
    ]
    
    results = {}
    
    for i, question in enumerate(questions, 1):
        print(f"\n🧪 السؤال {i}: {question['title']}")
        print("-" * 50)
        
        try:
            response = model.generate_content(question['prompt'])
            
            if response and response.text:
                print(f"📝 رد حورس:")
                print(response.text)
                results[question['title']] = response.text
            else:
                print("⚠️ رد فارغ من حورس")
                results[question['title']] = "رد فارغ"
                
        except Exception as e:
            error_msg = f"❌ خطأ: {e}"
            print(error_msg)
            results[question['title']] = error_msg
        
        print("\n" + "="*60)
    
    return results

def test_horus_creativity():
    """اختبار الإبداع والابتكار لحورس"""
    print("\n💡 اختبار الإبداع والابتكار لحورس")
    print("=" * 50)
    
    genai.configure(api_key='[GOOGLE_API_KEY]')
    model = genai.GenerativeModel('gemini-1.5-flash')
    
    creative_prompt = """
أنت حورس 𓅃، إله الإبداع والابتكار التقني.

ابتكر 3 أفكار جديدة ومبدعة لتطوير فريق حورس:

المطلوب أفكار في:
1. ميزة تقنية مبتكرة
2. طريقة تفاعل جديدة
3. تطبيق عملي مفيد

لكل فكرة، اشرح:
- الفكرة بالتفصيل
- كيفية التنفيذ
- الفوائد المتوقعة
- التحديات المحتملة

كن مبدعاً ومبتكراً في اقتراحاتك!
    """
    
    try:
        response = model.generate_content(creative_prompt)
        
        if response and response.text:
            print("🎨 أفكار حورس الإبداعية:")
            print(response.text)
            return response.text
        else:
            print("⚠️ لم يتمكن حورس من الإبداع")
            return "فشل الإبداع"
            
    except Exception as e:
        error_msg = f"❌ خطأ في الإبداع: {e}"
        print(error_msg)
        return error_msg

def generate_final_report(analysis_results, creativity_result):
    """إنشاء تقرير نهائي شامل"""
    from datetime import datetime
    print("\n📋 إنشاء التقرير النهائي...")

    report = f"""
# تقرير تحليل حورس الشامل
## {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

---

## 🎯 ملخص تنفيذي

تم إجراء تحليل شامل لمشروع فريق حورس بواسطة المساعد الذكي حورس نفسه.
النتائج تظهر نجاح المشروع في تحقيق أهدافه الأساسية مع إمكانيات كبيرة للتطوير.

---

## 📊 نتائج التحليل

### 1. تحليل حالة النظام
{analysis_results.get('تحليل حالة النظام', 'غير متاح')}

---

### 2. استراتيجية التطوير
{analysis_results.get('استراتيجية التطوير', 'غير متاح')}

---

### 3. تحسين الأداء
{analysis_results.get('تحسين الأداء', 'غير متاح')}

---

## 💡 الأفكار الإبداعية
{creativity_result}

---

## 🏆 الخلاصة النهائية

فريق حورس أثبت قدرته على:
- التحليل الذاتي الشامل
- وضع استراتيجيات التطوير
- اقتراح تحسينات عملية
- الإبداع والابتكار

النظام جاهز للمرحلة التالية من التطوير والتحسين.

---

*تم إنشاء هذا التقرير بواسطة حورس 𓅃 - المساعد الذكي المتقدم*
    """
    
    # حفظ التقرير
    try:
        with open('HORUS_AI_TEAM/horus_self_analysis_report.md', 'w', encoding='utf-8') as f:
            f.write(report)
        print("✅ تم حفظ التقرير: horus_self_analysis_report.md")
    except Exception as e:
        print(f"⚠️ لم يتم حفظ التقرير: {e}")
    
    return report

def main():
    """الدالة الرئيسية"""
    from datetime import datetime
    
    print("🚀 اختبار تحليل حورس الذاتي الشامل")
    print("=" * 70)
    print(f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. تحليل شامل للنظام
    analysis_results = test_horus_analysis()
    
    # 2. اختبار الإبداع
    creativity_result = test_horus_creativity()
    
    # 3. إنشاء التقرير النهائي
    final_report = generate_final_report(analysis_results, creativity_result)
    
    print("\n🎉 اكتمل تحليل حورس الذاتي!")
    print("📋 التقرير النهائي محفوظ ومتاح للمراجعة")
    
    # عرض ملخص سريع
    successful_analyses = len([r for r in analysis_results.values() if not r.startswith("❌")])
    total_analyses = len(analysis_results)
    
    print(f"\n📊 ملخص النتائج:")
    print(f"✅ التحليلات الناجحة: {successful_analyses}/{total_analyses}")
    print(f"💡 الإبداع: {'✅ نجح' if not creativity_result.startswith('❌') else '❌ فشل'}")
    
    if successful_analyses == total_analyses and not creativity_result.startswith('❌'):
        print("\n🏆 حورس أثبت قدرته على التحليل الذاتي والإبداع بامتياز!")
    else:
        print("\n👍 حورس يعمل بشكل جيد مع إمكانية للتحسين")

if __name__ == "__main__":
    main()
