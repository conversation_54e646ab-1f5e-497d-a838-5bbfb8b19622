#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 محلل ومنظم مشروع فريق حورس الشامل
HORUS AI Team Project Analyzer & Organizer

نظام شامل لفحص وتحليل وتنظيم وترتيب مشروع فريق حورس
Comprehensive system for analyzing, organizing and structuring Horus AI Team project
"""

import os
import json
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class HorusProjectAnalyzerOrganizer:
    """🔍 محلل ومنظم مشروع فريق حورس"""
    
    def __init__(self):
        """تهيئة المحلل والمنظم"""
        self.project_dir = Path(__file__).parent
        self.analysis_dir = self.project_dir / "project_analysis"
        self.analysis_dir.mkdir(exist_ok=True)
        
        # إحصائيات المشروع
        self.project_stats = {
            "total_files": 0,
            "python_files": 0,
            "json_files": 0,
            "markdown_files": 0,
            "directories": 0,
            "total_size": 0
        }
        
        # تصنيف الملفات
        self.file_categories = {
            "core_systems": [],
            "analysis_reports": [],
            "configuration": [],
            "documentation": [],
            "memory_system": [],
            "collaboration_tools": [],
            "requirements": [],
            "cache_files": [],
            "temporary_files": []
        }
        
        # الهيكل المقترح الجديد
        self.proposed_structure = {
            "01_core": {
                "description": "الأنظمة الأساسية والمحركات الرئيسية",
                "subdirs": ["engines", "managers", "interfaces"]
            },
            "02_team_members": {
                "description": "تكوينات وإعدادات أعضاء الفريق",
                "subdirs": ["local_models", "external_models", "configurations"]
            },
            "03_memory_system": {
                "description": "نظام الذاكرة والتعلم الجماعي",
                "subdirs": ["brain", "memory", "learning", "patterns"]
            },
            "04_collaboration": {
                "description": "أدوات التعاون والتنسيق",
                "subdirs": ["helpers", "systems", "workflows"]
            },
            "05_analysis": {
                "description": "التحليلات والتقارير",
                "subdirs": ["reports", "consultations", "enhancements"]
            },
            "06_documentation": {
                "description": "التوثيق والأدلة",
                "subdirs": ["guides", "reports", "plans"]
            },
            "07_configuration": {
                "description": "ملفات التكوين والإعدادات",
                "subdirs": ["requirements", "configs", "settings"]
            },
            "08_utilities": {
                "description": "الأدوات المساعدة والمرافق",
                "subdirs": ["helpers", "tools", "scripts"]
            },
            "09_archive": {
                "description": "الملفات المؤرشفة والقديمة",
                "subdirs": ["old_versions", "deprecated", "backup"]
            }
        }
        
        logger.info("🔍 تم تهيئة محلل ومنظم مشروع فريق حورس")
    
    def scan_project_structure(self) -> Dict[str, Any]:
        """فحص هيكل المشروع الحالي"""
        logger.info("📁 فحص هيكل المشروع الحالي...")
        
        structure = {
            "scan_time": datetime.now().isoformat(),
            "project_path": str(self.project_dir),
            "directories": {},
            "files": {},
            "statistics": {}
        }
        
        # فحص جميع الملفات والمجلدات
        for item in self.project_dir.rglob("*"):
            if item.is_file():
                self.project_stats["total_files"] += 1
                self.project_stats["total_size"] += item.stat().st_size
                
                # تصنيف الملفات حسب النوع
                if item.suffix == ".py":
                    self.project_stats["python_files"] += 1
                elif item.suffix == ".json":
                    self.project_stats["json_files"] += 1
                elif item.suffix == ".md":
                    self.project_stats["markdown_files"] += 1
                
                # إضافة معلومات الملف
                relative_path = item.relative_to(self.project_dir)
                structure["files"][str(relative_path)] = {
                    "size": item.stat().st_size,
                    "modified": datetime.fromtimestamp(item.stat().st_mtime).isoformat(),
                    "type": item.suffix,
                    "category": self.categorize_file(item)
                }
                
            elif item.is_dir() and item != self.project_dir:
                self.project_stats["directories"] += 1
                relative_path = item.relative_to(self.project_dir)
                structure["directories"][str(relative_path)] = {
                    "files_count": len(list(item.glob("*"))),
                    "subdirs_count": len([d for d in item.iterdir() if d.is_dir()])
                }
        
        structure["statistics"] = self.project_stats
        return structure
    
    def categorize_file(self, file_path: Path) -> str:
        """تصنيف الملف حسب نوعه ووظيفته"""
        file_name = file_path.name.lower()
        file_suffix = file_path.suffix.lower()
        
        # تصنيف حسب الاسم والمحتوى
        if "team_workflow_manager" in file_name or "horus_interface" in file_name:
            self.file_categories["core_systems"].append(str(file_path))
            return "core_systems"
        elif "analysis" in file_name or "report" in file_name or "consultation" in file_name:
            self.file_categories["analysis_reports"].append(str(file_path))
            return "analysis_reports"
        elif "config" in file_name or file_suffix == ".json":
            self.file_categories["configuration"].append(str(file_path))
            return "configuration"
        elif file_suffix == ".md" or "readme" in file_name:
            self.file_categories["documentation"].append(str(file_path))
            return "documentation"
        elif "memory" in file_name or "brain" in file_name:
            self.file_categories["memory_system"].append(str(file_path))
            return "memory_system"
        elif "collaboration" in file_name or "helper" in file_name:
            self.file_categories["collaboration_tools"].append(str(file_path))
            return "collaboration_tools"
        elif "requirements" in file_name:
            self.file_categories["requirements"].append(str(file_path))
            return "requirements"
        elif "__pycache__" in str(file_path) or file_suffix == ".pyc":
            self.file_categories["cache_files"].append(str(file_path))
            return "cache_files"
        else:
            return "uncategorized"
    
    def analyze_code_quality(self) -> Dict[str, Any]:
        """تحليل جودة الكود"""
        logger.info("🔍 تحليل جودة الكود...")
        
        quality_analysis = {
            "python_files_analysis": {},
            "documentation_coverage": 0,
            "code_organization": "good",
            "recommendations": []
        }
        
        python_files = [f for f in self.project_dir.rglob("*.py") if "__pycache__" not in str(f)]
        
        for py_file in python_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                analysis = {
                    "lines_count": len(content.splitlines()),
                    "has_docstring": '"""' in content or "'''" in content,
                    "has_imports": "import " in content,
                    "has_classes": "class " in content,
                    "has_functions": "def " in content,
                    "has_main": "if __name__" in content
                }
                
                relative_path = py_file.relative_to(self.project_dir)
                quality_analysis["python_files_analysis"][str(relative_path)] = analysis
                
            except Exception as e:
                logger.warning(f"⚠️ لا يمكن تحليل الملف {py_file}: {e}")
        
        # حساب تغطية التوثيق
        documented_files = sum(1 for analysis in quality_analysis["python_files_analysis"].values() 
                             if analysis["has_docstring"])
        total_files = len(quality_analysis["python_files_analysis"])
        
        if total_files > 0:
            quality_analysis["documentation_coverage"] = (documented_files / total_files) * 100
        
        # إضافة التوصيات
        if quality_analysis["documentation_coverage"] < 80:
            quality_analysis["recommendations"].append("تحسين تغطية التوثيق")
        
        if len(python_files) > 20:
            quality_analysis["recommendations"].append("تنظيم الملفات في مجلدات فرعية")
        
        return quality_analysis
    
    def identify_duplicate_files(self) -> Dict[str, List[str]]:
        """تحديد الملفات المكررة أو المتشابهة"""
        logger.info("🔍 البحث عن الملفات المكررة...")
        
        duplicates = {}
        file_sizes = {}
        
        # تجميع الملفات حسب الحجم
        for file_path in self.project_dir.rglob("*"):
            if file_path.is_file():
                size = file_path.stat().st_size
                if size not in file_sizes:
                    file_sizes[size] = []
                file_sizes[size].append(str(file_path))
        
        # البحث عن الملفات بنفس الحجم
        for size, files in file_sizes.items():
            if len(files) > 1:
                duplicates[f"size_{size}"] = files
        
        # البحث عن الملفات بأسماء متشابهة
        similar_names = {}
        for file_path in self.project_dir.rglob("*.py"):
            base_name = file_path.stem.lower()
            if base_name not in similar_names:
                similar_names[base_name] = []
            similar_names[base_name].append(str(file_path))
        
        for name, files in similar_names.items():
            if len(files) > 1:
                duplicates[f"similar_name_{name}"] = files
        
        return duplicates
    
    def create_organization_plan(self) -> Dict[str, Any]:
        """إنشاء خطة تنظيم المشروع"""
        logger.info("📋 إنشاء خطة تنظيم المشروع...")
        
        organization_plan = {
            "plan_created": datetime.now().isoformat(),
            "current_structure_issues": [],
            "proposed_new_structure": self.proposed_structure,
            "file_migration_plan": {},
            "cleanup_recommendations": [],
            "implementation_steps": []
        }
        
        # تحديد مشاكل الهيكل الحالي
        if self.project_stats["python_files"] > 15:
            organization_plan["current_structure_issues"].append("عدد كبير من ملفات Python في المجلد الرئيسي")
        
        if len(self.file_categories["cache_files"]) > 0:
            organization_plan["current_structure_issues"].append("وجود ملفات cache غير ضرورية")
        
        if len([f for f in self.project_dir.glob("*.py")]) > 10:
            organization_plan["current_structure_issues"].append("ملفات Python كثيرة في الجذر")
        
        # خطة نقل الملفات
        for category, files in self.file_categories.items():
            if category == "core_systems":
                organization_plan["file_migration_plan"]["01_core/engines/"] = files
            elif category == "memory_system":
                organization_plan["file_migration_plan"]["03_memory_system/"] = files
            elif category == "collaboration_tools":
                organization_plan["file_migration_plan"]["04_collaboration/"] = files
            elif category == "analysis_reports":
                organization_plan["file_migration_plan"]["05_analysis/"] = files
            elif category == "documentation":
                organization_plan["file_migration_plan"]["06_documentation/"] = files
            elif category == "configuration":
                organization_plan["file_migration_plan"]["07_configuration/"] = files
            elif category == "requirements":
                organization_plan["file_migration_plan"]["07_configuration/requirements/"] = files
        
        # توصيات التنظيف
        if len(self.file_categories["cache_files"]) > 0:
            organization_plan["cleanup_recommendations"].append("حذف ملفات __pycache__")
        
        # خطوات التنفيذ
        organization_plan["implementation_steps"] = [
            "1. إنشاء النسخة الاحتياطية من المشروع",
            "2. إنشاء الهيكل الجديد للمجلدات",
            "3. نقل الملفات حسب التصنيف",
            "4. تحديث المسارات في الكود",
            "5. اختبار النظام بعد التنظيم",
            "6. تنظيف الملفات غير الضرورية",
            "7. تحديث التوثيق"
        ]
        
        return organization_plan
    
    def generate_comprehensive_report(self) -> Dict[str, Any]:
        """إنشاء تقرير شامل للمشروع"""
        logger.info("📊 إنشاء تقرير شامل للمشروع...")
        
        # جمع جميع التحليلات
        project_structure = self.scan_project_structure()
        code_quality = self.analyze_code_quality()
        duplicates = self.identify_duplicate_files()
        organization_plan = self.create_organization_plan()
        
        comprehensive_report = {
            "report_title": "تقرير تحليل وتنظيم مشروع فريق حورس الشامل",
            "generated_at": datetime.now().isoformat(),
            "project_overview": {
                "total_files": self.project_stats["total_files"],
                "python_files": self.project_stats["python_files"],
                "directories": self.project_stats["directories"],
                "total_size_mb": round(self.project_stats["total_size"] / (1024 * 1024), 2)
            },
            "project_structure": project_structure,
            "code_quality_analysis": code_quality,
            "duplicate_files": duplicates,
            "organization_plan": organization_plan,
            "file_categories": self.file_categories,
            "recommendations": {
                "immediate_actions": [
                    "حذف ملفات __pycache__ غير الضرورية",
                    "تنظيم ملفات Python في مجلدات متخصصة",
                    "تحسين تغطية التوثيق"
                ],
                "structural_improvements": [
                    "تطبيق الهيكل المقترح الجديد",
                    "فصل الأنظمة الأساسية عن الأدوات المساعدة",
                    "إنشاء مجلد منفصل للتحليلات والتقارير"
                ],
                "maintenance": [
                    "إنشاء نظام نسخ احتياطية دوري",
                    "تطبيق معايير تسمية موحدة",
                    "إضافة اختبارات للأنظمة الأساسية"
                ]
            },
            "priority_score": self.calculate_priority_score()
        }
        
        return comprehensive_report
    
    def calculate_priority_score(self) -> Dict[str, int]:
        """حساب نقاط الأولوية للتحسينات"""
        scores = {
            "organization": 0,
            "cleanup": 0,
            "documentation": 0,
            "code_quality": 0
        }
        
        # نقاط التنظيم
        if self.project_stats["python_files"] > 15:
            scores["organization"] += 8
        if len([f for f in self.project_dir.glob("*.py")]) > 10:
            scores["organization"] += 6
        
        # نقاط التنظيف
        if len(self.file_categories["cache_files"]) > 0:
            scores["cleanup"] += 7
        
        # نقاط التوثيق
        if self.project_stats["markdown_files"] < 5:
            scores["documentation"] += 5
        
        # نقاط جودة الكود
        if self.project_stats["python_files"] > 20:
            scores["code_quality"] += 6
        
        return scores
    
    def save_analysis_report(self, report: Dict[str, Any]) -> str:
        """حفظ تقرير التحليل"""
        try:
            file_path = self.analysis_dir / f"comprehensive_project_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            logger.info(f"💾 تم حفظ تقرير التحليل: {file_path}")
            return str(file_path)
            
        except Exception as e:
            logger.error(f"❌ خطأ في حفظ التقرير: {e}")
            return ""

async def main():
    """الدالة الرئيسية"""
    print("🔍 محلل ومنظم مشروع فريق حورس الشامل")
    print("=" * 80)
    
    # إنشاء المحلل والمنظم
    analyzer = HorusProjectAnalyzerOrganizer()
    
    # إنشاء التقرير الشامل
    print("\n📊 إنشاء تقرير التحليل الشامل...")
    comprehensive_report = analyzer.generate_comprehensive_report()
    
    # حفظ التقرير
    report_file = analyzer.save_analysis_report(comprehensive_report)
    
    print(f"\n✅ نتائج التحليل:")
    print(f"   📁 إجمالي الملفات: {comprehensive_report['project_overview']['total_files']}")
    print(f"   🐍 ملفات Python: {comprehensive_report['project_overview']['python_files']}")
    print(f"   📂 المجلدات: {comprehensive_report['project_overview']['directories']}")
    print(f"   💾 الحجم الإجمالي: {comprehensive_report['project_overview']['total_size_mb']} MB")
    
    print(f"\n🎯 نقاط الأولوية:")
    for category, score in comprehensive_report['priority_score'].items():
        print(f"   📊 {category}: {score}/10")
    
    print(f"\n💡 التوصيات الفورية:")
    for action in comprehensive_report['recommendations']['immediate_actions']:
        print(f"   ⚡ {action}")
    
    print(f"\n📁 ملف التقرير: {report_file}")
    
    print(f"\n🚀 الخطوات التالية:")
    print(f"   1. مراجعة التقرير الشامل")
    print(f"   2. تطبيق التوصيات الفورية")
    print(f"   3. تنفيذ خطة التنظيم المقترحة")
    print(f"   4. اختبار النظام بعد التنظيم")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
