#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 نظام الاختبار الشامل لفريق حورس
𓅃 اختبار جميع مكونات النظام والوكلاء

تم إنشاؤه وفقاً لقواعد التطوير المعتمدة
الموقع: HORUS_AI_TEAM/05_analysis/tools/
"""

import os
import sys
import json
import time
import subprocess
import importlib.util
from datetime import datetime
from typing import Dict, List, Tuple, Optional
from pathlib import Path

class HorusSystemTester:
    """نظام اختبار شامل لفريق حورس"""
    
    def __init__(self):
        """تهيئة نظام الاختبار"""
        self.project_root = Path(__file__).parent.parent.parent
        self.test_results = {
            "timestamp": datetime.now().isoformat(),
            "tests": {},
            "summary": {},
            "errors": [],
            "warnings": []
        }
        
        print("🧪 نظام الاختبار الشامل لفريق حورس")
        print("=" * 60)
        print(f"📍 مجلد المشروع: {self.project_root}")
        print(f"⏰ وقت البدء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()

    def test_project_structure(self) -> Dict:
        """اختبار هيكل المشروع"""
        print("🏗️ اختبار هيكل المشروع...")
        
        required_folders = [
            "01_core",
            "02_team_members", 
            "03_memory_system",
            "04_specialized_agents",
            "05_analysis",
            "06_documentation",
            "07_configuration",
            "08_utilities",
            "09_archive"
        ]
        
        results = {
            "status": "success",
            "folders_found": 0,
            "folders_missing": [],
            "total_files": 0,
            "details": {}
        }
        
        for folder in required_folders:
            folder_path = self.project_root / folder
            if folder_path.exists():
                results["folders_found"] += 1
                file_count = len(list(folder_path.rglob("*")))
                results["details"][folder] = {
                    "exists": True,
                    "files": file_count
                }
                results["total_files"] += file_count
                print(f"  ✅ {folder} - {file_count} ملف")
            else:
                results["folders_missing"].append(folder)
                results["details"][folder] = {"exists": False}
                print(f"  ❌ {folder} - مفقود")
        
        if results["folders_missing"]:
            results["status"] = "warning"
            
        print(f"📊 النتيجة: {results['folders_found']}/{len(required_folders)} مجلدات موجودة")
        print()
        
        return results

    def test_team_members(self) -> Dict:
        """اختبار أعضاء الفريق"""
        print("👥 اختبار أعضاء الفريق...")
        
        team_members_path = self.project_root / "02_team_members"
        results = {
            "status": "success",
            "agents_found": 0,
            "agents_tested": 0,
            "agents_working": 0,
            "agents": {}
        }
        
        if not team_members_path.exists():
            results["status"] = "error"
            results["error"] = "مجلد أعضاء الفريق غير موجود"
            return results
        
        # البحث عن ملفات الوكلاء
        agent_files = list(team_members_path.glob("*_agent.py"))
        results["agents_found"] = len(agent_files)
        
        for agent_file in agent_files:
            agent_name = agent_file.stem
            print(f"  🤖 اختبار {agent_name}...")
            
            agent_result = self._test_agent_file(agent_file)
            results["agents"][agent_name] = agent_result
            results["agents_tested"] += 1
            
            if agent_result["status"] == "success":
                results["agents_working"] += 1
                print(f"    ✅ يعمل بشكل صحيح")
            else:
                print(f"    ❌ خطأ: {agent_result.get('error', 'غير محدد')}")
        
        # اختبار الوكيل المتخصص (web_research_agent)
        web_agent_path = self.project_root / "04_specialized_agents" / "web_research_agent"
        if web_agent_path.exists():
            print(f"  🌐 اختبار web_research_agent...")
            web_result = self._test_web_research_agent(web_agent_path)
            results["agents"]["web_research_agent"] = web_result
            results["agents_tested"] += 1
            if web_result["status"] == "success":
                results["agents_working"] += 1
                print(f"    ✅ يعمل بشكل صحيح")
            else:
                print(f"    ❌ خطأ: {web_result.get('error', 'غير محدد')}")
        
        print(f"📊 النتيجة: {results['agents_working']}/{results['agents_tested']} وكلاء يعملون")
        print()
        
        return results

    def _test_agent_file(self, agent_file: Path) -> Dict:
        """اختبار ملف وكيل محدد"""
        try:
            # قراءة الملف والتحقق من البنية
            with open(agent_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            result = {
                "status": "success",
                "file_size": len(content),
                "lines": len(content.split('\n')),
                "has_class": "class " in content,
                "has_imports": "import " in content,
                "encoding": "utf-8"
            }
            
            # محاولة استيراد الملف
            spec = importlib.util.spec_from_file_location("agent_module", agent_file)
            if spec and spec.loader:
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                result["importable"] = True
            else:
                result["importable"] = False
                result["status"] = "warning"
                
        except Exception as e:
            result = {
                "status": "error",
                "error": str(e),
                "importable": False
            }
            
        return result

    def _test_web_research_agent(self, web_agent_path: Path) -> Dict:
        """اختبار وكيل البحث على الويب"""
        try:
            agent_file = web_agent_path / "agent" / "anubis_web_researcher.py"
            if not agent_file.exists():
                return {"status": "error", "error": "ملف الوكيل غير موجود"}
            
            with open(agent_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            result = {
                "status": "success",
                "file_size": len(content),
                "lines": len(content.split('\n')),
                "has_anubis_class": "class AnubisWebResearcher" in content,
                "has_gemini": "genai" in content,
                "has_tools": "WebSearchTools" in content,
                "readme_exists": (web_agent_path / "README.md").exists()
            }
            
        except Exception as e:
            result = {"status": "error", "error": str(e)}
            
        return result

    def test_core_systems(self) -> Dict:
        """اختبار الأنظمة الأساسية"""
        print("⚙️ اختبار الأنظمة الأساسية...")
        
        core_path = self.project_root / "01_core"
        results = {
            "status": "success",
            "interfaces": {},
            "engines": {},
            "managers": {}
        }
        
        if not core_path.exists():
            results["status"] = "error"
            results["error"] = "مجلد الأنظمة الأساسية غير موجود"
            return results
        
        # اختبار الواجهات
        interfaces_path = core_path / "interfaces"
        if interfaces_path.exists():
            interface_files = list(interfaces_path.glob("*.py"))
            for interface_file in interface_files:
                name = interface_file.stem
                results["interfaces"][name] = self._test_python_file(interface_file)
                status = "✅" if results["interfaces"][name]["status"] == "success" else "❌"
                print(f"  {status} واجهة {name}")
        
        # اختبار المحركات
        engines_path = core_path / "engines"
        if engines_path.exists():
            engine_files = list(engines_path.glob("*.py"))
            for engine_file in engine_files:
                name = engine_file.stem
                results["engines"][name] = self._test_python_file(engine_file)
                status = "✅" if results["engines"][name]["status"] == "success" else "❌"
                print(f"  {status} محرك {name}")
        
        print()
        return results

    def test_memory_system(self) -> Dict:
        """اختبار نظام الذاكرة"""
        print("🧠 اختبار نظام الذاكرة...")
        
        memory_path = self.project_root / "03_memory_system"
        results = {"status": "success", "components": {}}
        
        if not memory_path.exists():
            results["status"] = "error"
            results["error"] = "نظام الذاكرة غير موجود"
            print("  ❌ نظام الذاكرة غير موجود")
            return results
        
        memory_files = list(memory_path.glob("*.py"))
        for memory_file in memory_files:
            name = memory_file.stem
            results["components"][name] = self._test_python_file(memory_file)
            status = "✅" if results["components"][name]["status"] == "success" else "❌"
            print(f"  {status} مكون الذاكرة {name}")
        
        print()
        return results

    def _test_python_file(self, file_path: Path) -> Dict:
        """اختبار ملف Python عام"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # اختبارات أساسية
            result = {
                "status": "success",
                "file_size": len(content),
                "lines": len(content.split('\n')),
                "has_imports": "import " in content,
                "has_functions": "def " in content,
                "has_classes": "class " in content,
                "syntax_valid": True
            }
            
            # اختبار صحة الصيغة
            try:
                compile(content, file_path, 'exec')
            except SyntaxError:
                result["syntax_valid"] = False
                result["status"] = "error"
                
        except Exception as e:
            result = {"status": "error", "error": str(e)}
            
        return result

    def test_documentation(self) -> Dict:
        """اختبار التوثيق"""
        print("📚 اختبار التوثيق...")
        
        docs_path = self.project_root / "06_documentation"
        results = {"status": "success", "files": {}}
        
        if not docs_path.exists():
            results["status"] = "warning"
            results["warning"] = "مجلد التوثيق غير موجود"
            print("  ⚠️ مجلد التوثيق غير موجود")
            return results
        
        # البحث عن ملفات README
        readme_files = list(self.project_root.rglob("README.md"))
        results["readme_count"] = len(readme_files)
        
        for readme in readme_files[:5]:  # أول 5 ملفات
            rel_path = readme.relative_to(self.project_root)
            try:
                with open(readme, 'r', encoding='utf-8') as f:
                    content = f.read()
                results["files"][str(rel_path)] = {
                    "status": "success",
                    "size": len(content),
                    "lines": len(content.split('\n'))
                }
                print(f"  ✅ {rel_path}")
            except Exception as e:
                results["files"][str(rel_path)] = {"status": "error", "error": str(e)}
                print(f"  ❌ {rel_path}")
        
        print(f"📊 وجد {results['readme_count']} ملف README")
        print()
        return results

    def run_comprehensive_test(self) -> Dict:
        """تشغيل الاختبار الشامل"""
        print("🚀 بدء الاختبار الشامل لفريق حورس")
        print("=" * 60)
        
        # تشغيل جميع الاختبارات
        self.test_results["tests"]["structure"] = self.test_project_structure()
        self.test_results["tests"]["team_members"] = self.test_team_members()
        self.test_results["tests"]["core_systems"] = self.test_core_systems()
        self.test_results["tests"]["memory_system"] = self.test_memory_system()
        self.test_results["tests"]["documentation"] = self.test_documentation()
        
        # حساب الملخص
        self._calculate_summary()
        
        # حفظ النتائج
        self._save_results()
        
        # عرض الملخص النهائي
        self._display_summary()
        
        return self.test_results

    def _calculate_summary(self):
        """حساب ملخص النتائج"""
        tests = self.test_results["tests"]
        
        total_tests = 0
        passed_tests = 0
        
        for test_name, test_result in tests.items():
            total_tests += 1
            if test_result.get("status") == "success":
                passed_tests += 1
        
        self.test_results["summary"] = {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "success_rate": (passed_tests / total_tests * 100) if total_tests > 0 else 0,
            "overall_status": "success" if passed_tests == total_tests else "warning"
        }

    def _save_results(self):
        """حفظ نتائج الاختبار"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = self.project_root / "05_analysis" / "reports" / f"system_test_results_{timestamp}.json"
        
        # إنشاء المجلد إذا لم يكن موجوداً
        results_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        print(f"💾 تم حفظ النتائج في: {results_file}")

    def _display_summary(self):
        """عرض ملخص النتائج"""
        summary = self.test_results["summary"]
        
        print("\n" + "=" * 60)
        print("📊 ملخص نتائج الاختبار الشامل")
        print("=" * 60)
        
        print(f"🧪 إجمالي الاختبارات: {summary['total_tests']}")
        print(f"✅ الاختبارات الناجحة: {summary['passed_tests']}")
        print(f"📈 معدل النجاح: {summary['success_rate']:.1f}%")
        
        if summary["overall_status"] == "success":
            print("🎉 النتيجة الإجمالية: نجح النظام في جميع الاختبارات!")
        else:
            print("⚠️ النتيجة الإجمالية: يحتاج النظام إلى بعض التحسينات")
        
        print("\n🔍 تفاصيل الاختبارات:")
        for test_name, test_result in self.test_results["tests"].items():
            status_icon = "✅" if test_result.get("status") == "success" else "⚠️" if test_result.get("status") == "warning" else "❌"
            print(f"  {status_icon} {test_name}")
        
        print(f"\n⏰ وقت الانتهاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)

def main():
    """الدالة الرئيسية"""
    tester = HorusSystemTester()
    results = tester.run_comprehensive_test()
    return results

if __name__ == "__main__":
    main()
