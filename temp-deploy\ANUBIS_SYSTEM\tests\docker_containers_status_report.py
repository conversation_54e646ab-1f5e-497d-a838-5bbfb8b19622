#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🐳 تقرير حالة حاويات Docker لمشروع Universal AI Assistants
Docker Containers Status Report for Universal AI Assistants Project
"""

import subprocess
import json
import sys
from datetime import datetime

class DockerContainersAnalyzer:
    def __init__(self):
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.containers_info = []
        
    def run_docker_command(self, command):
        """تشغيل أمر Docker وإرجاع النتيجة"""
        try:
            result = subprocess.run(
                command.split(), 
                capture_output=True, 
                text=True, 
                timeout=30
            )
            if result.returncode == 0:
                return result.stdout.strip()
            else:
                return f"خطأ: {result.stderr.strip()}"
        except subprocess.TimeoutExpired:
            return "خطأ: انتهت مهلة الأمر"
        except Exception as e:
            return f"خطأ: {str(e)}"
    
    def analyze_containers(self):
        """تحليل جميع الحاويات"""
        print("🐳 بدء تحليل حاويات Docker...")
        print("="*80)
        
        # الحصول على قائمة جميع الحاويات
        containers_output = self.run_docker_command("docker ps -a --format json")
        
        if "خطأ" in containers_output:
            print(f"❌ {containers_output}")
            return
        
        # تحليل كل حاوية
        for line in containers_output.split('\n'):
            if line.strip():
                try:
                    container = json.loads(line)
                    self.analyze_single_container(container)
                except json.JSONDecodeError:
                    continue
        
        # إنشاء التقرير النهائي
        self.generate_report()
    
    def analyze_single_container(self, container):
        """تحليل حاوية واحدة"""
        container_name = container.get('Names', 'Unknown')
        container_id = container.get('ID', 'Unknown')
        image = container.get('Image', 'Unknown')
        status = container.get('Status', 'Unknown')
        state = container.get('State', 'Unknown')
        ports = container.get('Ports', 'None')
        
        print(f"\n🔍 تحليل الحاوية: {container_name}")
        print("-" * 60)
        
        container_info = {
            "name": container_name,
            "id": container_id,
            "image": image,
            "status": status,
            "state": state,
            "ports": ports,
            "analysis": {}
        }
        
        # تحليل خاص بحاويات أنوبيس
        if "anubis" in container_name.lower() or "universal-ai-assistants" in image.lower():
            container_info["type"] = "anubis"
            container_info["analysis"] = self.analyze_anubis_container(container_name, container_id)
        elif "n8n" in container_name.lower():
            container_info["type"] = "automation"
            container_info["analysis"] = self.analyze_n8n_container(container_name, container_id)
        elif "grafana" in container_name.lower():
            container_info["type"] = "monitoring"
            container_info["analysis"] = self.analyze_monitoring_container(container_name, container_id)
        else:
            container_info["type"] = "other"
            container_info["analysis"] = {"status": "تم فحصها"}
        
        self.containers_info.append(container_info)
        
        # طباعة النتائج
        print(f"📊 النوع: {container_info['type']}")
        print(f"🏷️ الصورة: {image}")
        print(f"📈 الحالة: {status}")
        print(f"🔌 المنافذ: {ports}")
        
        for key, value in container_info["analysis"].items():
            print(f"🔍 {key}: {value}")
    
    def analyze_anubis_container(self, name, container_id):
        """تحليل حاوية أنوبيس"""
        analysis = {}
        
        # فحص السجلات
        logs = self.run_docker_command(f"docker logs {name} --tail 10")
        if "خطأ" not in logs:
            if "error" in logs.lower() or "exception" in logs.lower():
                analysis["logs_status"] = "❌ يحتوي على أخطاء"
            elif logs.strip():
                analysis["logs_status"] = "✅ سجلات طبيعية"
            else:
                analysis["logs_status"] = "⚠️ لا توجد سجلات"
        else:
            analysis["logs_status"] = "❌ لا يمكن قراءة السجلات"
        
        # فحص التكوين
        inspect_result = self.run_docker_command(f"docker inspect {name}")
        if "خطأ" not in inspect_result:
            try:
                inspect_data = json.loads(inspect_result)[0]
                config = inspect_data.get("Config", {})
                
                # فحص متغيرات البيئة
                env_vars = config.get("Env", [])
                anubis_env = [env for env in env_vars if "ANUBIS" in env]
                if anubis_env:
                    analysis["environment"] = f"✅ {len(anubis_env)} متغير أنوبيس"
                else:
                    analysis["environment"] = "⚠️ لا توجد متغيرات أنوبيس"
                
                # فحص المنافذ
                exposed_ports = config.get("ExposedPorts", {})
                if exposed_ports:
                    analysis["ports_config"] = f"✅ {len(exposed_ports)} منفذ مكشوف"
                else:
                    analysis["ports_config"] = "⚠️ لا توجد منافذ مكشوفة"
                
            except:
                analysis["config_status"] = "❌ خطأ في قراءة التكوين"
        
        return analysis
    
    def analyze_n8n_container(self, name, container_id):
        """تحليل حاوية N8N"""
        analysis = {}
        
        # فحص إذا كانت N8N تعمل
        logs = self.run_docker_command(f"docker logs {name} --tail 5")
        if "n8n ready" in logs.lower() or "server started" in logs.lower():
            analysis["n8n_status"] = "✅ N8N جاهز"
        elif "error" in logs.lower():
            analysis["n8n_status"] = "❌ خطأ في N8N"
        else:
            analysis["n8n_status"] = "⚠️ حالة غير واضحة"
        
        return analysis
    
    def analyze_monitoring_container(self, name, container_id):
        """تحليل حاوية المراقبة"""
        analysis = {}
        analysis["monitoring_type"] = "Grafana/Monitoring"
        return analysis
    
    def generate_report(self):
        """إنشاء التقرير النهائي"""
        print("\n" + "="*80)
        print("🐳 تقرير حالة حاويات Docker - Universal AI Assistants")
        print("="*80)
        
        # إحصائيات عامة
        total_containers = len(self.containers_info)
        anubis_containers = [c for c in self.containers_info if c["type"] == "anubis"]
        running_containers = [c for c in self.containers_info if "up" in c["status"].lower()]
        stopped_containers = [c for c in self.containers_info if "exited" in c["status"].lower()]
        
        print(f"📊 إجمالي الحاويات: {total_containers}")
        print(f"🏺 حاويات أنوبيس: {len(anubis_containers)}")
        print(f"✅ حاويات نشطة: {len(running_containers)}")
        print(f"⚠️ حاويات متوقفة: {len(stopped_containers)}")
        
        # تفاصيل حاويات أنوبيس
        if anubis_containers:
            print(f"\n🏺 تفاصيل حاويات أنوبيس:")
            print("-" * 60)
            for container in anubis_containers:
                status_icon = "✅" if "up" in container["status"].lower() else "⚠️"
                print(f"{status_icon} {container['name']}: {container['status']}")
        
        # توصيات
        print(f"\n🎯 التوصيات:")
        print("-" * 60)
        
        if stopped_containers:
            print(f"🔄 إعادة تشغيل {len(stopped_containers)} حاوية متوقفة")
            for container in stopped_containers:
                if container["type"] == "anubis":
                    print(f"   docker start {container['name']}")
        
        if len(running_containers) == total_containers:
            print("✅ جميع الحاويات تعمل بشكل طبيعي")
        
        # حفظ التقرير
        report_data = {
            "timestamp": self.timestamp,
            "total_containers": total_containers,
            "anubis_containers": len(anubis_containers),
            "running_containers": len(running_containers),
            "stopped_containers": len(stopped_containers),
            "containers_details": self.containers_info
        }
        
        report_filename = f"docker_containers_report_{self.timestamp}.json"
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 تم حفظ التقرير المفصل: {report_filename}")
        print("="*80)

def main():
    """الدالة الرئيسية"""
    print("🐳 مرحباً بك في محلل حاويات Docker")
    print("🔍 سيتم فحص جميع حاويات Universal AI Assistants...")
    
    analyzer = DockerContainersAnalyzer()
    
    try:
        analyzer.analyze_containers()
        print("\n✅ تم إكمال التحليل بنجاح!")
        
    except Exception as e:
        print(f"\n❌ خطأ في التحليل: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
