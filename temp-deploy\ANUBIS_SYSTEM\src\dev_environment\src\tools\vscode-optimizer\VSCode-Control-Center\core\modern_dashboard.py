#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎨 Modern VS Code Process Control Dashboard
==========================================
واجهة تحكم حديثة ومتطورة بتصميم جميل
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import subprocess
import psutil
import json
import os
import threading
import time
from datetime import datetime
import math

class ModernDashboard:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎛️ VS Code Control Center")
        self.root.geometry("1400x900")
        self.root.configure(bg='#0d1117')  # GitHub Dark theme
        self.root.resizable(True, True)
        
        # متغيرات التحكم
        self.monitoring = False
        self.process_data = []
        self.extensions_data = []
        
        # ألوان التصميم الحديث
        self.colors = {
            'bg_primary': '#0d1117',      # خلفية رئيسية
            'bg_secondary': '#161b22',    # خلفية ثانوية
            'bg_tertiary': '#21262d',     # خلفية ثالثية
            'accent': '#58a6ff',          # لون مميز
            'success': '#238636',         # أخضر
            'warning': '#d29922',         # أصفر
            'danger': '#da3633',          # أحمر
            'info': '#1f6feb',           # أزرق
            'text_primary': '#f0f6fc',    # نص رئيسي
            'text_secondary': '#8b949e',  # نص ثانوي
            'border': '#30363d'           # حدود
        }
        
        self.setup_styles()
        self.setup_modern_ui()
        self.refresh_data()
        
    def setup_styles(self):
        """إعداد الأنماط الحديثة"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # تخصيص الأنماط
        style.configure('Modern.TFrame', background=self.colors['bg_secondary'])
        style.configure('Card.TFrame', background=self.colors['bg_tertiary'], relief='flat', borderwidth=1)
        style.configure('Modern.TLabel', background=self.colors['bg_secondary'], foreground=self.colors['text_primary'])
        style.configure('Title.TLabel', background=self.colors['bg_secondary'], foreground=self.colors['text_primary'], font=('Segoe UI', 16, 'bold'))
        style.configure('Stat.TLabel', background=self.colors['bg_tertiary'], foreground=self.colors['text_primary'], font=('Segoe UI', 12, 'bold'))
        
        # تخصيص Treeview
        style.configure('Modern.Treeview', 
                       background=self.colors['bg_tertiary'],
                       foreground=self.colors['text_primary'],
                       fieldbackground=self.colors['bg_tertiary'],
                       borderwidth=0,
                       font=('Segoe UI', 10))
        style.configure('Modern.Treeview.Heading',
                       background=self.colors['bg_secondary'],
                       foreground=self.colors['text_primary'],
                       font=('Segoe UI', 10, 'bold'))
        
    def create_gradient_frame(self, parent, color1, color2, width, height):
        """إنشاء إطار بتدرج لوني"""
        canvas = tk.Canvas(parent, width=width, height=height, highlightthickness=0)
        canvas.configure(bg=color1)
        
        # رسم التدرج
        for i in range(height):
            ratio = i / height
            r1, g1, b1 = self.hex_to_rgb(color1)
            r2, g2, b2 = self.hex_to_rgb(color2)
            
            r = int(r1 + (r2 - r1) * ratio)
            g = int(g1 + (g2 - g1) * ratio)
            b = int(b1 + (b2 - b1) * ratio)
            
            color = f"#{r:02x}{g:02x}{b:02x}"
            canvas.create_line(0, i, width, i, fill=color)
        
        return canvas
    
    def hex_to_rgb(self, hex_color):
        """تحويل اللون من hex إلى RGB"""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
    
    def create_modern_button(self, parent, text, command, bg_color, hover_color=None):
        """إنشاء زر حديث مع تأثيرات"""
        if hover_color is None:
            hover_color = bg_color
            
        button = tk.Button(parent, text=text, command=command,
                          bg=bg_color, fg='white', font=('Segoe UI', 10, 'bold'),
                          relief='flat', borderwidth=0, padx=20, pady=8,
                          cursor='hand2')
        
        # تأثيرات التفاعل
        def on_enter(e):
            button.configure(bg=hover_color)
        
        def on_leave(e):
            button.configure(bg=bg_color)
        
        button.bind("<Enter>", on_enter)
        button.bind("<Leave>", on_leave)
        
        return button
    
    def create_stat_card(self, parent, title, value, color, icon="📊"):
        """إنشاء بطاقة إحصائية حديثة"""
        card_frame = tk.Frame(parent, bg=self.colors['bg_tertiary'], relief='flat', bd=1)
        card_frame.configure(highlightbackground=self.colors['border'], highlightthickness=1)
        
        # الأيقونة والعنوان
        header_frame = tk.Frame(card_frame, bg=self.colors['bg_tertiary'])
        header_frame.pack(fill='x', padx=15, pady=(15, 5))
        
        icon_label = tk.Label(header_frame, text=icon, font=('Segoe UI', 16),
                             bg=self.colors['bg_tertiary'], fg=color)
        icon_label.pack(side='left')
        
        title_label = tk.Label(header_frame, text=title, font=('Segoe UI', 11, 'bold'),
                              bg=self.colors['bg_tertiary'], fg=self.colors['text_secondary'])
        title_label.pack(side='left', padx=(10, 0))
        
        # القيمة
        value_label = tk.Label(card_frame, text=str(value), font=('Segoe UI', 24, 'bold'),
                              bg=self.colors['bg_tertiary'], fg=color)
        value_label.pack(padx=15, pady=(0, 15))
        
        return card_frame, value_label
    
    def setup_modern_ui(self):
        """إعداد الواجهة الحديثة"""
        # الشريط العلوي
        self.create_header()
        
        # بطاقات الإحصائيات
        self.create_stats_section()
        
        # المحتوى الرئيسي
        self.create_main_content()
        
        # الشريط السفلي
        self.create_footer()
    
    def create_header(self):
        """إنشاء الشريط العلوي"""
        header_frame = tk.Frame(self.root, bg=self.colors['bg_secondary'], height=80)
        header_frame.pack(fill='x', padx=0, pady=0)
        header_frame.pack_propagate(False)
        
        # العنوان الرئيسي
        title_frame = tk.Frame(header_frame, bg=self.colors['bg_secondary'])
        title_frame.pack(expand=True, fill='both')
        
        # الأيقونة والعنوان
        icon_label = tk.Label(title_frame, text="🎛️", font=('Segoe UI', 32),
                             bg=self.colors['bg_secondary'], fg=self.colors['accent'])
        icon_label.pack(side='left', padx=(30, 15), pady=15)
        
        title_text = tk.Frame(title_frame, bg=self.colors['bg_secondary'])
        title_text.pack(side='left', pady=15)
        
        main_title = tk.Label(title_text, text="VS Code Control Center",
                             font=('Segoe UI', 20, 'bold'),
                             bg=self.colors['bg_secondary'], fg=self.colors['text_primary'])
        main_title.pack(anchor='w')
        
        subtitle = tk.Label(title_text, text="مركز التحكم المتقدم في العمليات والإضافات",
                           font=('Segoe UI', 11),
                           bg=self.colors['bg_secondary'], fg=self.colors['text_secondary'])
        subtitle.pack(anchor='w')
        
        # أزرار التحكم السريع
        quick_controls = tk.Frame(header_frame, bg=self.colors['bg_secondary'])
        quick_controls.pack(side='right', padx=30, pady=15)
        
        self.create_modern_button(quick_controls, "🔄 تحديث", self.refresh_data, 
                                 self.colors['info'], '#2f81f7').pack(side='right', padx=5)
        
        self.create_modern_button(quick_controls, "⚡ تحسين", self.optimize_performance,
                                 self.colors['warning'], '#e2a822').pack(side='right', padx=5)
    
    def create_stats_section(self):
        """إنشاء قسم الإحصائيات"""
        stats_frame = tk.Frame(self.root, bg=self.colors['bg_primary'], height=120)
        stats_frame.pack(fill='x', padx=20, pady=(10, 0))
        stats_frame.pack_propagate(False)
        
        # بطاقات الإحصائيات
        self.stats_cards = {}
        stats_data = [
            ("العمليات النشطة", "0", self.colors['accent'], "⚙️"),
            ("استهلاك الذاكرة", "0 MB", self.colors['success'], "💾"),
            ("استهلاك المعالج", "0%", self.colors['warning'], "🔥"),
            ("الإضافات المفعلة", "0", self.colors['info'], "🧩")
        ]
        
        for i, (title, value, color, icon) in enumerate(stats_data):
            card, value_label = self.create_stat_card(stats_frame, title, value, color, icon)
            card.pack(side='left', fill='both', expand=True, padx=5)
            self.stats_cards[title] = value_label
    
    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        main_frame = tk.Frame(self.root, bg=self.colors['bg_primary'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # الجانب الأيسر - العمليات
        left_panel = self.create_processes_panel(main_frame)
        left_panel.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        # الجانب الأيمن - الإضافات
        right_panel = self.create_extensions_panel(main_frame)
        right_panel.pack(side='right', fill='both', expand=True, padx=(10, 0))
    
    def create_processes_panel(self, parent):
        """إنشاء لوحة العمليات"""
        panel = tk.Frame(parent, bg=self.colors['bg_secondary'], relief='flat', bd=1)
        panel.configure(highlightbackground=self.colors['border'], highlightthickness=1)
        
        # عنوان القسم
        header = tk.Frame(panel, bg=self.colors['bg_secondary'], height=50)
        header.pack(fill='x', padx=0, pady=0)
        header.pack_propagate(False)
        
        title_label = tk.Label(header, text="⚙️ إدارة العمليات", 
                              font=('Segoe UI', 14, 'bold'),
                              bg=self.colors['bg_secondary'], fg=self.colors['text_primary'])
        title_label.pack(side='left', padx=20, pady=15)
        
        # أزرار التحكم
        controls = tk.Frame(header, bg=self.colors['bg_secondary'])
        controls.pack(side='right', padx=20, pady=10)
        
        self.create_modern_button(controls, "🚫 إغلاق", self.kill_selected_process,
                                 self.colors['danger'], '#f85149').pack(side='right', padx=2)
        
        self.create_modern_button(controls, "🧹 تنظيف", self.cleanup_all,
                                 self.colors['warning'], '#e2a822').pack(side='right', padx=2)
        
        # جدول العمليات
        tree_frame = tk.Frame(panel, bg=self.colors['bg_secondary'])
        tree_frame.pack(fill='both', expand=True, padx=15, pady=(0, 15))
        
        # إنشاء Treeview مع تمرير
        tree_scroll = ttk.Scrollbar(tree_frame)
        tree_scroll.pack(side='right', fill='y')
        
        self.process_tree = ttk.Treeview(tree_frame, style='Modern.Treeview',
                                        columns=('PID', 'Name', 'CPU', 'Memory', 'Status'),
                                        show='tree headings',
                                        yscrollcommand=tree_scroll.set)
        
        tree_scroll.config(command=self.process_tree.yview)
        
        # تكوين الأعمدة
        self.process_tree.heading('#0', text='العملية')
        self.process_tree.heading('PID', text='PID')
        self.process_tree.heading('Name', text='الاسم')
        self.process_tree.heading('CPU', text='المعالج %')
        self.process_tree.heading('Memory', text='الذاكرة MB')
        self.process_tree.heading('Status', text='الحالة')
        
        self.process_tree.column('#0', width=120)
        self.process_tree.column('PID', width=80)
        self.process_tree.column('Name', width=150)
        self.process_tree.column('CPU', width=80)
        self.process_tree.column('Memory', width=100)
        self.process_tree.column('Status', width=80)
        
        self.process_tree.pack(fill='both', expand=True)
        
        return panel
    
    def create_extensions_panel(self, parent):
        """إنشاء لوحة الإضافات"""
        panel = tk.Frame(parent, bg=self.colors['bg_secondary'], relief='flat', bd=1)
        panel.configure(highlightbackground=self.colors['border'], highlightthickness=1)
        
        # عنوان القسم
        header = tk.Frame(panel, bg=self.colors['bg_secondary'], height=50)
        header.pack(fill='x', padx=0, pady=0)
        header.pack_propagate(False)
        
        title_label = tk.Label(header, text="🧩 إدارة الإضافات",
                              font=('Segoe UI', 14, 'bold'),
                              bg=self.colors['bg_secondary'], fg=self.colors['text_primary'])
        title_label.pack(side='left', padx=20, pady=15)
        
        # أزرار التحكم
        controls = tk.Frame(header, bg=self.colors['bg_secondary'])
        controls.pack(side='right', padx=20, pady=10)
        
        self.create_modern_button(controls, "✅ تفعيل", self.enable_selected_extension,
                                 self.colors['success'], '#2ea043').pack(side='right', padx=2)
        
        self.create_modern_button(controls, "❌ تعطيل", self.disable_selected_extension,
                                 self.colors['warning'], '#e2a822').pack(side='right', padx=2)
        
        # جدول الإضافات
        tree_frame = tk.Frame(panel, bg=self.colors['bg_secondary'])
        tree_frame.pack(fill='both', expand=True, padx=15, pady=(0, 15))
        
        # إنشاء Treeview مع تمرير
        ext_scroll = ttk.Scrollbar(tree_frame)
        ext_scroll.pack(side='right', fill='y')
        
        self.extensions_tree = ttk.Treeview(tree_frame, style='Modern.Treeview',
                                           columns=('ID', 'Version', 'Status', 'Publisher'),
                                           show='tree headings',
                                           yscrollcommand=ext_scroll.set)
        
        ext_scroll.config(command=self.extensions_tree.yview)
        
        # تكوين الأعمدة
        self.extensions_tree.heading('#0', text='الإضافة')
        self.extensions_tree.heading('ID', text='المعرف')
        self.extensions_tree.heading('Version', text='الإصدار')
        self.extensions_tree.heading('Status', text='الحالة')
        self.extensions_tree.heading('Publisher', text='الناشر')
        
        self.extensions_tree.column('#0', width=150)
        self.extensions_tree.column('ID', width=120)
        self.extensions_tree.column('Version', width=80)
        self.extensions_tree.column('Status', width=80)
        self.extensions_tree.column('Publisher', width=100)
        
        self.extensions_tree.pack(fill='both', expand=True)
        
        return panel
    
    def create_footer(self):
        """إنشاء الشريط السفلي"""
        footer_frame = tk.Frame(self.root, bg=self.colors['bg_secondary'], height=60)
        footer_frame.pack(fill='x', side='bottom')
        footer_frame.pack_propagate(False)
        
        # معلومات الحالة
        status_frame = tk.Frame(footer_frame, bg=self.colors['bg_secondary'])
        status_frame.pack(side='left', padx=20, pady=15)
        
        self.status_label = tk.Label(status_frame, text="🟢 جاهز للاستخدام",
                                    font=('Segoe UI', 10),
                                    bg=self.colors['bg_secondary'], fg=self.colors['success'])
        self.status_label.pack()
        
        # أدوات إضافية
        tools_frame = tk.Frame(footer_frame, bg=self.colors['bg_secondary'])
        tools_frame.pack(side='right', padx=20, pady=10)
        
        # مراقبة تلقائية
        self.monitor_var = tk.BooleanVar()
        monitor_check = tk.Checkbutton(tools_frame, text="مراقبة تلقائية",
                                      variable=self.monitor_var,
                                      command=self.toggle_monitoring,
                                      bg=self.colors['bg_secondary'], 
                                      fg=self.colors['text_primary'],
                                      selectcolor=self.colors['bg_tertiary'],
                                      font=('Segoe UI', 10))
        monitor_check.pack(side='left', padx=10)
        
        self.create_modern_button(tools_frame, "💾 حفظ تقرير", self.save_report,
                                 self.colors['info'], '#2f81f7').pack(side='right', padx=5)
        
        self.create_modern_button(tools_frame, "🚀 إعادة تشغيل", self.restart_vscode,
                                 self.colors['accent'], '#6cb6ff').pack(side='right', padx=5)

    def update_status(self, message, status_type="info"):
        """تحديث رسالة الحالة"""
        colors = {
            "success": self.colors['success'],
            "warning": self.colors['warning'],
            "error": self.colors['danger'],
            "info": self.colors['info']
        }

        icons = {
            "success": "🟢",
            "warning": "🟡",
            "error": "🔴",
            "info": "🔵"
        }

        color = colors.get(status_type, self.colors['info'])
        icon = icons.get(status_type, "🔵")

        self.status_label.configure(text=f"{icon} {message}", fg=color)
        self.root.update_idletasks()

    def refresh_data(self):
        """تحديث جميع البيانات"""
        self.update_status("جاري تحديث البيانات...", "info")
        self.refresh_processes()
        self.refresh_extensions()
        self.update_status("تم التحديث بنجاح", "success")

    def refresh_processes(self):
        """تحديث قائمة العمليات"""
        # مسح البيانات السابقة
        for item in self.process_tree.get_children():
            self.process_tree.delete(item)

        self.process_data = []
        vscode_processes = 0
        total_memory = 0
        total_cpu = 0

        try:
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_info', 'status']):
                try:
                    pinfo = proc.info
                    if any(keyword in pinfo['name'].lower() for keyword in
                          ['code', 'node', 'electron', 'typescript', 'eslint']):

                        memory_mb = pinfo['memory_info'].rss / 1024 / 1024
                        cpu_percent = pinfo['cpu_percent'] or 0

                        self.process_data.append(pinfo)
                        vscode_processes += 1
                        total_memory += memory_mb
                        total_cpu += cpu_percent

                        # تحديد لون الصف حسب الاستهلاك
                        if cpu_percent > 50:
                            tags = ('high_cpu',)
                        elif memory_mb > 500:
                            tags = ('high_memory',)
                        else:
                            tags = ('normal',)

                        # إضافة إلى الجدول
                        item = self.process_tree.insert('', 'end',
                                                       text=pinfo['name'],
                                                       values=(pinfo['pid'],
                                                              pinfo['name'],
                                                              f"{cpu_percent:.1f}%",
                                                              f"{memory_mb:.1f}",
                                                              pinfo['status']),
                                                       tags=tags)

                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

        except Exception as e:
            self.update_status(f"خطأ في تحديث العمليات: {e}", "error")

        # تحديث الإحصائيات
        self.stats_cards['العمليات النشطة'].config(text=str(vscode_processes))
        self.stats_cards['استهلاك الذاكرة'].config(text=f"{total_memory:.0f} MB")
        self.stats_cards['استهلاك المعالج'].config(text=f"{total_cpu:.1f}%")

        # تكوين ألوان الصفوف
        self.process_tree.tag_configure('high_cpu', background='#4a1a1a')
        self.process_tree.tag_configure('high_memory', background='#4a3a1a')
        self.process_tree.tag_configure('normal', background=self.colors['bg_tertiary'])

    def refresh_extensions(self):
        """تحديث قائمة الإضافات"""
        # مسح البيانات السابقة
        for item in self.extensions_tree.get_children():
            self.extensions_tree.delete(item)

        try:
            # الحصول على قائمة الإضافات
            result = subprocess.run(['code', '--list-extensions', '--show-versions'],
                                  capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                extensions = result.stdout.strip().split('\n')
                enabled_count = 0

                for ext in extensions:
                    if '@' in ext:
                        name, version = ext.rsplit('@', 1)
                        publisher = name.split('.')[0] if '.' in name else 'Unknown'

                        # تحديد حالة الإضافة
                        status = "🟢 مفعل"
                        tags = ('enabled',)

                        self.extensions_tree.insert('', 'end',
                                                   text=name.split('.')[-1] if '.' in name else name,
                                                   values=(name, version, status, publisher),
                                                   tags=tags)
                        enabled_count += 1

                self.stats_cards['الإضافات المفعلة'].config(text=str(enabled_count))
            else:
                self.update_status("فشل في الحصول على قائمة الإضافات", "warning")

        except Exception as e:
            self.update_status(f"خطأ في تحديث الإضافات: {e}", "error")

        # تكوين ألوان الصفوف
        self.extensions_tree.tag_configure('enabled', background=self.colors['bg_tertiary'])
        self.extensions_tree.tag_configure('disabled', background='#3a2a1a')

    def kill_selected_process(self):
        """إغلاق العملية المحددة"""
        selection = self.process_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد عملية لإغلاقها")
            return

        item = self.process_tree.item(selection[0])
        pid = item['values'][0]
        name = item['values'][1]

        if messagebox.askyesno("تأكيد", f"هل تريد إغلاق العملية {name} (PID: {pid})؟"):
            try:
                proc = psutil.Process(pid)
                proc.terminate()
                self.update_status(f"تم إغلاق العملية {name}", "success")
                self.refresh_processes()
            except Exception as e:
                self.update_status(f"فشل في إغلاق العملية: {e}", "error")

    def cleanup_all(self):
        """تنظيف شامل لجميع العمليات"""
        if messagebox.askyesno("تأكيد", "هل تريد إغلاق جميع عمليات VS Code؟\n⚠️ تأكد من حفظ عملك!"):
            self.update_status("جاري التنظيف الشامل...", "warning")

            killed_count = 0
            for pinfo in self.process_data:
                try:
                    proc = psutil.Process(pinfo['pid'])
                    proc.terminate()
                    killed_count += 1
                except:
                    pass

            time.sleep(2)
            self.refresh_processes()
            self.update_status(f"تم إغلاق {killed_count} عملية", "success")

    def disable_selected_extension(self):
        """تعطيل الإضافة المحددة"""
        selection = self.extensions_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد إضافة لتعطيلها")
            return

        item = self.extensions_tree.item(selection[0])
        ext_id = item['values'][0]

        try:
            subprocess.run(['code', '--disable-extension', ext_id], check=True)
            self.update_status(f"تم تعطيل الإضافة {ext_id}", "success")
            self.refresh_extensions()
        except Exception as e:
            self.update_status(f"فشل في تعطيل الإضافة: {e}", "error")

    def enable_selected_extension(self):
        """تفعيل الإضافة المحددة"""
        selection = self.extensions_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد إضافة لتفعيلها")
            return

        item = self.extensions_tree.item(selection[0])
        ext_id = item['values'][0]

        try:
            subprocess.run(['code', '--enable-extension', ext_id], check=True)
            self.update_status(f"تم تفعيل الإضافة {ext_id}", "success")
            self.refresh_extensions()
        except Exception as e:
            self.update_status(f"فشل في تفعيل الإضافة: {e}", "error")

    def restart_vscode(self):
        """إعادة تشغيل VS Code"""
        if messagebox.askyesno("تأكيد", "هل تريد إعادة تشغيل VS Code؟"):
            self.update_status("جاري إعادة تشغيل VS Code...", "info")

            try:
                subprocess.run(['taskkill', '/IM', 'Code.exe', '/F'], check=False)
                time.sleep(3)
                subprocess.Popen(['code'])
                self.update_status("تم إعادة تشغيل VS Code", "success")
            except Exception as e:
                self.update_status(f"فشل في إعادة التشغيل: {e}", "error")

    def optimize_performance(self):
        """تحسين الأداء"""
        self.update_status("جاري تحسين الأداء...", "info")

        # إغلاق العمليات الخاملة
        killed_count = 0
        for pinfo in self.process_data:
            try:
                proc = psutil.Process(pinfo['pid'])
                if proc.cpu_percent() < 1.0:
                    proc.terminate()
                    killed_count += 1
            except:
                pass

        self.update_status(f"تم تحسين الأداء - أغلقت {killed_count} عملية خاملة", "success")
        self.refresh_processes()

    def save_report(self):
        """حفظ تقرير الحالة"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"modern_dashboard_report_{timestamp}.json"

            report_data = {
                "timestamp": datetime.now().isoformat(),
                "processes": len(self.process_data),
                "total_memory": sum(p.get('memory_info', {}).get('rss', 0) for p in self.process_data) / 1024 / 1024,
                "extensions_count": len(self.extensions_data),
                "process_details": [
                    {
                        "pid": p['pid'],
                        "name": p['name'],
                        "memory_mb": p.get('memory_info', {}).get('rss', 0) / 1024 / 1024,
                        "status": p['status']
                    } for p in self.process_data
                ]
            }

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)

            self.update_status(f"تم حفظ التقرير: {filename}", "success")

        except Exception as e:
            self.update_status(f"فشل في حفظ التقرير: {e}", "error")

    def toggle_monitoring(self):
        """تفعيل/إلغاء المراقبة التلقائية"""
        if self.monitor_var.get():
            self.monitoring = True
            self.update_status("تم تفعيل المراقبة التلقائية", "info")
            self.start_monitoring()
        else:
            self.monitoring = False
            self.update_status("تم إيقاف المراقبة التلقائية", "warning")

    def start_monitoring(self):
        """بدء المراقبة التلقائية"""
        def monitor():
            while self.monitoring:
                self.refresh_data()
                time.sleep(5)

        thread = threading.Thread(target=monitor, daemon=True)
        thread.start()

    def run(self):
        """تشغيل التطبيق"""
        self.update_status("تم تشغيل مركز التحكم الحديث", "success")
        self.root.mainloop()


if __name__ == "__main__":
    app = ModernDashboard()
    app.run()
