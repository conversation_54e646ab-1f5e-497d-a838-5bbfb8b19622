#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 تشغيل سريع لنظام أنوبيس المتكامل
Quick Start for Anubis Integrated System
"""

import os
import sys
import time
import subprocess
import webbrowser

# ألوان للإخراج
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    PURPLE = '\033[0;35m'
    CYAN = '\033[0;36m'
    NC = '\033[0m'

def print_header():
    print(f"{Colors.PURPLE}{'='*60}{Colors.NC}")
    print(f"{Colors.PURPLE}🚀 تشغيل سريع لنظام أنوبيس المتكامل{Colors.NC}")
    print(f"{Colors.PURPLE}Quick Start for Anubis Integrated System{Colors.NC}")
    print(f"{Colors.PURPLE}{'='*60}{Colors.NC}")

def print_step(message):
    print(f"{Colors.BLUE}📋 {message}{Colors.NC}")

def print_success(message):
    print(f"{Colors.GREEN}✅ {message}{Colors.NC}")

def print_warning(message):
    print(f"{Colors.YELLOW}⚠️ {message}{Colors.NC}")

def print_error(message):
    print(f"{Colors.RED}❌ {message}{Colors.NC}")

def run_command(cmd, description=""):
    """تشغيل أمر مع معالجة الأخطاء"""
    try:
        print_step(f"تشغيل: {description}")
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print_success(f"نجح: {description}")
            return True
        else:
            print_error(f"فشل: {description}")
            return False
    except Exception as e:
        print_error(f"خطأ: {description} - {e}")
        return False

def quick_start():
    """التشغيل السريع"""
    print_header()
    
    print(f"{Colors.CYAN}🎯 اختر طريقة التشغيل السريع:{Colors.NC}")
    print(f"{Colors.GREEN}1. 🚀 تشغيل كامل مع Docker (موصى به){Colors.NC}")
    print(f"{Colors.GREEN}2. 🏺 تشغيل أساسي مع قاعدة البيانات المحلية{Colors.NC}")
    print(f"{Colors.GREEN}3. 🌐 تشغيل الواجهة الموحدة فقط{Colors.NC}")
    
    choice = input(f"\n{Colors.CYAN}اختر رقم (1-3): {Colors.NC}").strip()
    
    if choice == "1":
        # تشغيل كامل مع Docker
        print_step("تشغيل النظام الكامل مع Docker...")
        
        if run_command("python start_complete_anubis_system.py", "النظام الكامل"):
            time.sleep(10)
            
            # فتح الواجهات
            interfaces = [
                "http://localhost:5000",  # الواجهة الموحدة
                "http://localhost:8000",  # أنوبيس
                "http://localhost:7000",  # حورس
            ]
            
            print_step("فتح الواجهات في المتصفح...")
            for url in interfaces:
                try:
                    webbrowser.open(url)
                    time.sleep(1)
                except:
                    print_warning(f"لا يمكن فتح {url} تلقائياً")
            
            show_success_info()
    
    elif choice == "2":
        # تشغيل أساسي
        print_step("تشغيل النظام الأساسي مع قاعدة البيانات المحلية...")
        
        if run_command("python start_anubis_with_local_mysql.py", "النظام الأساسي"):
            time.sleep(5)
            webbrowser.open("http://localhost:8000")
            print_success("تم تشغيل النظام الأساسي: http://localhost:8000")
    
    elif choice == "3":
        # واجهة موحدة فقط
        print_step("تشغيل الواجهة الموحدة...")
        
        if run_command("docker run -d --name anubis-web-client-server -p 5000:5000 anubis-web-client", "الواجهة الموحدة"):
            time.sleep(3)
            webbrowser.open("http://localhost:5000")
            print_success("تم تشغيل الواجهة الموحدة: http://localhost:5000")
    
    else:
        print_warning("اختيار غير صحيح!")

def show_success_info():
    """عرض معلومات النجاح"""
    print(f"\n{Colors.GREEN}🎉 تم تشغيل النظام بنجاح!{Colors.NC}")
    
    print(f"\n{Colors.CYAN}🌐 الواجهات المتاحة:{Colors.NC}")
    print(f"{Colors.GREEN}🌐 الواجهة الموحدة: http://localhost:5000{Colors.NC}")
    print(f"{Colors.GREEN}🏺 نظام أنوبيس: http://localhost:8000{Colors.NC}")
    print(f"{Colors.GREEN}𓅃 فريق حورس: http://localhost:7000{Colors.NC}")
    print(f"{Colors.GREEN}🔗 نظام MCP: http://localhost:3000{Colors.NC}")
    
    print(f"\n{Colors.CYAN}🔧 أوامر مفيدة:{Colors.NC}")
    print(f"{Colors.YELLOW}عرض الحاويات: docker ps{Colors.NC}")
    print(f"{Colors.YELLOW}عرض السجلات: docker logs [container-name]{Colors.NC}")
    print(f"{Colors.YELLOW}إيقاف النظام: docker stop anubis-core-server anubis-mcp-server horus-team-server{Colors.NC}")
    
    print(f"\n{Colors.CYAN}📚 للمساعدة:{Colors.NC}")
    print(f"{Colors.YELLOW}التوثيق الكامل: PROJECT_DOCUMENTATION/README_COMPREHENSIVE.md{Colors.NC}")
    print(f"{Colors.YELLOW}دليل المستخدم: PROJECT_DOCUMENTATION/USER_GUIDE_COMPLETE.md{Colors.NC}")

def main():
    """الدالة الرئيسية"""
    try:
        quick_start()
    except KeyboardInterrupt:
        print_warning("\nتم إيقاف التشغيل بواسطة المستخدم")
    except Exception as e:
        print_error(f"خطأ غير متوقع: {e}")

if __name__ == "__main__":
    main()
