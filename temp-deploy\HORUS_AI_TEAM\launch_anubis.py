#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشغل أنوبيس السريع
𓂀 تشغيل وكيل البحث المتخصص
"""

import os
import sys
from pathlib import Path

# إضافة مسار أنوبيس
current_dir = Path(__file__).parent
anubis_path = current_dir / "04_specialized_agents" / "web_research_agent"
sys.path.append(str(anubis_path))

def main():
    """تشغيل أنوبيس"""
    print("𓂀 تشغيل أنوبيس - وكيل البحث المتخصص")
    print("=" * 50)
    
    try:
        # تغيير المجلد إلى مجلد أنوبيس
        os.chdir(anubis_path)
        
        # تشغيل المشغل المتكامل
        import subprocess
        result = subprocess.run([sys.executable, "horus_anubis_launcher.py"],
                              cwd=anubis_path, capture_output=False)
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد أنوبيس: {e}")
        print("💡 تأكد من تثبيت المتطلبات:")
        print("   pip install beautifulsoup4 requests serpapi lxml html5lib")
    except Exception as e:
        print(f"❌ خطأ في تشغيل أنوبيس: {e}")

if __name__ == "__main__":
    main()
