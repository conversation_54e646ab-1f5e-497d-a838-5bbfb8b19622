#!/bin/bash
# سكريبت إيقاف بيئة العمل المعزولة

echo "🛑 إيقاف بيئة العمل المعزولة..."

cd workspace

# حفظ البيانات المهمة قبل الإيقاف
echo "💾 حفظ حالة العمل..."
docker-compose exec anubis-workspace jupyter lab list > current_sessions.txt 2>/dev/null || true

# إيقاف جميع الخدمات
echo "📱 إيقاف جميع خدمات بيئة العمل..."
docker-compose down

# إزالة الشبكات (اختياري)
echo "🌐 تنظيف الشبكات (اختياري)..."
read -p "هل تريد إزالة الشبكات المعزولة؟ (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    docker network rm anubis-workspace-net anubis-workspace-data-net 2>/dev/null || true
    echo "✅ تم تنظيف الشبكات"
fi

echo "✅ تم إيقاف بيئة العمل بنجاح"
