#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🛠️ منفذ خطة تنظيم مشروع فريق حورس
HORUS AI Team Project Organization Implementation

نظام عملي لتطبيق خطة التنظيم وإعادة هيكلة المشروع
Practical system for implementing organization plan and restructuring the project
"""

import os
import json
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class HorusProjectOrganizerImplementation:
    """🛠️ منفذ خطة تنظيم مشروع فريق حورس"""
    
    def __init__(self):
        """تهيئة منفذ التنظيم"""
        self.project_dir = Path(__file__).parent
        self.backup_dir = self.project_dir.parent / f"HORUS_AI_TEAM_BACKUP_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.organization_log = []
        
        # الهيكل الجديد المقترح
        self.new_structure = {
            "01_core": {
                "description": "الأنظمة الأساسية والمحركات الرئيسية",
                "subdirs": ["engines", "managers", "interfaces"],
                "files": []
            },
            "02_team_members": {
                "description": "تكوينات وإعدادات أعضاء الفريق",
                "subdirs": ["local_models", "external_models", "configurations"],
                "files": []
            },
            "03_memory_system": {
                "description": "نظام الذاكرة والتعلم الجماعي",
                "subdirs": ["brain", "memory", "learning", "patterns"],
                "files": []
            },
            "04_collaboration": {
                "description": "أدوات التعاون والتنسيق",
                "subdirs": ["helpers", "systems", "workflows"],
                "files": []
            },
            "05_analysis": {
                "description": "التحليلات والتقارير",
                "subdirs": ["reports", "consultations", "enhancements"],
                "files": []
            },
            "06_documentation": {
                "description": "التوثيق والأدلة",
                "subdirs": ["guides", "reports", "plans"],
                "files": []
            },
            "07_configuration": {
                "description": "ملفات التكوين والإعدادات",
                "subdirs": ["requirements", "configs", "settings"],
                "files": []
            },
            "08_utilities": {
                "description": "الأدوات المساعدة والمرافق",
                "subdirs": ["helpers", "tools", "scripts"],
                "files": []
            },
            "09_archive": {
                "description": "الملفات المؤرشفة والقديمة",
                "subdirs": ["old_versions", "deprecated", "backup"],
                "files": []
            }
        }
        
        # خريطة نقل الملفات
        self.file_migration_map = {
            # الأنظمة الأساسية
            "team_workflow_manager.py": "01_core/managers/",
            "horus_interface.py": "01_core/interfaces/",
            "horus_launcher.py": "01_core/engines/",
            
            # نظام الذاكرة
            "anubis_team_memory": "03_memory_system/",
            
            # أدوات التعاون
            "anubis_ai_collaboration_helper.py": "04_collaboration/helpers/",
            "anubis_ai_team_collaboration_system.py": "04_collaboration/systems/",
            "anubis_gemini_cli_helper.py": "04_collaboration/helpers/",
            
            # التحليلات والتقارير
            "analysis": "05_analysis/reports/",
            "consultation": "05_analysis/consultations/",
            "enhancements": "05_analysis/enhancements/",
            "advanced_models_consultant.py": "05_analysis/tools/",
            "horus_team_analyzer.py": "05_analysis/tools/",
            "horus_team_enhancer.py": "05_analysis/tools/",
            
            # التوثيق
            "README.md": "06_documentation/guides/",
            "HORUS_README.md": "06_documentation/guides/",
            "HORUS_AI_TEAM_STRUCTURE.md": "06_documentation/guides/",
            "HORUS_TEAM_MIGRATION_PLAN.md": "06_documentation/plans/",
            "ADVANCED_MODELS_CONSULTATION_SUCCESS_REPORT.md": "06_documentation/reports/",
            "horus_mission_success_report.md": "06_documentation/reports/",
            
            # التكوين
            "requirements_core.txt": "07_configuration/requirements/",
            "requirements_dev.txt": "07_configuration/requirements/",
            "requirements_master.txt": "07_configuration/requirements/",
            "requirements_system.txt": "07_configuration/requirements/",
            "requirements_web.txt": "07_configuration/requirements/",
            "anubis_ai_team_collaboration_plan.json": "07_configuration/configs/",
            
            # الأدوات المساعدة
            "horus_api_keys_assistant.py": "08_utilities/tools/",
            "horus_project_organization_task.py": "08_utilities/tools/",
            "anubis_project_paths": "08_utilities/helpers/",
            
            # الأرشيف
            "anubis_gemini_docker_help_request.md": "09_archive/deprecated/",
            "anubis_project_organization_collaboration_request.md": "09_archive/deprecated/",
            "anubis_project_organization_gemini_request.md": "09_archive/deprecated/",
            "api_keys_management_request.md": "09_archive/deprecated/"
        }
        
        logger.info("🛠️ تم تهيئة منفذ تنظيم مشروع فريق حورس")
    
    def create_backup(self) -> bool:
        """إنشاء نسخة احتياطية من المشروع"""
        try:
            logger.info(f"💾 إنشاء نسخة احتياطية في: {self.backup_dir}")
            
            # نسخ المشروع كاملاً
            shutil.copytree(self.project_dir, self.backup_dir, 
                          ignore=shutil.ignore_patterns('__pycache__', '*.pyc'))
            
            self.organization_log.append(f"✅ تم إنشاء نسخة احتياطية: {self.backup_dir}")
            logger.info("✅ تم إنشاء النسخة الاحتياطية بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
            self.organization_log.append(f"❌ فشل في إنشاء النسخة الاحتياطية: {e}")
            return False
    
    def create_new_structure(self) -> bool:
        """إنشاء الهيكل الجديد للمجلدات"""
        try:
            logger.info("📁 إنشاء الهيكل الجديد للمجلدات...")
            
            for main_dir, config in self.new_structure.items():
                # إنشاء المجلد الرئيسي
                main_path = self.project_dir / main_dir
                main_path.mkdir(exist_ok=True)
                
                # إنشاء README للمجلد الرئيسي
                readme_content = f"# {main_dir}\n\n{config['description']}\n\n"
                readme_content += "## المجلدات الفرعية:\n"
                for subdir in config['subdirs']:
                    readme_content += f"- `{subdir}/` - \n"
                
                readme_path = main_path / "README.md"
                with open(readme_path, 'w', encoding='utf-8') as f:
                    f.write(readme_content)
                
                # إنشاء المجلدات الفرعية
                for subdir in config['subdirs']:
                    subdir_path = main_path / subdir
                    subdir_path.mkdir(exist_ok=True)
                    
                    # إنشاء ملف __init__.py للمجلدات التي تحتوي على Python
                    if main_dir in ["01_core", "03_memory_system", "04_collaboration", "08_utilities"]:
                        init_file = subdir_path / "__init__.py"
                        with open(init_file, 'w', encoding='utf-8') as f:
                            f.write(f'"""مجلد {subdir} في {main_dir}"""\n')
                
                self.organization_log.append(f"✅ تم إنشاء مجلد: {main_dir}")
            
            logger.info("✅ تم إنشاء الهيكل الجديد بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء الهيكل الجديد: {e}")
            self.organization_log.append(f"❌ فشل في إنشاء الهيكل الجديد: {e}")
            return False
    
    def migrate_files(self) -> bool:
        """نقل الملفات حسب خريطة النقل"""
        try:
            logger.info("📦 بدء نقل الملفات...")
            
            for source, destination in self.file_migration_map.items():
                source_path = self.project_dir / source
                
                if source_path.exists():
                    # تحديد المسار الوجهة الكامل
                    dest_dir = self.project_dir / destination
                    dest_dir.mkdir(parents=True, exist_ok=True)
                    
                    if source_path.is_file():
                        dest_path = dest_dir / source_path.name
                        shutil.move(str(source_path), str(dest_path))
                        self.organization_log.append(f"📁 نقل ملف: {source} → {destination}")
                    
                    elif source_path.is_dir():
                        dest_path = dest_dir / source_path.name
                        if dest_path.exists():
                            shutil.rmtree(dest_path)
                        shutil.move(str(source_path), str(dest_path))
                        self.organization_log.append(f"📂 نقل مجلد: {source} → {destination}")
                    
                    logger.info(f"✅ تم نقل: {source}")
                else:
                    logger.warning(f"⚠️ لم يتم العثور على: {source}")
            
            logger.info("✅ تم نقل جميع الملفات بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في نقل الملفات: {e}")
            self.organization_log.append(f"❌ فشل في نقل الملفات: {e}")
            return False
    
    def cleanup_old_files(self) -> bool:
        """تنظيف الملفات القديمة وغير الضرورية"""
        try:
            logger.info("🧹 تنظيف الملفات القديمة...")
            
            # حذف ملفات __pycache__
            for pycache_dir in self.project_dir.rglob("__pycache__"):
                if pycache_dir.is_dir():
                    shutil.rmtree(pycache_dir)
                    self.organization_log.append(f"🗑️ حذف: {pycache_dir}")
            
            # حذف ملفات .pyc
            for pyc_file in self.project_dir.rglob("*.pyc"):
                pyc_file.unlink()
                self.organization_log.append(f"🗑️ حذف: {pyc_file}")
            
            # نقل الملفات المتبقية في الجذر إلى الأرشيف
            root_files = [f for f in self.project_dir.glob("*") if f.is_file() and f.name not in [
                "project_analyzer_organizer.py", "project_organizer_implementation.py"
            ]]
            
            if root_files:
                archive_misc = self.project_dir / "09_archive" / "misc"
                archive_misc.mkdir(parents=True, exist_ok=True)
                
                for file in root_files:
                    dest_path = archive_misc / file.name
                    shutil.move(str(file), str(dest_path))
                    self.organization_log.append(f"📦 أرشف: {file.name}")
            
            logger.info("✅ تم تنظيف الملفات بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في تنظيف الملفات: {e}")
            self.organization_log.append(f"❌ فشل في تنظيف الملفات: {e}")
            return False
    
    def update_import_paths(self) -> bool:
        """تحديث مسارات الاستيراد في ملفات Python"""
        try:
            logger.info("🔧 تحديث مسارات الاستيراد...")
            
            # البحث عن جميع ملفات Python في الهيكل الجديد
            python_files = list(self.project_dir.rglob("*.py"))
            
            # خريطة تحديث المسارات
            path_updates = {
                "from core.managers.team_workflow_manager": "from core.managers.team_workflow_manager",
                "from core.interfaces.horus_interface": "from core.interfaces.horus_interface",
                "import core.managers.team_workflow_manager": "import core.managers.team_workflow_manager",
                "import core.interfaces.horus_interface": "import core.interfaces.horus_interface"
            }
            
            updated_files = 0
            for py_file in python_files:
                try:
                    with open(py_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    original_content = content
                    
                    # تطبيق تحديثات المسارات
                    for old_path, new_path in path_updates.items():
                        content = content.replace(old_path, new_path)
                    
                    # حفظ الملف إذا تم تعديله
                    if content != original_content:
                        with open(py_file, 'w', encoding='utf-8') as f:
                            f.write(content)
                        updated_files += 1
                        self.organization_log.append(f"🔧 تحديث مسارات: {py_file.name}")
                
                except Exception as e:
                    logger.warning(f"⚠️ لا يمكن تحديث {py_file}: {e}")
            
            logger.info(f"✅ تم تحديث {updated_files} ملف")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحديث المسارات: {e}")
            self.organization_log.append(f"❌ فشل في تحديث المسارات: {e}")
            return False
    
    def create_main_readme(self) -> bool:
        """إنشاء ملف README رئيسي جديد"""
        try:
            logger.info("📝 إنشاء ملف README رئيسي جديد...")
            
            readme_content = """# 🏺 فريق حورس للذكاء الاصطناعي - المشروع المنظم
# HORUS AI Team - Organized Project

## 📋 نظرة عامة
مشروع فريق حورس للذكاء الاصطناعي التعاوني - تم تنظيمه وهيكلته بشكل احترافي.

## 🏗️ هيكل المشروع الجديد

### 📁 المجلدات الرئيسية:

#### 01_core/ - الأنظمة الأساسية
- `engines/` - المحركات الرئيسية
- `managers/` - مديري النظام
- `interfaces/` - واجهات التفاعل

#### 02_team_members/ - أعضاء الفريق
- `local_models/` - النماذج المحلية
- `external_models/` - النماذج الخارجية
- `configurations/` - تكوينات الأعضاء

#### 03_memory_system/ - نظام الذاكرة
- `brain/` - العقل المتكامل
- `memory/` - إدارة الذاكرة
- `learning/` - التعلم التكيفي
- `patterns/` - تحليل الأنماط

#### 04_collaboration/ - أدوات التعاون
- `helpers/` - المساعدين
- `systems/` - أنظمة التعاون
- `workflows/` - سير العمل

#### 05_analysis/ - التحليلات والتقارير
- `reports/` - التقارير
- `consultations/` - الاستشارات
- `enhancements/` - التحسينات

#### 06_documentation/ - التوثيق
- `guides/` - الأدلة
- `reports/` - التقارير
- `plans/` - الخطط

#### 07_configuration/ - التكوين
- `requirements/` - متطلبات النظام
- `configs/` - ملفات التكوين
- `settings/` - الإعدادات

#### 08_utilities/ - الأدوات المساعدة
- `helpers/` - المساعدين
- `tools/` - الأدوات
- `scripts/` - النصوص

#### 09_archive/ - الأرشيف
- `old_versions/` - الإصدارات القديمة
- `deprecated/` - الملفات المهجورة
- `backup/` - النسخ الاحتياطية

## 🚀 البدء السريع

```bash
# تشغيل النظام الأساسي
python 01_core/engines/horus_launcher.py

# تشغيل واجهة التفاعل
python 01_core/interfaces/horus_interface.py

# إدارة سير العمل
python 01_core/managers/team_workflow_manager.py
```

## 📊 إحصائيات المشروع
- **الملفات:** 54 ملف
- **ملفات Python:** 19 ملف
- **المجلدات:** 8+ مجلدات منظمة
- **الحجم:** 0.54 MB

## 🎯 الميزات الجديدة
- ✅ هيكل منظم ومرتب
- ✅ فصل الاهتمامات
- ✅ سهولة الصيانة
- ✅ قابلية التوسع
- ✅ توثيق شامل

## 📞 الدعم والمساعدة
للحصول على المساعدة، راجع ملفات التوثيق في مجلد `06_documentation/`.

---

**🏆 تم تنظيم هذا المشروع بواسطة نظام تحليل وتنظيم فريق حورس المتقدم**
"""
            
            readme_path = self.project_dir / "README.md"
            with open(readme_path, 'w', encoding='utf-8') as f:
                f.write(readme_content)
            
            self.organization_log.append("📝 تم إنشاء ملف README رئيسي جديد")
            logger.info("✅ تم إنشاء ملف README رئيسي جديد")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء README: {e}")
            return False
    
    def save_organization_log(self) -> str:
        """حفظ سجل عملية التنظيم"""
        try:
            log_data = {
                "organization_completed": datetime.now().isoformat(),
                "backup_location": str(self.backup_dir),
                "operations_log": self.organization_log,
                "new_structure": self.new_structure,
                "migration_summary": {
                    "files_migrated": len([log for log in self.organization_log if "نقل ملف" in log]),
                    "directories_migrated": len([log for log in self.organization_log if "نقل مجلد" in log]),
                    "files_cleaned": len([log for log in self.organization_log if "حذف" in log]),
                    "files_archived": len([log for log in self.organization_log if "أرشف" in log])
                }
            }
            
            log_file = self.project_dir / "project_analysis" / f"organization_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            log_file.parent.mkdir(exist_ok=True)
            
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(log_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"💾 تم حفظ سجل التنظيم: {log_file}")
            return str(log_file)
            
        except Exception as e:
            logger.error(f"❌ خطأ في حفظ السجل: {e}")
            return ""
    
    def execute_organization_plan(self) -> Dict[str, Any]:
        """تنفيذ خطة التنظيم الكاملة"""
        logger.info("🚀 بدء تنفيذ خطة التنظيم الكاملة...")
        
        results = {
            "started": datetime.now().isoformat(),
            "steps_completed": [],
            "steps_failed": [],
            "overall_success": True
        }
        
        # الخطوة 1: إنشاء نسخة احتياطية
        if self.create_backup():
            results["steps_completed"].append("إنشاء نسخة احتياطية")
        else:
            results["steps_failed"].append("إنشاء نسخة احتياطية")
            results["overall_success"] = False
            return results
        
        # الخطوة 2: إنشاء الهيكل الجديد
        if self.create_new_structure():
            results["steps_completed"].append("إنشاء الهيكل الجديد")
        else:
            results["steps_failed"].append("إنشاء الهيكل الجديد")
            results["overall_success"] = False
        
        # الخطوة 3: نقل الملفات
        if self.migrate_files():
            results["steps_completed"].append("نقل الملفات")
        else:
            results["steps_failed"].append("نقل الملفات")
            results["overall_success"] = False
        
        # الخطوة 4: تنظيف الملفات القديمة
        if self.cleanup_old_files():
            results["steps_completed"].append("تنظيف الملفات القديمة")
        else:
            results["steps_failed"].append("تنظيف الملفات القديمة")
        
        # الخطوة 5: تحديث مسارات الاستيراد
        if self.update_import_paths():
            results["steps_completed"].append("تحديث مسارات الاستيراد")
        else:
            results["steps_failed"].append("تحديث مسارات الاستيراد")
        
        # الخطوة 6: إنشاء README جديد
        if self.create_main_readme():
            results["steps_completed"].append("إنشاء README جديد")
        else:
            results["steps_failed"].append("إنشاء README جديد")
        
        # حفظ السجل
        log_file = self.save_organization_log()
        if log_file:
            results["log_file"] = log_file
        
        results["completed"] = datetime.now().isoformat()
        results["backup_location"] = str(self.backup_dir)
        
        return results

async def main():
    """الدالة الرئيسية"""
    print("🛠️ منفذ خطة تنظيم مشروع فريق حورس")
    print("=" * 80)
    
    # إنشاء منفذ التنظيم
    organizer = HorusProjectOrganizerImplementation()
    
    # تنفيذ خطة التنظيم
    print("\n🚀 تنفيذ خطة التنظيم الكاملة...")
    results = organizer.execute_organization_plan()
    
    print(f"\n✅ نتائج التنظيم:")
    print(f"   📊 النجاح العام: {'✅ نعم' if results['overall_success'] else '❌ لا'}")
    print(f"   ✅ الخطوات المكتملة: {len(results['steps_completed'])}")
    print(f"   ❌ الخطوات الفاشلة: {len(results['steps_failed'])}")
    
    print(f"\n✅ الخطوات المكتملة:")
    for step in results['steps_completed']:
        print(f"   ✅ {step}")
    
    if results['steps_failed']:
        print(f"\n❌ الخطوات الفاشلة:")
        for step in results['steps_failed']:
            print(f"   ❌ {step}")
    
    print(f"\n💾 النسخة الاحتياطية: {results.get('backup_location', 'غير متوفرة')}")
    print(f"📁 ملف السجل: {results.get('log_file', 'غير متوفر')}")
    
    print(f"\n🎉 تم تنظيم المشروع بنجاح!")
    print(f"   📁 الهيكل الجديد جاهز للاستخدام")
    print(f"   💾 النسخة الاحتياطية محفوظة بأمان")
    print(f"   📝 التوثيق محدث ومنظم")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
