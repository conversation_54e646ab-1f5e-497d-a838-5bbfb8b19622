#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧠 وكيل الذاكرة المحسن
Enhanced Memory Agent
"""

import os
import sys
import json
from pathlib import Path
from typing import Dict, Any, List
from datetime import datetime

sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'core'))
from base_agent import BaseAgent

class EnhancedMemoryAgent(BaseAgent):
    """🧠 وكيل الذاكرة المحسن"""
    
    def get_agent_type(self) -> str:
        return "enhanced_memory_agent"
    
    def initialize_agent(self):
        """تهيئة وكيل الذاكرة"""
        self.memory_file = self.project_path / 'memory.json'
        self.memory_data = self._load_memory()
        
        self.log_action("تهيئة وكيل الذاكرة المحسن", "الذاكرة جاهزة للاستخدام")
    
    def _load_memory(self):
        """تحميل الذاكرة من الملف"""
        if self.memory_file.exists():
            try:
                with open(self.memory_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass
        return {}
    
    def _save_memory(self):
        """حفظ الذاكرة في الملف"""
        try:
            with open(self.memory_file, 'w', encoding='utf-8') as f:
                json.dump(self.memory_data, f, ensure_ascii=False, indent=2)
            return True
        except:
            return False
    
    def store_memory(self, key: str, data: Any, category: str = 'general'):
        """تخزين ذكرى جديدة"""
        memory_entry = {
            'data': data,
            'timestamp': datetime.now().isoformat(),
            'category': category
        }
        
        self.memory_data[key] = memory_entry
        
        if self._save_memory():
            self.log_action("تخزين ذكرى", f"المفتاح: {key}")
            return {
                'status': 'stored',
                'key': key,
                'timestamp': memory_entry['timestamp']
            }
        else:
            return {'status': 'error', 'message': 'فشل في حفظ الذاكرة'}
    
    def retrieve_memory(self, key: str):
        """استرجاع ذكرى محددة"""
        if key in self.memory_data:
            memory = self.memory_data[key]
            self.log_action("استرجاع ذكرى", f"المفتاح: {key}")
            return {
                'key': key,
                'data': memory['data'],
                'timestamp': memory['timestamp'],
                'category': memory['category']
            }
        
        return None
    
    def search_memory(self, query: str):
        """البحث في الذاكرة"""
        results = []
        query_lower = query.lower()
        
        for key, memory in self.memory_data.items():
            # البحث في المفتاح
            if query_lower in key.lower():
                results.append({
                    'key': key,
                    'data': memory['data'],
                    'timestamp': memory['timestamp'],
                    'category': memory['category'],
                    'match_type': 'key'
                })
                continue
            
            # البحث في البيانات
            data_str = str(memory['data']).lower()
            if query_lower in data_str:
                results.append({
                    'key': key,
                    'data': memory['data'],
                    'timestamp': memory['timestamp'],
                    'category': memory['category'],
                    'match_type': 'data'
                })
        
        self.log_action("البحث في الذاكرة", f"الاستعلام: {query}, النتائج: {len(results)}")
        return results
    
    def get_all_memories(self):
        """الحصول على جميع الذكريات"""
        memories = []
        for key, memory in self.memory_data.items():
            memories.append({
                'key': key,
                'timestamp': memory['timestamp'],
                'category': memory['category'],
                'data_preview': str(memory['data'])[:100] + "..." if len(str(memory['data'])) > 100 else str(memory['data'])
            })
        
        # ترتيب حسب التاريخ (الأحدث أولاً)
        memories.sort(key=lambda x: x['timestamp'], reverse=True)
        return memories
    
    def delete_memory(self, key: str):
        """حذف ذكرى محددة"""
        if key in self.memory_data:
            del self.memory_data[key]
            if self._save_memory():
                self.log_action("حذف ذكرى", f"المفتاح: {key}")
                return {'status': 'deleted', 'key': key}
            else:
                return {'status': 'error', 'message': 'فشل في حفظ التغييرات'}
        
        return {'status': 'not_found', 'key': key}
    
    def get_memory_stats(self):
        """إحصائيات الذاكرة"""
        categories = {}
        for memory in self.memory_data.values():
            category = memory['category']
            categories[category] = categories.get(category, 0) + 1
        
        return {
            'total_memories': len(self.memory_data),
            'categories': categories,
            'memory_file': str(self.memory_file),
            'file_exists': self.memory_file.exists()
        }
