#!/bin/bash
# سكريبت تشغيل نظام العزل المتقدم

echo "🛡️ بدء تشغيل نظام العزل المتقدم لأنوبيس..."

# التحقق من المتطلبات
echo "🔍 فحص المتطلبات..."

if ! command -v docker &> /dev/null; then
    echo "❌ Docker غير مثبت"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose غير مثبت"
    exit 1
fi

# التحقق من دعم الأمان المتقدم
if ! docker info | grep -q "Security Options"; then
    echo "⚠️ تحذير: إعدادات الأمان المتقدمة قد لا تكون مدعومة"
fi

# الانتقال لمجلد العزل المتقدم
cd isolation_systems/advanced_isolation

# إنشاء البنية التحتية الآمنة
echo "🏗️ إنشاء البنية التحتية الآمنة..."
mkdir -p data logs secrets configs monitoring security
mkdir -p security/policies security/certificates security/keys

# تعيين صلاحيات أمان متقدمة
echo "🔒 تطبيق صلاحيات الأمان المتقدمة..."
chmod 700 data secrets security
chmod 750 logs configs monitoring
chmod 640 security/policies/*

# إنشاء الشبكات المعزولة
echo "🌐 إنشاء الشبكات المعزولة..."
docker network create anubis-advanced-net --driver bridge --internal 2>/dev/null || true
docker network create anubis-monitoring-net --driver bridge --internal 2>/dev/null || true
docker network create anubis-data-net --driver bridge --internal 2>/dev/null || true

# بناء النظام المتقدم
echo "🔨 بناء نظام العزل المتقدم..."
docker-compose build

# تشغيل خدمات الأمان أولاً
echo "🛡️ تشغيل خدمات الأمان..."
docker-compose up -d anubis-vault anubis-monitor

# انتظار تجهيز خدمات الأمان
echo "⏳ انتظار تجهيز خدمات الأمان..."
sleep 30

# تشغيل النظام الرئيسي
echo "🚀 تشغيل النظام الرئيسي..."
docker-compose up -d anubis-advanced

# تشغيل فحص الأمان
echo "🧪 تشغيل فحص الأمان..."
docker-compose up anubis-scanner

# عرض الحالة النهائية
echo "📊 حالة النظام:"
docker-compose ps

echo ""
echo "🎉 تم تشغيل نظام العزل المتقدم بنجاح!"
echo "🌐 النظام الرئيسي: http://localhost:8002"
echo "📊 مراقبة الأمان: http://localhost:9090"
echo "🔐 مخزن الأسرار: http://localhost:8200"
echo ""
echo "📋 أوامر مفيدة:"
echo "   السجلات: docker-compose logs -f"
echo "   الحالة: docker-compose ps"
echo "   الإيقاف: docker-compose down"
echo "   فحص الأمان: docker-compose up anubis-scanner"
