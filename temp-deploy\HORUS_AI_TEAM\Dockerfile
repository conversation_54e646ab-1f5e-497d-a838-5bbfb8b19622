# 𓅃 Dockerfile لفريق حورس
FROM python:3.11-slim

# تعيين متغيرات البيئة
ENV PYTHONUNBUFFERED=1
ENV PYTHONPATH=/app
ENV HORUS_ENV=production

# تثبيت المتطلبات الأساسية
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    git \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# إنشاء مستخدم غير جذر
RUN useradd -m -u 1000 horus && \
    mkdir -p /app && \
    chown -R horus:horus /app

# تعيين مجلد العمل
WORKDIR /app

# نسخ ملفات المتطلبات (إنشاء ملف محلي)
RUN echo "fastapi==0.104.1\nuvicorn==0.24.0\nrequests==2.31.0\npsutil==5.9.6\nflask==3.0.0" > requirements.txt

# تثبيت المتطلبات Python
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# نسخ الكود المصدري
COPY . .

# إنشاء المجلدات المطلوبة
RUN mkdir -p /app/03_memory_system/data /app/logs /app/temp && \
    chown -R horus:horus /app

# إنشاء خادم Flask بسيط لفريق حورس
RUN echo '#!/usr/bin/env python3\n\
    import sys\n\
    import os\n\
    from flask import Flask, jsonify, request\n\
    \n\
    sys.path.append("/app")\n\
    sys.path.append("/app/01_core/interfaces")\n\
    \n\
    app = Flask(__name__)\n\
    \n\
    try:\n\
    from horus_interface import horus\n\
    horus_available = True\n\
    except Exception as e:\n\
    print(f"تحذير: فريق حورس غير متاح: {e}")\n\
    horus_available = False\n\
    \n\
    @app.route("/health")\n\
    def health():\n\
    return jsonify({\n\
    "status": "healthy",\n\
    "service": "horus-team",\n\
    "horus_available": horus_available\n\
    })\n\
    \n\
    @app.route("/api/ask", methods=["POST"])\n\
    def ask_horus():\n\
    if not horus_available:\n\
    return jsonify({"error": "فريق حورس غير متاح"}), 503\n\
    \n\
    data = request.get_json()\n\
    message = data.get("message", "")\n\
    \n\
    if not message:\n\
    return jsonify({"error": "الرسالة مطلوبة"}), 400\n\
    \n\
    try:\n\
    response = horus.ask(message)\n\
    return jsonify({\n\
    "response": response,\n\
    "source": "horus-team"\n\
    })\n\
    except Exception as e:\n\
    return jsonify({"error": str(e)}), 500\n\
    \n\
    @app.route("/api/status")\n\
    def status():\n\
    return jsonify({\n\
    "horus_available": horus_available,\n\
    "team_members": [\n\
    "THOTH", "PTAH", "RA", "KHNUM", "SESHAT", "ANUBIS", "MAAT", "HAPI"\n\
    ] if horus_available else []\n\
    })\n\
    \n\
    if __name__ == "__main__":\n\
    app.run(host="0.0.0.0", port=7000, debug=False)\n\
    ' > /app/horus_server.py && chmod +x /app/horus_server.py

# التبديل للمستخدم غير الجذر
USER horus

# فحص الصحة
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:7000/health || exit 1

# تعريض المنفذ
EXPOSE 7000

# الأمر الافتراضي
CMD ["python", "horus_server.py"]
