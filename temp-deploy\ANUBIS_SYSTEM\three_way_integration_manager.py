#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔗 مدير التكامل الثلاثي
Three-Way Integration Manager
"""

import os
import sys
import json
import time
import threading
from datetime import datetime

class ThreeWayIntegrationManager:
    def __init__(self):
        self.anubis_available = False
        self.mcp_available = False
        self.horus_available = False
        self.integration_active = False
        
    def check_systems_availability(self):
        """فحص توفر الأنظمة"""
        print("🔍 فحص توفر الأنظمة...")
        
        # فحص أنوبيس
        try:
            import requests
            response = requests.get("http://localhost:8000/health", timeout=5)
            if response.status_code == 200:
                self.anubis_available = True
                print("✅ نظام أنوبيس متاح")
        except:
            print("❌ نظام أنوبيس غير متاح")
        
        # فحص MCP
        if os.path.exists("../ANUBIS_HORUS_MCP"):
            self.mcp_available = True
            print("✅ نظام MCP متاح")
        else:
            print("❌ نظام MCP غير متاح")
        
        # فحص حورس
        if os.path.exists("../HORUS_AI_TEAM"):
            self.horus_available = True
            print("✅ فريق حورس متاح")
        else:
            print("❌ فريق حورس غير متاح")
        
        return self.anubis_available and self.mcp_available and self.horus_available
    
    def start_integration(self):
        """بدء التكامل الثلاثي"""
        print("🚀 بدء التكامل الثلاثي...")
        
        if not self.check_systems_availability():
            print("❌ لا يمكن بدء التكامل - أنظمة مفقودة")
            return False
        
        try:
            # تشغيل جسر أنوبيس → MCP
            print("🏺→🔗 تشغيل جسر أنوبيس → MCP...")
            os.system("python anubis_mcp_bridge.py")
            
            # تشغيل جسر أنوبيس → حورس
            print("🏺→𓅃 تشغيل جسر أنوبيس → حورس...")
            os.system("python anubis_horus_bridge.py")
            
            # تفعيل التكامل
            self.integration_active = True
            print("✅ تم تفعيل التكامل الثلاثي")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في بدء التكامل: {str(e)}")
            return False
    
    def monitor_integration(self):
        """مراقبة التكامل"""
        print("👁️ بدء مراقبة التكامل...")
        
        while self.integration_active:
            try:
                # فحص دوري للأنظمة
                if self.check_systems_availability():
                    print(f"✅ {datetime.now().strftime('%H:%M:%S')} - جميع الأنظمة تعمل")
                else:
                    print(f"⚠️ {datetime.now().strftime('%H:%M:%S')} - مشكلة في أحد الأنظمة")
                
                time.sleep(30)  # فحص كل 30 ثانية
                
            except KeyboardInterrupt:
                print("🛑 تم إيقاف المراقبة")
                break
            except Exception as e:
                print(f"❌ خطأ في المراقبة: {str(e)}")
                time.sleep(10)
    
    def stop_integration(self):
        """إيقاف التكامل"""
        print("🛑 إيقاف التكامل الثلاثي...")
        self.integration_active = False

# تشغيل المدير
if __name__ == "__main__":
    manager = ThreeWayIntegrationManager()
    
    print("🔗 مدير التكامل الثلاثي")
    print("🏺 أنوبيس ↔ 🔗 MCP ↔ 𓅃 حورس")
    
    if manager.start_integration():
        try:
            manager.monitor_integration()
        except KeyboardInterrupt:
            manager.stop_integration()
    else:
        print("❌ فشل في بدء التكامل")
