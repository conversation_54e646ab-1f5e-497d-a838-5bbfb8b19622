@echo off
chcp 65001 >nul
color 0A
title VS Code Control Center - Unified Interface

cls
echo.
echo ================================================================
echo                VS Code Control Center                          
echo                Unified Optimal Interface                      
echo ================================================================
echo.
echo Starting unified interface...
echo.

REM Check Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python not installed!
    echo Please download and install Python from: https://python.org
    pause
    exit /b 1
)

echo Python OK

REM Check basic libraries
echo Checking required libraries...
python -c "import tkinter, psutil" >nul 2>&1
if %errorlevel% neq 0 (
    echo Installing basic libraries...
    pip install psutil
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install basic libraries!
        echo Try manually: pip install psutil
        pause
        exit /b 1
    )
)

REM Check AI agent libraries (optional)
python -c "import requests" >nul 2>&1
if %errorlevel% neq 0 (
    echo Installing AI agent libraries...
    pip install requests >nul 2>&1
    if %errorlevel% neq 0 (
        echo WARNING: Will run in basic mode (without AI agents)
    ) else (
        echo AI agent libraries installed
    )
) else (
    echo AI agent libraries available
)

echo.
echo Features available:
echo    - Live system and VS Code monitoring
echo    - Beautiful interface with smart warning colors
echo    - Auto-refresh every 3 seconds
echo    - Cleaning and optimization tools
echo    - Detailed report saving

python -c "import sys; sys.path.append('agents'); from agents.agent_coordinator import AgentCoordinator" >nul 2>&1
if %errorlevel% equ 0 (
    echo    - Intelligent agents for advanced analysis
    echo    - Chat with agents
    echo    - Custom and intelligent recommendations
)

echo.
echo Starting VS Code Control Center...
echo.

REM Run the unified application
python vscode_control_center.py

if %errorlevel% neq 0 (
    echo.
    echo ERROR: Failed to start application!
    echo Make sure file exists: vscode_control_center.py
    echo Or check if required libraries are installed
    pause
    exit /b 1
)

echo.
echo VS Code Control Center closed
echo Thank you for using the optimal interface!
pause
