# Jupyter Lab Configuration for Anubis Workspace
import os

# Server settings
c.ServerApp.ip = '0.0.0.0'
c.ServerApp.port = 8888
c.ServerApp.allow_root = False
c.ServerApp.open_browser = False

# Security settings
c.ServerApp.token = os.environ.get('JUPYTER_TOKEN', 'anubis_workspace_token')
c.ServerApp.password = ''
c.ServerApp.allow_origin = '*'
c.ServerApp.allow_remote_access = True

# File and directory settings
c.ServerApp.root_dir = '/app/workspace'
c.ServerApp.notebook_dir = '/app/workspace/notebooks'

# Logging
c.ServerApp.log_level = 'INFO'

# Extensions
c.ServerApp.jpserver_extensions = {
    'jupyterlab': True
}
