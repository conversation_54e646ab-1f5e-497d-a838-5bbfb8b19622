#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 فاحص حالة النظام المعزول - أنوبيس
Anubis Isolation Status Checker
"""

import requests
import subprocess
import json
import time
from datetime import datetime

def check_docker_containers():
    """فحص حالة الحاويات"""
    try:
        result = subprocess.run([
            'docker-compose', '-f', 'docker-compose-anubis-isolation.yml', 'ps', '--format', 'json'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            containers = []
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    try:
                        container = json.loads(line)
                        containers.append(container)
                    except:
                        pass
            return containers
        else:
            print(f"❌ خطأ في فحص الحاويات: {result.stderr}")
            return []
            
    except Exception as e:
        print(f"❌ خطأ في فحص الحاويات: {e}")
        return []

def check_service_health(url, service_name):
    """فحص صحة خدمة"""
    try:
        response = requests.get(url, timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ {service_name}: صحية")
            return True, data
        else:
            print(f"⚠️  {service_name}: تستجيب لكن بحالة {response.status_code}")
            return False, None
    except requests.exceptions.ConnectionError:
        print(f"❌ {service_name}: غير متاحة")
        return False, None
    except Exception as e:
        print(f"⚠️  {service_name}: خطأ - {e}")
        return False, None

def check_api_endpoints():
    """فحص نقاط API"""
    base_url = "http://localhost:8080"
    endpoints = [
        ("/", "الصفحة الرئيسية"),
        ("/health", "فحص الصحة"),
        ("/info", "معلومات الخدمة"),
        ("/status", "حالة الخدمة"),
    ]
    
    print("\n🔍 فحص نقاط API:")
    print("-" * 30)
    
    for endpoint, description in endpoints:
        url = f"{base_url}{endpoint}"
        success, data = check_service_health(url, f"{description} ({endpoint})")
        if success and data:
            if endpoint == "/health":
                print(f"   📊 الحالة: {data.get('status', 'غير محدد')}")
                print(f"   🕐 الوقت: {data.get('timestamp', 'غير محدد')}")
            elif endpoint == "/":
                print(f"   📝 الرسالة: {data.get('message', 'غير محدد')}")
                print(f"   🔒 معزول: {data.get('isolated', 'غير محدد')}")

def show_container_status():
    """عرض حالة الحاويات"""
    print("\n📊 حالة الحاويات:")
    print("=" * 50)
    
    containers = check_docker_containers()
    
    if not containers:
        print("❌ لا توجد حاويات أو خطأ في الفحص")
        return
    
    for container in containers:
        name = container.get('Name', 'غير محدد')
        status = container.get('State', 'غير محدد')
        health = container.get('Health', 'غير محدد')
        ports = container.get('Publishers', [])
        
        # تحديد رمز الحالة
        if status == 'running':
            if health == 'healthy':
                status_icon = "✅"
            elif health == 'starting':
                status_icon = "🔄"
            else:
                status_icon = "🟡"
        else:
            status_icon = "❌"
        
        print(f"{status_icon} {name}")
        print(f"   📊 الحالة: {status}")
        if health != 'غير محدد':
            print(f"   🏥 الصحة: {health}")
        
        if ports:
            port_info = []
            for port in ports:
                if isinstance(port, dict):
                    published = port.get('PublishedPort', '')
                    target = port.get('TargetPort', '')
                    if published and target:
                        port_info.append(f"{published}:{target}")
            if port_info:
                print(f"   🌐 المنافذ: {', '.join(port_info)}")
        print()

def show_network_info():
    """عرض معلومات الشبكة"""
    print("🌐 معلومات الشبكة:")
    print("-" * 30)
    
    try:
        result = subprocess.run([
            'docker', 'network', 'ls', '--filter', 'name=anubis-isolation-net', '--format', 'table {{.Name}}\t{{.Driver}}\t{{.Scope}}'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print(result.stdout)
        else:
            print("❌ خطأ في فحص الشبكة")
            
    except Exception as e:
        print(f"❌ خطأ في فحص الشبكة: {e}")

def show_volume_info():
    """عرض معلومات الأحجام"""
    print("💾 معلومات الأحجام:")
    print("-" * 30)
    
    try:
        result = subprocess.run([
            'docker', 'volume', 'ls', '--filter', 'label=anubis.system=isolation', '--format', 'table {{.Name}}\t{{.Driver}}'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print(result.stdout)
        else:
            print("❌ خطأ في فحص الأحجام")
            
    except Exception as e:
        print(f"❌ خطأ في فحص الأحجام: {e}")

def show_service_urls():
    """عرض روابط الخدمات"""
    print("\n🌐 روابط الخدمات:")
    print("=" * 30)
    print("📱 API الرئيسي: http://localhost:8080")
    print("📚 API Docs: http://localhost:8080/docs")
    print("📋 API Redoc: http://localhost:8080/redoc")
    print("🏥 فحص الصحة: http://localhost:8080/health")
    print("📊 حالة الخدمة: http://localhost:8080/status")
    print("📈 المقاييس: http://localhost:8080/metrics")

def main():
    """الدالة الرئيسية"""
    print("🏺 فاحص حالة النظام المعزول - أنوبيس")
    print("=" * 60)
    print(f"🕐 وقت الفحص: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # فحص حالة الحاويات
    show_container_status()
    
    # فحص نقاط API
    check_api_endpoints()
    
    # عرض معلومات الشبكة
    print()
    show_network_info()
    
    # عرض معلومات الأحجام
    print()
    show_volume_info()
    
    # عرض روابط الخدمات
    show_service_urls()
    
    print(f"\n✅ تم إكمال فحص النظام المعزول")

if __name__ == "__main__":
    main()
