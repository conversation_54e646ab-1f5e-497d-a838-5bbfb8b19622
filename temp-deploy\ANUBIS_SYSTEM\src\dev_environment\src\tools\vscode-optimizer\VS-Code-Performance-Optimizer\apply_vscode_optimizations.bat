@echo off
chcp 65001 >nul
color 0A
title تطبيق تحسينات VS Code

echo.
echo ================================================================
echo                    تطبيق تحسينات VS Code                     
echo ================================================================
echo.

echo 🚀 تطبيق التحسينات على VS Code...
echo.

echo 📋 الخطوات المطلوبة:
echo.

echo 1️⃣ تطبيق إعدادات الأداء:
echo    • افتح VS Code
echo    • اضغط Ctrl+Shift+P
echo    • اكتب: "Preferences: Open Settings (JSON)"
echo    • انسخ المحتوى من: vscode_optimized_settings.json
echo    • الصق في settings.json
echo    • احفظ الملف (Ctrl+S)
echo.

echo 2️⃣ تعطيل تسريع الأجهزة:
echo    • في VS Code: File → Preferences → Settings
echo    • ابحث عن: "hardware acceleration"
echo    • أزل العلامة من: "Use hardware acceleration when available"
echo    • أعد تشغيل VS Code
echo.

echo 3️⃣ تعطيل الإضافات الثقيلة:
echo    • اضغط Ctrl+Shift+X في VS Code
echo    • ابحث عن هذه الإضافات وعطلها:
echo      - SonarQube for IDE (423 MB)
echo      - VS Code Speech (257 MB)
echo      - C# (إذا لم تستخدمه - 405 MB)
echo      - PowerShell (إحدى النسختين - 301 MB)
echo      - IntelliPHP - AI Autocomplete (161 MB)
echo.

echo 4️⃣ إعادة تحميل النافذة:
echo    • اضغط Ctrl+Shift+P
echo    • اكتب: "Developer: Reload Window"
echo    • اضغط Enter
echo.

echo 🎯 النتيجة المتوقعة:
echo    • انخفاض استهلاك VS Code من 31%% إلى أقل من 15%%
echo    • استجابة أسرع للواجهة
echo    • تحرير ذاكرة إضافية
echo    • تحسن عام في الأداء
echo.

echo ⚠️ ملاحظة: احفظ عملك قبل تطبيق التغييرات
echo.

echo 📁 الملفات المساعدة:
echo    • vscode_optimized_settings.json - إعدادات الأداء
echo    • disable_heavy_extensions.bat - دليل تعطيل الإضافات
echo.

pause

echo.
echo 🔄 هل تريد فتح VS Code الآن لتطبيق التحسينات؟ (Y/N)
set /p choice=

if /i "%choice%"=="Y" (
    echo 🚀 فتح VS Code...
    start code
    echo ✅ تم فتح VS Code
    echo 💡 طبق الخطوات المذكورة أعلاه
) else (
    echo 💡 يمكنك تطبيق التحسينات لاحقاً
)

echo.
echo ✅ انتهى السكريبت
pause
