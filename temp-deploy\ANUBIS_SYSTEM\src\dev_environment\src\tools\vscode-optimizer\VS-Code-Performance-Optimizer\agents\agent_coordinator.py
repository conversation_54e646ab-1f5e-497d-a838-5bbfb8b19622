# -*- coding: utf-8 -*-
"""
🎯 منسق الوكلاء - Agent Coordinator
==================================

المنسق الرئيسي لجميع الوكلاء الذكيين في النظام
"""

import time
import threading
import json
from typing import Dict, List, Any, Optional
from .base_agent import BaseAgent
from .process_analyzer import ProcessAnalyzerAgent
from .performance_optimizer import PerformanceOptimizerAgent
from .security_monitor import SecurityMonitorAgent
from .smart_recommendations import SmartRecommendationsAgent
from .gemini_agent import GeminiAgent
from .ollama_agent import OllamaAgent

class AgentCoordinator:
    """منسق الوكلاء الذكي"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.agents = {}
        self.is_running = False
        self.analysis_results = {}
        self.coordination_history = []
        
        # إنشاء الوكلاء
        self._initialize_agents()
        
    def _initialize_agents(self):
        """تهيئة جميع الوكلاء"""
        try:
            # الوكلاء الأساسيين
            self.agents['process_analyzer'] = ProcessAnalyzerAgent(self.config)
            self.agents['performance_optimizer'] = PerformanceOptimizerAgent(self.config)
            self.agents['security_monitor'] = SecurityMonitorAgent(self.config)
            self.agents['smart_recommendations'] = SmartRecommendationsAgent(self.config)
            
            # وكلاء الذكاء الاصطناعي
            self.agents['gemini_agent'] = GeminiAgent(self.config)
            self.agents['ollama_agent'] = OllamaAgent(self.config)
            
            print("✅ تم تهيئة جميع الوكلاء بنجاح")
            
        except Exception as e:
            print(f"❌ خطأ في تهيئة الوكلاء: {e}")
    
    def start_all_agents(self):
        """تشغيل جميع الوكلاء"""
        self.is_running = True
        
        for name, agent in self.agents.items():
            try:
                agent.start()
                print(f"🚀 تم تشغيل الوكيل: {name}")
            except Exception as e:
                print(f"❌ خطأ في تشغيل الوكيل {name}: {e}")
    
    def stop_all_agents(self):
        """إيقاف جميع الوكلاء"""
        self.is_running = False
        
        for name, agent in self.agents.items():
            try:
                agent.stop()
                print(f"⏹️ تم إيقاف الوكيل: {name}")
            except Exception as e:
                print(f"❌ خطأ في إيقاف الوكيل {name}: {e}")
    
    def run_comprehensive_analysis(self, data: Dict[str, Any] = None) -> Dict[str, Any]:
        """تشغيل تحليل شامل بجميع الوكلاء"""
        print("🔍 بدء التحليل الشامل...")
        
        # جمع البيانات إذا لم تكن متوفرة
        if data is None:
            data = self._collect_system_data()
        
        analysis_results = {
            'timestamp': time.time(),
            'coordinator_analysis': {},
            'agent_results': {},
            'combined_recommendations': [],
            'overall_score': 0,
            'summary': {}
        }
        
        # تشغيل كل وكيل
        for agent_name, agent in self.agents.items():
            try:
                print(f"🔄 تشغيل الوكيل: {agent_name}")
                result = agent.analyze(data)
                analysis_results['agent_results'][agent_name] = result
                
                # جمع التوصيات
                recommendations = agent.get_recommendations(result)
                analysis_results['combined_recommendations'].extend(
                    [f"[{agent_name}] {rec}" for rec in recommendations]
                )
                
            except Exception as e:
                print(f"❌ خطأ في الوكيل {agent_name}: {e}")
                analysis_results['agent_results'][agent_name] = {'error': str(e)}
        
        # تحليل منسق
        analysis_results['coordinator_analysis'] = self._coordinate_analysis(analysis_results)
        analysis_results['overall_score'] = self._calculate_overall_score(analysis_results)
        analysis_results['summary'] = self._generate_summary(analysis_results)
        
        # حفظ النتائج
        self.analysis_results = analysis_results
        self.coordination_history.append(analysis_results)
        
        print("✅ تم إكمال التحليل الشامل")
        return analysis_results
    
    def _collect_system_data(self) -> Dict[str, Any]:
        """جمع بيانات النظام الأساسية"""
        try:
            import psutil
            
            data = {
                'timestamp': time.time(),
                'system_health': {
                    'cpu_usage': psutil.cpu_percent(interval=1),
                    'memory_usage': psutil.virtual_memory().percent,
                    'disk_usage': psutil.disk_usage('/').percent,
                    'available_memory_gb': round(psutil.virtual_memory().available / (1024**3), 2)
                },
                'process_count': len(psutil.pids()),
                'vscode_processes': self._get_vscode_processes()
            }
            
            return data
            
        except Exception as e:
            print(f"❌ خطأ في جمع بيانات النظام: {e}")
            return {'error': str(e)}
    
    def _get_vscode_processes(self) -> Dict[str, Any]:
        """الحصول على معلومات عمليات VS Code"""
        try:
            import psutil
            
            vscode_procs = []
            total_cpu = 0
            total_memory = 0
            
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
                try:
                    if 'code' in proc.info['name'].lower():
                        cpu = proc.cpu_percent()
                        memory = proc.memory_percent()
                        
                        vscode_procs.append({
                            'pid': proc.info['pid'],
                            'name': proc.info['name'],
                            'cpu': cpu,
                            'memory': memory
                        })
                        
                        total_cpu += cpu
                        total_memory += memory
                        
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            return {
                'count': len(vscode_procs),
                'total_cpu_usage': round(total_cpu, 2),
                'total_memory_usage': round(total_memory, 2),
                'processes': vscode_procs
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def _coordinate_analysis(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """تنسيق وتحليل نتائج جميع الوكلاء"""
        coordination = {
            'cross_agent_insights': [],
            'conflicting_recommendations': [],
            'priority_actions': [],
            'coordination_score': 0
        }
        
        try:
            agent_results = results.get('agent_results', {})
            
            # تحليل متقاطع
            # مقارنة نتائج محلل العمليات مع محسن الأداء
            if 'process_analyzer' in agent_results and 'performance_optimizer' in agent_results:
                proc_analysis = agent_results['process_analyzer']
                perf_analysis = agent_results['performance_optimizer']
                
                # مقارنة نقاط الأداء
                if 'optimization_score' in perf_analysis and 'system_health' in proc_analysis:
                    perf_score = perf_analysis.get('optimization_score', 0)
                    system_status = proc_analysis['system_health'].get('status', '')
                    
                    if perf_score < 50 and 'حمولة عالية' in system_status:
                        coordination['cross_agent_insights'].append(
                            'تأكيد متقاطع: النظام يواجه مشاكل أداء حقيقية'
                        )
            
            # مقارنة الأمان مع التوصيات الذكية
            if 'security_monitor' in agent_results and 'smart_recommendations' in agent_results:
                security_score = agent_results['security_monitor'].get('security_score', 100)
                
                if security_score < 70:
                    coordination['priority_actions'].append(
                        'أولوية عالية: معالجة المشاكل الأمنية المكتشفة'
                    )
            
            # تحليل توصيات Gemini و Ollama
            ai_agents = ['gemini_agent', 'ollama_agent']
            ai_recommendations = []
            
            for ai_agent in ai_agents:
                if ai_agent in agent_results:
                    ai_result = agent_results[ai_agent]
                    if 'recommendations' in ai_result:
                        ai_recommendations.extend(ai_result['recommendations'])
            
            if ai_recommendations:
                coordination['cross_agent_insights'].append(
                    f'تحليل AI متقدم: {len(ai_recommendations)} توصية ذكية'
                )
            
            # حساب نقاط التنسيق
            coordination['coordination_score'] = self._calculate_coordination_score(results)
            
        except Exception as e:
            coordination['cross_agent_insights'].append(f'خطأ في التنسيق: {e}')
        
        return coordination
    
    def _calculate_overall_score(self, results: Dict[str, Any]) -> int:
        """حساب النقاط الإجمالية للنظام"""
        scores = []
        
        try:
            agent_results = results.get('agent_results', {})
            
            # جمع النقاط من كل وكيل
            if 'performance_optimizer' in agent_results:
                perf_score = agent_results['performance_optimizer'].get('optimization_score', 0)
                scores.append(perf_score)
            
            if 'security_monitor' in agent_results:
                security_score = agent_results['security_monitor'].get('security_score', 0)
                scores.append(security_score)
            
            if 'smart_recommendations' in agent_results:
                rec_score = agent_results['smart_recommendations'].get('recommendation_score', 0)
                scores.append(rec_score)
            
            # حساب المتوسط
            if scores:
                overall_score = sum(scores) / len(scores)
                return int(overall_score)
            
        except Exception as e:
            print(f"خطأ في حساب النقاط الإجمالية: {e}")
        
        return 50  # نقاط افتراضية
    
    def _calculate_coordination_score(self, results: Dict[str, Any]) -> int:
        """حساب نقاط التنسيق بين الوكلاء"""
        score = 0
        
        try:
            agent_results = results.get('agent_results', {})
            successful_agents = len([r for r in agent_results.values() if 'error' not in r])
            total_agents = len(agent_results)
            
            if total_agents > 0:
                score = int((successful_agents / total_agents) * 100)
            
        except Exception:
            score = 50
        
        return score
    
    def _generate_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """توليد ملخص شامل للتحليل"""
        summary = {
            'overall_status': 'غير محدد',
            'key_findings': [],
            'urgent_actions': [],
            'system_health_summary': {},
            'agent_status_summary': {}
        }
        
        try:
            overall_score = results.get('overall_score', 0)
            
            # تحديد الحالة العامة
            if overall_score >= 80:
                summary['overall_status'] = '🟢 ممتاز'
            elif overall_score >= 60:
                summary['overall_status'] = '🟡 جيد'
            elif overall_score >= 40:
                summary['overall_status'] = '🟠 يحتاج تحسين'
            else:
                summary['overall_status'] = '🔴 يحتاج تدخل فوري'
            
            # استخراج النتائج الرئيسية
            agent_results = results.get('agent_results', {})
            
            for agent_name, agent_result in agent_results.items():
                if 'error' not in agent_result:
                    summary['agent_status_summary'][agent_name] = '✅ نجح'
                    
                    # استخراج النتائج المهمة
                    if agent_name == 'process_analyzer':
                        vscode_data = agent_result.get('vscode_processes', {})
                        if vscode_data.get('count', 0) > 0:
                            summary['key_findings'].append(
                                f"VS Code يشغل {vscode_data['count']} عمليات"
                            )
                    
                    elif agent_name == 'security_monitor':
                        security_score = agent_result.get('security_score', 100)
                        if security_score < 70:
                            summary['urgent_actions'].append(
                                f"تحسين الأمان (نقاط: {security_score})"
                            )
                else:
                    summary['agent_status_summary'][agent_name] = '❌ فشل'
            
            # ملخص صحة النظام
            if 'process_analyzer' in agent_results:
                system_health = agent_results['process_analyzer'].get('system_health', {})
                summary['system_health_summary'] = {
                    'cpu': f"{system_health.get('cpu_usage', 0)}%",
                    'memory': f"{system_health.get('memory_usage', 0)}%",
                    'status': system_health.get('status', 'غير محدد')
                }
            
        except Exception as e:
            summary['key_findings'].append(f'خطأ في توليد الملخص: {e}')
        
        return summary
    
    def get_agent_status(self, agent_name: str = None) -> Dict[str, Any]:
        """الحصول على حالة وكيل محدد أو جميع الوكلاء"""
        if agent_name:
            if agent_name in self.agents:
                return self.agents[agent_name].get_status()
            else:
                return {'error': f'الوكيل {agent_name} غير موجود'}
        else:
            return {name: agent.get_status() for name, agent in self.agents.items()}
    
    def ask_ai_agents(self, question: str) -> Dict[str, str]:
        """سؤال وكلاء الذكاء الاصطناعي"""
        responses = {}
        
        # سؤال Gemini
        if 'gemini_agent' in self.agents:
            try:
                responses['gemini'] = self.agents['gemini_agent'].ask_gemini(question)
            except Exception as e:
                responses['gemini'] = f"خطأ: {e}"
        
        # سؤال Ollama
        if 'ollama_agent' in self.agents:
            try:
                responses['ollama'] = self.agents['ollama_agent'].ask_ollama(question)
            except Exception as e:
                responses['ollama'] = f"خطأ: {e}"
        
        return responses
    
    def export_analysis(self, format_type: str = 'json') -> str:
        """تصدير نتائج التحليل"""
        if not self.analysis_results:
            return "لا توجد نتائج تحليل متاحة"
        
        if format_type.lower() == 'json':
            return json.dumps(self.analysis_results, ensure_ascii=False, indent=2)
        else:
            return str(self.analysis_results)
    
    def get_quick_summary(self) -> str:
        """ملخص سريع للحالة الحالية"""
        if not self.analysis_results:
            return "لا توجد بيانات تحليل متاحة"
        
        summary = self.analysis_results.get('summary', {})
        overall_status = summary.get('overall_status', 'غير محدد')
        overall_score = self.analysis_results.get('overall_score', 0)
        
        return f"الحالة العامة: {overall_status} (النقاط: {overall_score}/100)"
