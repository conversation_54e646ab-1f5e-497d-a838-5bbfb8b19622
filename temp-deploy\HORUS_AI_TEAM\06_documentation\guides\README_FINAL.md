# 𓅃 نظام حورس المتقدم للذكاء الاصطناعي التعاوني

## 🌟 مرحباً بك في عالم الذكاء الاصطناعي المصري

**نظام حورس** هو نظام متقدم للذكاء الاصطناعي التعاوني يجمع بين قوة النماذج المحلية والخارجية في فريق عمل متناغم من الوكلاء المتخصصين المستوحين من الآلهة المصرية القديمة.

## 🚀 التشغيل السريع

### الطريقة الأسهل
```bash
python start_horus.py
```

### التشغيل الكامل
```bash
python horus_complete_system.py
```

### واجهة الويب فقط
```bash
streamlit run web_interface.py
```

## 🤖 فريق الوكلاء المتخصصين

| الوكيل | الرمز | التخصص | النموذج |
|--------|-------|----------|---------|
| **تحوت** | ⚡ | المحلل السريع والباحث | phi3:mini |
| **بتاح** | 🔧 | المطور الخبير والمهندس | mistral:7b |
| **رع** | 🎯 | المستشار الاستراتيجي | llama3:8b |
| **خنوم** | 💡 | المبدع والمبتكر | strikegpt-r1-zero-8b |
| **سشات** | 👁️ | المحللة البصرية | Qwen2.5-VL-7B |
| **حورس** | 𓅃 | المنسق الأعلى | gemini-1.5-flash |

## 📋 المتطلبات

### المتطلبات الأساسية
```bash
pip install google-generativeai streamlit plotly pandas
```

### المتطلبات الكاملة
```bash
pip install -r requirements_complete.txt
```

### النماذج المحلية (Ollama)
```bash
# تثبيت Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# تحميل النماذج
ollama pull phi3:mini
ollama pull mistral:7b
ollama pull llama3:8b
```

## 🎮 أنماط الاستخدام

### 1. النمط التفاعلي
محادثة مباشرة مع فريق حورس من سطر الأوامر:
```bash
𓅃 حورس> ask ما هو أفضل نهج لتطوير تطبيق ويب؟
𓅃 حورس> task تحليل البيانات تحليل شامل لبيانات المبيعات
𓅃 حورس> collab تطوير استراتيجية تسويق جديدة
```

### 2. واجهة الويب
لوحة تحكم شاملة مع:
- إدارة الوكلاء والمهام
- محادثة مباشرة
- إحصائيات وتحليلات
- إعدادات النظام

### 3. العرض التوضيحي
عرض تلقائي لقدرات النظام مع مهام تجريبية متنوعة.

## 🔧 الإعداد والتكوين

### 1. إعداد مفاتيح Gemini
```python
# في ملف .env أو مباشرة في الكود
GEMINI_API_KEY="your_api_key_here"
```

### 2. إعداد Ollama
```bash
# التحقق من النماذج المتاحة
ollama list

# تشغيل نموذج
ollama run phi3:mini
```

### 3. إعدادات النظام
يمكن تخصيص النظام من خلال:
- واجهة الويب > الإعدادات
- تعديل ملفات التكوين في مجلد `07_configuration`

## 📊 الميزات المتقدمة

### 🧠 الذكاء التعاوني
- **4 فرق متخصصة**: تحليل، تطوير، استراتيجية، إبداع
- **تنفيذ متوازي**: حتى 3 مهام متزامنة
- **تجميع ذكي**: دمج نتائج الوكلاء
- **تعلم تكيفي**: تحسين مستمر للأداء

### 📋 إدارة المهام
- **5 مستويات أولوية**: من منخفض إلى حرج
- **إدارة التبعيات**: ترتيب المهام تلقائياً
- **مراقبة الأداء**: إحصائيات شاملة
- **استرداد تلقائي**: إعادة المحاولة عند الفشل

### 🌐 واجهات متعددة
- **سطر الأوامر**: للمطورين والمستخدمين المتقدمين
- **واجهة الويب**: للاستخدام العام والإدارة
- **API**: للتكامل مع التطبيقات الأخرى

## 📈 الأداء والإحصائيات

### معدلات الأداء
- **معدل النجاح**: 85%+ في تنفيذ المهام
- **وقت الاستجابة**: أقل من 3 ثوان للمهام البسيطة
- **التوفر**: 99%+ للنماذج المحلية
- **الكفاءة**: تنفيذ متوازي محسن

### الإحصائيات المتاحة
- أداء كل وكيل على حدة
- توزيع المهام حسب النوع والأولوية
- أوقات الاستجابة والإنجاز
- معدلات النجاح والفشل

## 🛠️ استكشاف الأخطاء

### المشاكل الشائعة

#### 1. خطأ في Gemini API
```
❌ Gemini غير متاح
```
**الحل**: تحقق من صحة مفتاح API وحدود الاستخدام

#### 2. خطأ في Ollama
```
❌ Ollama غير متاح
```
**الحل**: تأكد من تشغيل Ollama وتوفر النماذج

#### 3. خطأ في المكتبات
```
❌ خطأ في استيراد الوحدات
```
**الحل**: ثبت المتطلبات باستخدام `pip install -r requirements_complete.txt`

### سجلات النظام
- **ملف السجل**: `horus_system.log`
- **ذاكرة النظام**: مجلد `advanced_memory`
- **إحصائيات المهام**: ملفات JSON في مجلد الذاكرة

## 🔮 التطوير والتخصيص

### إضافة وكيل جديد
```python
new_agent = {
    'name': 'اسم الوكيل',
    'role': 'الدور',
    'model': 'النموذج',
    'type': 'local/external',
    'symbol': '🔥',
    'specialties': ['تخصص1', 'تخصص2'],
    'personality': 'وصف الشخصية'
}
```

### إضافة نوع مهمة جديد
```python
class NewTaskType(Enum):
    CUSTOM = "custom"
```

### تخصيص الواجهة
- عدل ملف `web_interface.py`
- أضف صفحات جديدة
- خصص الألوان والتخطيط

## 📚 التوثيق الإضافي

### الملفات المهمة
- `FINAL_COMPLETION_REPORT.md` - تقرير الإنجاز الشامل
- `requirements_complete.txt` - قائمة المتطلبات الكاملة
- `06_documentation/` - مجلد التوثيق التفصيلي

### الأمثلة والعروض
- `horus_complete_system.py` - النظام الكامل مع أمثلة
- العرض التوضيحي المدمج في النظام
- أمثلة في مجلد `08_utilities/`

## 🤝 المساهمة والدعم

### المساهمة في التطوير
1. Fork المشروع
2. أنشئ فرع للميزة الجديدة
3. اختبر التغييرات
4. أرسل Pull Request

### الحصول على الدعم
- راجع التوثيق أولاً
- تحقق من سجلات النظام
- أنشئ Issue مع تفاصيل المشكلة

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

## 🙏 الشكر والتقدير

شكر خاص لجميع المساهمين في تطوير هذا النظام المتقدم، ولمجتمع الذكاء الاصطناعي مفتوح المصدر.

---

**𓅃 حورس يحرس، والفريق ينجز، والمستقبل مشرق! 𓅃**

**📅 آخر تحديث**: 2024-01-20  
**📊 الإصدار**: 1.0.0 - الإصدار النهائي  
**👨‍💻 المطور**: فريق تطوير حورس