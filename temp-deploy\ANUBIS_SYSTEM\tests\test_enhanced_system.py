#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 اختبار النظام المحسن لأنوبيس
Anubis Enhanced System Test

اختبار شامل للنظام المحسن مع قاعدة البيانات والـ API الجديد
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime
from typing import Dict, Any, List

class AnubisEnhancedTester:
    """فئة اختبار النظام المحسن"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = None
        self.test_results = []
        
    async def __aenter__(self):
        """بدء جلسة HTTP"""
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """إنهاء جلسة HTTP"""
        if self.session:
            await self.session.close()
    
    def log_test(self, test_name: str, success: bool, details: str = "", duration: float = 0):
        """تسجيل نتيجة الاختبار"""
        result = {
            "test_name": test_name,
            "success": success,
            "details": details,
            "duration": duration,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status = "✅" if success else "❌"
        print(f"{status} {test_name}: {details} ({duration:.2f}s)")
    
    async def test_basic_endpoints(self):
        """اختبار النقاط الأساسية"""
        print("\n🔍 اختبار النقاط الأساسية...")
        
        endpoints = [
            ("/", "الصفحة الرئيسية"),
            ("/health", "فحص الصحة"),
            ("/status", "حالة النظام"),
            ("/docs", "توثيق API"),
            ("/api/v1/info", "معلومات API v1"),
            ("/api/v1/models", "قائمة النماذج")
        ]
        
        for endpoint, description in endpoints:
            start_time = time.time()
            try:
                async with self.session.get(f"{self.base_url}{endpoint}") as response:
                    duration = time.time() - start_time
                    
                    if response.status == 200:
                        self.log_test(
                            f"GET {endpoint}",
                            True,
                            f"{description} - Status: {response.status}",
                            duration
                        )
                    else:
                        self.log_test(
                            f"GET {endpoint}",
                            False,
                            f"Status: {response.status}",
                            duration
                        )
                        
            except Exception as e:
                duration = time.time() - start_time
                self.log_test(
                    f"GET {endpoint}",
                    False,
                    f"خطأ: {str(e)}",
                    duration
                )
    
    async def test_database_endpoints(self):
        """اختبار نقاط قاعدة البيانات"""
        print("\n🗄️ اختبار نقاط قاعدة البيانات...")
        
        # فحص حالة قاعدة البيانات
        start_time = time.time()
        try:
            async with self.session.get(f"{self.base_url}/api/v2/database/status") as response:
                duration = time.time() - start_time
                
                if response.status == 200:
                    data = await response.json()
                    self.log_test(
                        "Database Status",
                        True,
                        f"MySQL: {data.get('databases', {}).get('mysql', {}).get('connected', False)}, "
                        f"SQLite: {data.get('databases', {}).get('sqlite', {}).get('connected', False)}",
                        duration
                    )
                else:
                    self.log_test(
                        "Database Status",
                        False,
                        f"Status: {response.status}",
                        duration
                    )
                    
        except Exception as e:
            duration = time.time() - start_time
            self.log_test(
                "Database Status",
                False,
                f"خطأ: {str(e)}",
                duration
            )
        
        # تهيئة قاعدة البيانات
        for db_type in ["mysql", "sqlite"]:
            start_time = time.time()
            try:
                async with self.session.post(
                    f"{self.base_url}/api/v2/database/initialize",
                    params={"database": db_type}
                ) as response:
                    duration = time.time() - start_time
                    
                    if response.status == 200:
                        self.log_test(
                            f"Initialize {db_type.upper()}",
                            True,
                            "تم التهيئة بنجاح",
                            duration
                        )
                    else:
                        self.log_test(
                            f"Initialize {db_type.upper()}",
                            False,
                            f"Status: {response.status}",
                            duration
                        )
                        
            except Exception as e:
                duration = time.time() - start_time
                self.log_test(
                    f"Initialize {db_type.upper()}",
                    False,
                    f"خطأ: {str(e)}",
                    duration
                )
    
    async def test_user_management(self):
        """اختبار إدارة المستخدمين"""
        print("\n👥 اختبار إدارة المستخدمين...")
        
        # تسجيل مستخدم جديد
        test_user = {
            "username": f"test_user_{int(time.time())}",
            "email": f"test_{int(time.time())}@example.com",
            "password": "[TEST_PASSWORD]"
        }
        
        start_time = time.time()
        try:
            async with self.session.post(
                f"{self.base_url}/api/v2/users/register",
                json=test_user
            ) as response:
                duration = time.time() - start_time
                
                if response.status == 200:
                    data = await response.json()
                    self.log_test(
                        "User Registration",
                        True,
                        f"مستخدم جديد: {data.get('username')}",
                        duration
                    )
                    
                    # حفظ معرف المستخدم للاختبارات اللاحقة
                    self.test_user_id = 1  # افتراضي
                    
                else:
                    self.log_test(
                        "User Registration",
                        False,
                        f"Status: {response.status}",
                        duration
                    )
                    
        except Exception as e:
            duration = time.time() - start_time
            self.log_test(
                "User Registration",
                False,
                f"خطأ: {str(e)}",
                duration
            )
        
        # جلب قائمة المستخدمين
        start_time = time.time()
        try:
            async with self.session.get(f"{self.base_url}/api/v2/users") as response:
                duration = time.time() - start_time
                
                if response.status == 200:
                    data = await response.json()
                    user_count = data.get('total_count', 0)
                    self.log_test(
                        "List Users",
                        True,
                        f"عدد المستخدمين: {user_count}",
                        duration
                    )
                else:
                    self.log_test(
                        "List Users",
                        False,
                        f"Status: {response.status}",
                        duration
                    )
                    
        except Exception as e:
            duration = time.time() - start_time
            self.log_test(
                "List Users",
                False,
                f"خطأ: {str(e)}",
                duration
            )
    
    async def test_ai_endpoints(self):
        """اختبار نقاط الذكاء الاصطناعي"""
        print("\n🤖 اختبار نقاط الذكاء الاصطناعي...")
        
        # طلب إنتاج نص
        ai_request = {
            "prompt": "مرحبا، كيف يمكنني مساعدتك اليوم؟",
            "model": "gpt-3.5-turbo",
            "max_tokens": 100,
            "temperature": 0.7,
            "user_id": 1
        }
        
        start_time = time.time()
        try:
            async with self.session.post(
                f"{self.base_url}/api/v2/ai/generate",
                json=ai_request
            ) as response:
                duration = time.time() - start_time
                
                if response.status == 200:
                    data = await response.json()
                    tokens_used = data.get('tokens_used', 0)
                    self.log_test(
                        "AI Text Generation",
                        True,
                        f"تم إنتاج النص - الرموز المستخدمة: {tokens_used}",
                        duration
                    )
                else:
                    self.log_test(
                        "AI Text Generation",
                        False,
                        f"Status: {response.status}",
                        duration
                    )
                    
        except Exception as e:
            duration = time.time() - start_time
            self.log_test(
                "AI Text Generation",
                False,
                f"خطأ: {str(e)}",
                duration
            )
        
        # جلب جلسات الذكاء الاصطناعي
        start_time = time.time()
        try:
            async with self.session.get(f"{self.base_url}/api/v2/ai/sessions") as response:
                duration = time.time() - start_time
                
                if response.status == 200:
                    data = await response.json()
                    session_count = data.get('count', 0)
                    self.log_test(
                        "AI Sessions List",
                        True,
                        f"عدد الجلسات: {session_count}",
                        duration
                    )
                else:
                    self.log_test(
                        "AI Sessions List",
                        False,
                        f"Status: {response.status}",
                        duration
                    )
                    
        except Exception as e:
            duration = time.time() - start_time
            self.log_test(
                "AI Sessions List",
                False,
                f"خطأ: {str(e)}",
                duration
            )
    
    async def test_system_endpoints(self):
        """اختبار نقاط النظام"""
        print("\n⚙️ اختبار نقاط النظام...")
        
        # إحصائيات النظام
        start_time = time.time()
        try:
            async with self.session.get(f"{self.base_url}/api/v2/system/stats") as response:
                duration = time.time() - start_time
                
                if response.status == 200:
                    data = await response.json()
                    stats = data.get('stats', {})
                    self.log_test(
                        "System Stats",
                        True,
                        f"المستخدمين: {stats.get('users', {}).get('total_users', 0)}, "
                        f"جلسات AI: {stats.get('ai_sessions', {}).get('total_sessions', 0)}",
                        duration
                    )
                else:
                    self.log_test(
                        "System Stats",
                        False,
                        f"Status: {response.status}",
                        duration
                    )
                    
        except Exception as e:
            duration = time.time() - start_time
            self.log_test(
                "System Stats",
                False,
                f"خطأ: {str(e)}",
                duration
            )
        
        # فحص صحة مفصل
        start_time = time.time()
        try:
            async with self.session.get(f"{self.base_url}/api/v2/monitoring/health") as response:
                duration = time.time() - start_time
                
                if response.status == 200:
                    data = await response.json()
                    overall_status = data.get('overall', 'unknown')
                    self.log_test(
                        "Detailed Health Check",
                        True,
                        f"الحالة العامة: {overall_status}",
                        duration
                    )
                else:
                    self.log_test(
                        "Detailed Health Check",
                        False,
                        f"Status: {response.status}",
                        duration
                    )
                    
        except Exception as e:
            duration = time.time() - start_time
            self.log_test(
                "Detailed Health Check",
                False,
                f"خطأ: {str(e)}",
                duration
            )
    
    async def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🏺 بدء اختبار النظام المحسن لأنوبيس")
        print("=" * 60)
        
        start_time = time.time()
        
        # تشغيل الاختبارات
        await self.test_basic_endpoints()
        await self.test_database_endpoints()
        await self.test_user_management()
        await self.test_ai_endpoints()
        await self.test_system_endpoints()
        
        total_duration = time.time() - start_time
        
        # تلخيص النتائج
        print("\n" + "=" * 60)
        print("📊 ملخص نتائج الاختبار")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - successful_tests
        
        print(f"إجمالي الاختبارات: {total_tests}")
        print(f"✅ نجح: {successful_tests}")
        print(f"❌ فشل: {failed_tests}")
        print(f"📈 معدل النجاح: {(successful_tests/total_tests)*100:.1f}%")
        print(f"⏱️ إجمالي الوقت: {total_duration:.2f} ثانية")
        
        # حفظ النتائج
        report = {
            "test_summary": {
                "total_tests": total_tests,
                "successful_tests": successful_tests,
                "failed_tests": failed_tests,
                "success_rate": (successful_tests/total_tests)*100,
                "total_duration": total_duration,
                "timestamp": datetime.now().isoformat()
            },
            "test_results": self.test_results
        }
        
        with open(f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json", 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n📝 تم حفظ تقرير مفصل في: test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        
        return successful_tests == total_tests

async def main():
    """الدالة الرئيسية"""
    async with AnubisEnhancedTester() as tester:
        success = await tester.run_all_tests()
        
        if success:
            print("\n🎉 جميع الاختبارات نجحت!")
            return 0
        else:
            print("\n⚠️ بعض الاختبارات فشلت!")
            return 1

if __name__ == "__main__":
    import sys
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
